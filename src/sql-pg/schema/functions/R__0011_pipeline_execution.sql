-- Flyway will re-run this script if ${changeReason} is updated
drop function if exists log.start_processing;
create function log.start_processing(pipeline text, pipeline_run_id text)
returns integer
as
$$
declare
    result integer;
begin
    insert into log.pipeline_execution(pipeline_name, run_id)
    values (pipeline, pipeline_run_id)
    on conflict on constraint pipeline_execution_uq
    do update set
        started = now() at time zone 'utc',
        finished = null,
        error    = null
    returning id into result;

    return result;
end;
$$
language plpgsql volatile;

drop function if exists log.get_pipeline_execution_id;
create function log.get_pipeline_execution_id(pipeline text, pipeline_run_id text)
returns integer
as
$$
select
    id
from
    log.pipeline_execution
where
    pipeline_name = pipeline
and run_id = pipeline_run_id;
$$
language sql stable;

drop function if exists log.finish_processing;
create or replace function log.finish_processing(execution_id int, error_message text = null)
returns integer
as
$$
declare
    result integer;
begin
    update log.pipeline_execution
    set finished = now() at time zone 'utc',
        error    = error_message
    where
        id = execution_id
    returning id into result;

    return result;
end;
$$
language plpgsql volatile;

drop function if exists log.start_processing_step;
create function log.start_processing_step(execution_id int, execution_step text)
returns integer
as
$$
declare
    result integer;
begin
    insert into log.pipeline_execution_step(pipeline_execution_id, step_name)
    values (execution_id, execution_step)
    on conflict on constraint pipeline_execution_step_uq
    do update set
        started = now() at time zone 'utc',
        finished = null,
        error    = null
    returning id into result;

    return result;
end;
$$
language plpgsql volatile;

drop function if exists log.finish_processing_step;
create function log.finish_processing_step(execution_step_id int, error_message text = null)
returns integer
as
$$
declare
    result integer;
begin
    update log.pipeline_execution_step
    set finished = now() at time zone 'utc',
        error    = error_message
    where
        id = execution_step_id
    returning id into result;

    return result;
end;
$$
language plpgsql volatile;


drop function if exists log.get_pipeline_step_execution_id;
create function log.get_pipeline_step_execution_id(
    pipeline text,
    pipeline_run_id text,
    execution_step text
)
returns integer
as
$$
select
    pes.id as id
from
    log.pipeline_execution_step pes
inner join
    log.pipeline_execution pe
    on pes.pipeline_execution_id = pe.id
where
    pe.pipeline_name = pipeline
and pe.run_id = pipeline_run_id
and pes.step_name = execution_step;
$$
language sql stable;

-- test: insert test pipeline execution for local/staging development
do
$test$
begin
    if ('${runTest}' = 'true') then -- noqa
        perform log.start_processing('test-pipeline', 'test-run');
    end if;
end;
$test$;

/*
-- Test cases
select * from log.pipeline_execution;
select * from log.pipeline_execution_step;

select log.start_processing('test-pipeline', 'test-run');
select log.get_pipeline_execution_id('test-pipeline', 'test-run');

select log.start_processing_step(1, 'step 1');
select log.start_processing_step(1, 'step 2');
select log.start_processing_step(1, 'step 3');

select log.finish_processing_step(1);
select log.finish_processing_step(2, 'error1');
select log.finish_processing_step(3, 'error2');

select log.finish_processing(1, 'error1 + error2');

select *
from
    log.pipeline_execution
order by
    id;

select *
from
    log.pipeline_execution_step
order by
    id;
*/
