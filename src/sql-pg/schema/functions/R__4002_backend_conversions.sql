drop function if exists analytics.select_backend_conversions;
/**
    Helper function to get backend conversions.
    Required by Adtriba True Consent Mode export.

    Based on:
    - https://gitlab.com/refurbed/analytics-and-ds/analytics-pipelines/-/issues/123#note_2007705243

    Dependent on:
    - `R__3005_analytics_order_item_offers.sql` script - updated on 2025-05-29
*/
create function analytics.select_backend_conversions(
    p_start_date date default current_date - interval '1 day', p_end_date date default current_date
) returns table
(
    date date,
    costumer_type text,
    device_type text,
    total_conversions bigint,
    total_revenue numeric(15, 2),
    brand text,
    country text,
    conversion_name text,
    currency text
)
language plpgsql as
$$
begin
    return query
    select
        oio.paid_at::date as "date",
        case when cc.order_number != 1 then 'returning' else 'new' end as costumer_type,
        'web' as device_type,
        count(distinct oio.order_id) as total_conversions,
        sum(re.data_layer_revenue) as total_revenue,
        'refurbed' as brand,
        upper(oio.country::text) as country,
        'Transaction' as conversion_name,
        'EUR' as currency
    from
        analytics.order_item_offers as oio
    inner join
        analytics.order_data_layer_revenue as re
    on  oio.id = re.order_item_id
    left join
        analytics.connected_customers_dev as cc
    on  oio.order_id = cc.order_id
    where
        oio.paid_at between p_start_date and p_end_date
    group by
        "date",
        costumer_type,
        oio.country;
end;
$$ immutable parallel safe;

-- test me!
do
$test$
begin
    if ('${runTest}' = 'true') then -- noqa
        perform * from analytics.select_backend_conversions(date('2024-08-01'), date('2024-08-03')) as f limit 10;
    end if;
end;
$test$;
