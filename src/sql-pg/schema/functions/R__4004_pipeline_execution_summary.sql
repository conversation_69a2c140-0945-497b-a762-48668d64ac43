drop function if exists log.pipeline_execution_summary;
create function log.pipeline_execution_summary(pipeline_run_id text = null)
returns table
(
    id int,
    pipeline_name text,
    run_id text,
    started timestamp with time zone,
    finished timestamp with time zone,
    duration varchar(10),
    seconds numeric,
    succeeded boolean,
    error text
)
as
$$
select
    pe.id,
    pe.pipeline_name,
    pe.run_id,
    pe.started,
    pe.finished,
    log.calculate_duration(pe.started, pe.finished)     as duration,
    log.calculate_diff_seconds(pe.started, pe.finished) as seconds,
    pe.succeeded,
    pe.error
from
    log.pipeline_execution pe
where
    pe.run_id = pipeline_run_id
 or pipeline_run_id is null
$$ language sql;

/*
select * from log.pipeline_execution_summary();
*/

drop function if exists log.pipeline_execution_step_summary;
create function log.pipeline_execution_step_summary(pipeline_run_id text default null)
returns table
(
    id int,
    pipeline_name text,
    run_id text,
    step_id int,
    step_name text,
    started timestamp with time zone,
    finished timestamp with time zone,
    duration varchar(10),
    seconds numeric,
    succeeded boolean,
    error text
)
as
$$
select
    pe.id as id,
    pe.pipeline_name,
    pe.run_id,
    pes.id as step_id,
    pes.step_name,
    pes.started as started,
    pes.finished as finished,
    log.calculate_duration(pes.started, pes.finished) as duration,
    log.calculate_diff_seconds(pes.started, pes.finished) as seconds,
    pes.succeeded,
    pes.error
from
    log.pipeline_execution pe
inner join
    log.pipeline_execution_step pes
on pe.id = pes.pipeline_execution_id
where
    pe.run_id = pipeline_run_id
 or pipeline_run_id is null
order by
    pes.started,
    pes.finished
$$
language sql;

/*
select * from log.pipeline_execution_step_summary();
*/


drop function if exists log.get_execution_summary;
create function log.get_execution_summary(pipeline_run_id text = null)
returns table
(
    metric_name text,
    metric_value text
)
as
$$
begin
    if pipeline_run_id is null then
        select pe.run_id
        into pipeline_run_id
        from log.pipeline_execution pe
        order by pe.started desc
        limit 1;
    end if;

    drop table if exists tmp_last_30_days_successful_executions;
    create temporary table tmp_last_30_days_successful_executions as
    select avg(seconds) as avg_seconds
    from (select round(extract(epoch from pe.finished - pe.started))::numeric as seconds
          from log.pipeline_execution pe
          where pe.finished > now() - interval '30 days'
            and pe.run_id <> pipeline_run_id) as t;


    return query select cast(s.metric_name as text),
                        cast(s.metric_value as text)
                 from (select *,
                              log.calculate_kpi(seconds, avg_seconds)           as kpi,
                              log.calculate_kpi_indicator(seconds, avg_seconds) as kpi_indicator
                       from (select *,
                                    log.calculate_duration(started, finished)              as duration,
                                    round(extract(epoch from finished - started))::numeric as seconds
                             from log.pipeline_execution
                             where run_id = pipeline_run_id) loe
                                cross join tmp_last_30_days_successful_executions) els
                          inner join lateral (values ('Pipeline name', els.pipeline_name::text),
                                                     ('Run ID', els.run_id::text),
                                                     ('Start date [UTC]', cast(els.started as varchar(19))),
                                                     ('Finish date [UTC]', cast(els.finished as varchar(19))),
                                                     ('Total duration [hh:mm:ss]', els.duration::text),
                                                     ('Duration KPI [last 30 days]',
                                                      concat(kpi_indicator::char(12), ('[' ||
                                                                                       log.calculate_duration_from_seconds(els.avg_seconds) ||
                                                                                       ']')::char(10)))) s(metric_name, metric_value)
                                     on true;

end
$$ language plpgsql volatile
parallel safe;

/*
select *
from
    log.get_execution_summary();
 */

drop function if exists log.get_execution_step_summary;
create function log.get_execution_step_summary(pipeline_run_id text = null)
returns table
(
    step_name text,
    last_30_days_kpi_duration text
)
as
$$
declare
    pipeline record;
begin
    if pipeline_run_id is null then
        select pe.run_id
        into pipeline_run_id
        from log.pipeline_execution pe
        order by pe.started desc
        limit 1;
    end if;

    select pe.pipeline_name,
           pe.run_id
    into pipeline
    from log.pipeline_execution pe
    where pe.run_id = pipeline_run_id;


    drop table if exists tmp_last_30_days_successful_executions;
    create temporary table tmp_last_30_days_successful_executions as
    select t.pipeline_name,
           t.step_name,
           avg(t.seconds) as avg_seconds
    from (select pe.pipeline_name,
                 pse.step_name,
                 round(extract(epoch from pse.finished - pse.started))::numeric as seconds
          from log.pipeline_execution pe
                   inner join
               log.pipeline_execution_step pse
               on pe.id = pse.pipeline_execution_id
          where pse.finished > now() - interval '30 days'
            and pe.pipeline_name = pipeline.pipeline_name
            and pe.run_id <> pipeline_run_id -- do not include the current run
            and pse.error is null) as t
    group by t.pipeline_name,
             t.step_name;

    return query select summary.step_name                                                                      as step_name,
                        concat(log.calculate_kpi_indicator(summary.seconds, coalesce(last30.avg_seconds, 0))::char(12),
                               ('[' || log.calculate_duration_from_seconds(summary.seconds) || ']')::char(10)) as last_30_days_kpi_duration
                 from log.pipeline_execution_step_summary(pipeline_run_id) as summary
                          left join
                      tmp_last_30_days_successful_executions last30
                      on summary.pipeline_name = last30.pipeline_name
                          and summary.step_name = last30.step_name
                 order by summary.started,
                          summary.finished;

end
$$ language plpgsql volatile
parallel safe;

/*
select *
from
    log.get_execution_step_summary();
 */

drop function if exists log.get_execution_summary_details;
create function log.get_execution_summary_details(pipeline_run_id text = null)
returns table
(
    metric_name text,
    metric_value text
)
as
$$
select
    s.metric_name,
    s.metric_value
from
    log.get_execution_summary(pipeline_run_id) s
union all
select
    'Details KPI [duration]:',
    ''
union all
select
    '  - ' || step_name,
    last_30_days_kpi_duration
from
    log.get_execution_step_summary(pipeline_run_id)
$$ language sql;

-- test: select * from log.get_execution_summary_details() limit 10;
do
$test$
    begin
        if ('${runTest}' = 'true') then -- noqa
            perform * from log.get_execution_summary_details() limit 10;
        end if;
    end;
$test$;
