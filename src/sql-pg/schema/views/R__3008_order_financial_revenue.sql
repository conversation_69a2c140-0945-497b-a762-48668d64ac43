-- Flyway will re-run this script if it is changed or ${changeReason} is updated
drop view if exists analytics.order_financial_revenue cascade;
create or replace view analytics.order_financial_revenue
/**
  Gets financial revenue and gmv in EUR.

  Used by the following functions:
  - `analytics.select_revenue_countries`

  Extended with columns from `etl_internal.orders` BQ dataset:
    - addon_revenue
    - service_fee_revenue_eur
    - discount_eur
*/
as
select
    order_item_offers.id as order_item_id,
    order_item_offers.order_id,
    -- Replacement for etl_internal.orders.revenue_eur
    round(cast(coalesce((case
        when (
            merchants.is_addon_support
        )
            then (case
                when
                    order_items.net
                    then (
                        order_items.price
                        * (1 / exchange_rates.exchange_rate)
                        / order_items.presentment_exchange_rate_modifier
                    )
                else (
                    order_items.price
                    * (1 / (exchange_rates.exchange_rate))
                    / order_items.presentment_exchange_rate_modifier
                )
                / (1 + (order_items.vat / 100))
            end) + (case
                when
                    order_items.net
                    then (
                        order_items.shipping_costs
                        * (1 / exchange_rates.exchange_rate)
                        / order_items.presentment_exchange_rate_modifier
                    )
                else (
                    order_items.shipping_costs
                    * (1 / exchange_rates.exchange_rate)
                    / order_items.presentment_exchange_rate_modifier
                )
                / (1 + (order_items.vat / 100))
            end)
        when
            order_items.type = 'offer'
            then (
                (
                    (
                        order_items.presentment_flex_price_gross
                        * ((1 / exchange_rates.exchange_rate))
                        / order_items.presentment_exchange_rate_modifier
                    )
                    + (
                        order_items.presentment_shipping_costs_gross
                        * ((1 / exchange_rates.exchange_rate))
                        / order_items.presentment_exchange_rate_modifier
                    )
                )
                * (order_items.commission_pc)
            )
            + (
                (
                    (
                        order_items.presentment_price_gross
                        * ((1 / exchange_rates.exchange_rate))
                        / order_items.presentment_exchange_rate_modifier
                    )
                    - (
                        order_items.presentment_flex_price_gross
                        * ((1 / exchange_rates.exchange_rate))
                        / order_items.presentment_exchange_rate_modifier
                    )
                )
                * 0.7
            )
            + (
                (
                    (
                        order_items.presentment_flex_price_gross
                        * (1 / exchange_rates.exchange_rate)
                        / order_items.presentment_exchange_rate_modifier
                    )
                    + (
                        order_items.presentment_shipping_costs_gross
                        * ((1 / exchange_rates.exchange_rate))
                        / order_items.presentment_exchange_rate_modifier
                    )
                )
                * order_items.payout_commission_pc
            )
            + (
                (
                    (
                        order_items.presentment_flex_price_gross
                        * ((1 / exchange_rates.exchange_rate))
                        / order_items.presentment_exchange_rate_modifier
                    )
                    + (
                        order_items.presentment_shipping_costs_gross
                        * ((1 / exchange_rates.exchange_rate))
                        / order_items.presentment_exchange_rate_modifier
                    )
                )
                * order_items.payment_commission_pc
            )
        when order_items.type = 'addon' then (case
            when
                order_items.net
                then (
                    order_items.price
                    * ((1 / exchange_rates.exchange_rate))
                    / order_items.presentment_exchange_rate_modifier
                )
            else (
                order_items.price * (1 / exchange_rates.exchange_rate) / order_items.presentment_exchange_rate_modifier
            )
            / (1 + (order_items.vat / 100))
        end) + (case
            when
                order_items.net
                then (
                    order_items.shipping_costs
                    * (1 / exchange_rates.exchange_rate)
                    / order_items.presentment_exchange_rate_modifier
                )
            else (
                order_items.shipping_costs
                * (1 / exchange_rates.exchange_rate)
                / order_items.presentment_exchange_rate_modifier
            )
            / (1 + (order_items.vat / 100))
        end)
    end), 0) as numeric), 2) as revenue,
    -- Replacement for etl_internal.orders.addon_revenue_eur:
    round(cast(coalesce(case when (order_items.type = 'addon') or (
                merchants.is_addon_support
            )
            then (case
                when
                    order_items.net
                    then (
                        order_items.price
                        * (1 / exchange_rates.exchange_rate)
                        / order_items.presentment_exchange_rate_modifier
                    )
                else (
                    order_items.price
                    * (1 / exchange_rates.exchange_rate)
                    / order_items.presentment_exchange_rate_modifier
                )
                / (1 + (order_items.vat / 100))
            end) + (case
                when
                    order_items.net
                    then (
                        order_items.shipping_costs
                        * (1 / exchange_rates.exchange_rate)
                        / order_items.presentment_exchange_rate_modifier
                    )
                else (
                    order_items.shipping_costs
                    * (1 / exchange_rates.exchange_rate)
                    / order_items.presentment_exchange_rate_modifier
                )
                / (1 + (order_items.vat / 100))
            end)
            - (
                case
                    when
                        (order_items.addon_details ->> 'type') = 'extended-warranty'
                        then (
                            coalesce(cast((order_items.addon_details ->> 'cost') as numeric), 0)
                            * (1 / exchange_rates.exchange_rate)
                            / order_items.presentment_exchange_rate_modifier
                        )
                    else 0
                end
            )
        else 0
    end, 0) as numeric), 2) as addon_revenue,
    -- Same as etl_internal.orders.gmv_eur:
    round(cast(coalesce(
        (
            (
                order_items.presentment_price_gross
                * (1 / exchange_rates.exchange_rate)
                / order_items.presentment_exchange_rate_modifier
            )
            + (
                order_items.presentment_shipping_costs_gross
                * (1 / exchange_rates.exchange_rate)
                / order_items.presentment_exchange_rate_modifier
            )
        ),
        0
    ) as numeric), 2) as gmv,
    order_items.discount
    * (
        (1 / exchange_rates.exchange_rate)
    )
    / order_items.presentment_exchange_rate_modifier as discount_eur,
    case
        when order_items.type = 'service-fee'
            then (
                case
                    when order_items.net
                        then (
                            order_items.price
                            * (1 / exchange_rates.exchange_rate)
                            / order_items.presentment_exchange_rate_modifier
                        )
                    else (
                        order_items.price
                        * (1 / exchange_rates.exchange_rate)
                        / order_items.presentment_exchange_rate_modifier
                    )
                    / (1 + (order_items.vat / 100))
                end
            )
        else 0
    end as service_fee_revenue_eur
from
    public.order_item_offers as order_item_offers
inner join
    -- analytics.order_item_exchange_rate is already filtered to
    -- order_item_offers.state in ('released', 'released.failed')
    analytics.order_item_exchange_rate as exchange_rates
on order_item_offers.id = exchange_rates.order_item_id
inner join
    public.order_items as order_items
on order_item_offers.id = order_items.id
left join
    -- must be left join as there can be no merchant for given order_item_offers!
    public.merchants as merchants
on order_item_offers.merchant_id = merchants.id;

-- test: analytics.order_financial_revenue
/*
drop table if exists temp_order_financial_revenue;
create temp table temp_order_financial_revenue
as
select * from  analytics.order_financial_revenue;

-- 418 MB
select pg_size_pretty(pg_relation_size('temp_order_financial_revenue'));
*/
do
$test$
begin
    if ('${runTest}' = 'true') then -- noqa
        perform * from analytics.order_financial_revenue limit 10;
    end if;
end;
$test$;
