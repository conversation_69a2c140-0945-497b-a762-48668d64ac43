-- Flyway will re-run this script if it is changed or ${changeReason} is updated
drop view if exists export_etl.v_partners;
create or replace view export_etl.v_partners
as
select
    id,
    name::text,
    slug::text
from public.partners;

-- test me!
do
$test$
begin
    if ('${runTest}' = 'true') then -- noqa
        -- size: 8192 bytes
        -- select pg_size_pretty(pg_relation_size('public.partners')) as partners_size;
        perform * from export_etl.v_partners limit 10;
    end if;
end;
$test$;
