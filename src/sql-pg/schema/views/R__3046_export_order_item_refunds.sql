-- Flyway will re-run this script if it is changed or ${changeReason} is updated
drop view if exists export_etl.v_order_item_refunds;
create or replace view export_etl.v_order_item_refunds
as
select
    id,
    order_item_id,
    order_refund_id,
    refunded::numeric(14, 4)
from
    public.order_item_refunds;

-- test me!
do
$test$
begin
    if ('${runTest}' = 'true') then
        -- size: 35 MB
        -- select pg_size_pretty(pg_relation_size('public.order_item_refunds')) as order_item_refunds_size;
        perform * from export_etl.v_order_item_refunds limit 10;
    end if;
end;
$test$;
