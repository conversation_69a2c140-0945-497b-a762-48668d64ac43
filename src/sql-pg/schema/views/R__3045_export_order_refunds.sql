-- Flyway will re-run this script if it is changed or ${changeReason} is updated
drop view if exists export_etl.v_order_refunds;
create or replace view export_etl.v_order_refunds
as
select
    id,
    created_at,
    updated_at,
    null::timestamp with time zone as deleted_at,
    status::text,
    type::text,
    order_id,
    merchant_id,
    refund_handle::text,
    refunded::numeric(14, 4)
from
    public.order_refunds;

-- test me!
do
$test$
begin
    if ('${runTest}' = 'true') then -- noqa
        -- size: 82 MB
        -- select pg_size_pretty(pg_relation_size('public.order_refunds')) as order_refunds_size;
        perform * from export_etl.v_order_refunds limit 10;
    end if;
end;
$test$;
