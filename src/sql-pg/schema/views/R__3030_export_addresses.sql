-- Flyway will re-run this script if it is changed or ${changeReason} is updated
drop view if exists export_etl.v_addresses;
create or replace view export_etl.v_addresses
as
select
    id,
    valid_from::timestamp with time zone,
    -- `infinity` is not supported in BigQuery!
    analytics.infinity_to_timestamp(valid_to) as valid_to,

    -- Mandatory address fields
    owner_id,
    first_name::text,
    family_name::text,
    country::text,
    post_code::text,
    town::text,
    street_name::text,
    house_no::text,
    phone::text,

    -- Optional address fields
    address_type::text,
    supplement::text,
    male,
    company::text,
    vatin::text,
    personal_vatin::text
from public.addresses;

-- test me!
do
$test$
begin
    if ('${runTest}' = 'true') then -- noqa
        -- size: 3399 MB
        -- select pg_size_pretty(pg_relation_size('public.addresses')) as addresses_size;
        perform * from export_etl.v_addresses where vatin is not null limit 10;
    end if;
end;
$test$;
