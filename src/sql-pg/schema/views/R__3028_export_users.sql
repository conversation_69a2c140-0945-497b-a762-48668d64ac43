-- Flyway will re-run this script if it is changed or ${changeReason} is updated
drop view if exists export_etl.v_users;
create or replace view export_etl.v_users
as
select
    id,
    created_at,
    updated_at,
    deleted_at,

    email::text,
    first_name::text,
    family_name::text,
    type::text,
    language::text,
    merchant_id
from public.users;

-- test me!
do
$test$
begin
    if ('${runTest}' = 'true') then -- noqa
        -- size: 1122 MB
        -- select pg_size_pretty(pg_relation_size('public.users')) as users_size;
        perform * from export_etl.v_users limit 10;
    end if;
end;
$test$;
