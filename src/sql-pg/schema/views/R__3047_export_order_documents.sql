-- Flyway will re-run this script if it is changed or ${changeReason} is updated
drop view if exists export_etl.v_order_documents;
create or replace view export_etl.v_order_documents
as
select
    id,
    valid_from::timestamp with time zone,
    -- `infinity` is not supported in BigQuery!
    analytics.infinity_to_timestamp(valid_to) as valid_to,

    order_id,
    type::text,
    status::text,
    file_id::text,
    invoice_merchant_id
from
    public.order_documents;

-- test me!
do
$test$
begin
    if ('${runTest}' = 'true') then -- noqa
        -- size: 525 MB
        -- select pg_size_pretty(pg_relation_size('public.order_documents')) as order_documents_size;
        perform * from export_etl.v_order_documents limit 10;
    end if;
end;
$test$;
