-- Flyway will re-run this script if it is changed or ${changeReason} is updated
drop view if exists export_etl.v_order_item_details;
create or replace view export_etl.v_order_item_details
as
select
    oid.id,
    oid.valid_from::timestamp with time zone,
    -- `infinity` is not supported in BigQuery!
    analytics.infinity_to_timestamp(valid_to) as valid_to,
    oid.state::text,
    oid.order_item_id,
    oid.shipment_tracking_id,
    oid.item_identifier::text,
    oid.parcel_tracking_url::text,
    oid.return_initiated_at
from
    public.order_item_details as oid
-- limit extract to `order_item_offers` for last 6 months for PoC purposes:
where exists
    (
        select from public.order_item_offers as oio
        where oio.id = oid.order_item_id
    )
    and oid.valid_from >= '2024-04-01'::timestamp with time zone;

-- test me!
do
$test$
begin
    if ('${runTest}' = 'true') then -- noqa
        -- size: 3104 MB
        -- select pg_size_pretty(pg_relation_size('public.order_item_details')) as order_item_details_size;
        perform * from export_etl.v_order_item_details limit 10;
    end if;
end;
$test$;
