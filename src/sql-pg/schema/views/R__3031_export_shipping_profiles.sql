-- Flyway will re-run this script if it is changed or ${changeReason} is updated
drop view if exists export_etl.v_shipping_profiles;
create view export_etl.v_shipping_profiles
as
select
    -- PK: id, valid_from
    id,
    valid_from::timestamp with time zone,
    -- `infinity` is not supported in BigQuery!
    analytics.infinity_to_timestamp(valid_to) as valid_to,
    merchant_id,
    name::text,
    ships_from::text
from
    public.shipping_profiles;

-- test: select * from export_etl.v_shipping_profiles limit 5;
do
$test$
begin
    if ('${runTest}' = 'true') then -- noqa
        /*
        --Size: 2952 kB
        select pg_size_pretty(pg_relation_size('public.shipping_profiles')) as shipping_profiles_size;
        */
        perform * from export_etl.v_shipping_profiles limit 5;
    end if;
end;
$test$;
