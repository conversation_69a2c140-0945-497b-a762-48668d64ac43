-- Flyway will re-run this script if it is changed or ${changeReason} is updated
drop view if exists analytics.order_item_offers cascade;
create view analytics.order_item_offers as
select
    -- order_item_offers
    oio.id,
    oio.created_at,
    oio.updated_at,
    oio.order_id,
    oio.offer_id,
    oio.offer_valid_from,
    oio.type,
    oio.paid_at,
    oio.state,
    oio.country,
    oio.presentment_currency,
    oio.payment_provider,
    oio.valid_to,
    oio.instance_id,
    oio.merchant_id,
    oio.grading,
    oio.warranty,

    -- offer_properties: nullable
    op.battery_condition,
    op.is_new_battery
from
    public.order_item_offers as oio
left join
    analytics.offer_properties as op
on oio.offer_id = op.offer_id;

-- test: select * from analytics.order_item_offers where is_new_battery is not null limit 5;
do
$test$
begin
    if ('${runTest}' = 'true') then -- noqa
        perform * from analytics.order_item_offers limit 5;
    end if;
end;
$test$;
