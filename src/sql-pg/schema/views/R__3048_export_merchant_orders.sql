-- Flyway will re-run this script if it is changed or ${changeReason} is updated
drop view if exists export_etl.v_merchant_orders;
create or replace view export_etl.v_merchant_orders
as
select
    order_id,
    merchant_id,
    total_charged::numeric(14, 4),
    total_paid::numeric(14, 4),
    total_refunded::numeric(14, 4),
    total_discount::numeric(14, 4)
from public.merchant_orders as mo
-- limit extract to `order_item_offers` for PoC purposes:
where exists
    (
        select from public.order_item_offers as oio
        where oio.order_id = mo.order_id
    );


-- test me!
do
$test$
begin
    if ('${runTest}' = 'true') then -- noqa
        -- size: 1329 MB
        -- select pg_size_pretty(pg_relation_size('public.merchant_orders')) as merchant_orders_size;
        perform * from export_etl.v_merchant_orders limit 10;
    end if;
end;
$test$;
