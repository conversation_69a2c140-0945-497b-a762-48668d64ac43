-- Flyway will re-run this script if it is changed or ${changeReason} is updated
drop view if exists export_etl.v_shipping_profile_destinations;
create view export_etl.v_shipping_profile_destinations
as
select
    -- PK: id, valid_from
    id,
    valid_from::timestamp with time zone,
    analytics.infinity_to_timestamp(valid_to) as valid_to,
    shipping_profile_id,
    analytics.infinity_to_timestamp(shipping_profile_valid_to) as shipping_profile_valid_to,
    country::text,
    shipping_costs::numeric(14, 4),
    delivery_min_days,
    delivery_max_days,
    delivery_time_cond
from
    public.shipping_profile_destinations;

-- test: select * from export_etl.v_shipping_profile_destinations limit 5;
do
$test$
begin
    if ('${runTest}' = 'true') then -- noqa
        /*
        --Size: 2872 kB
        select pg_size_pretty(pg_relation_size('public.shipping_profile_destinations')) as size;
        */
        perform * from export_etl.v_shipping_profile_destinations limit 5;
    end if;
end;
$test$;
