# More information on the parameters can be found below:
# https://documentation.red-gate.com/flyway/flyway-cli-and-api/configuration/parameters

[environments.default]
connectRetries = 5

[environments.cicd-docker-build]
url = "*****************************************************"

[environments.local]
url = "****************************************"

[environments.staging]
url = "*****************************************"

[environments.production]
url = "*****************************************"

[flyway]
defaultSchema = "analytics"
table = "schema_history"
baselineOnMigrate = true
baselineVersion = "0"
outOfOrder = true
batch = true

[flyway.placeholders]
changeReason = "20250627"
gcpMultiRegion = "EU"
runTest = false
