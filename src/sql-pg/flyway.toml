# More information on the parameters can be found below:
# https://documentation.red-gate.com/flyway/flyway-cli-and-api/configuration/parameters

[environments.default]
connectRetries = 5

[environments.cicd-docker-build]
url = "*****************************************************"

[environments.cicd-docker-build.flyway.placeholders]
changeReason = "20250429"
gcpMultiRegion = "EU"
runTest = false

[environments.local]
url = "****************************************"

[environments.staging]
url = "*****************************************"

[environments.production]
url = "*****************************************"

[environments.production.flyway.placeholders]
changeReason = "20250429"
gcpMultiRegion = "EU"
runTest = false

[flyway]
defaultSchema = "analytics"
table = "schema_history"
baselineOnMigrate = true
baselineVersion = "0"
outOfOrder = true
batch = true

#general placeholders
[flyway.placeholders]
changeReason = "20250429"
gcpMultiRegion = "EU"
runTest = true
