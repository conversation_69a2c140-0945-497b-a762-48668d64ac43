/*
This table already exists in PROD
for circuit breakers (goal of this migration), we need specific fields

>> Query to get the schema:

select
    column_name,
    data_type
from information_schema.columns
where table_schema = 'analytics'
  and table_name   = 'defect_alert_history'
order by ordinal_position;

 */

create table if not exists analytics.defect_alert_history
(
    -- general fields
    merchant_id integer,
    date date,

    -- defect score
    merchant_actual_defect_rate numeric
);

-- create index if not exists
create unique index if not exists merchant_date
on analytics.defect_alert_history using btree (merchant_id, date);
