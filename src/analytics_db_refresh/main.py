from config.cicd_config import CICD_CONFIG
from config.cloud_config import CLOUD_CONFIG
from config.common_config import dump_db, refresh_time_service, restore_db
from config.local_config import LOCAL_CONFIG
from google.cloud.sql.connector import Connector
from provider import Instance<PERSON>rovider
from refresh import AnalyticsDbRefresh
from refresh_shared.source_pg_repo import SourcePgRepository
from refresh_shared.target_pg_repo import TargetPgRepository

from common.config import DEFAULT_CONFIG
from common.logger import PASSWORD_MASKER, setup_logger
from common.mattermost_client import MattermostClient
from common.sql_repository import SqlRepository

logger = setup_logger(DEFAULT_CONFIG, PASSWORD_MASKER)


def run() -> None:
    """
    Code entry point.
    Creates the necessary Cloud SQL instances and runs the refresh process.
    """
    with Connector() as connector:
        config = LOCAL_CONFIG if DEFAULT_CONFIG.is_local else CLOUD_CONFIG
        sql_repo = SqlRepository(db_config=config.target_db_config, connector=connector)
        provider = InstanceProvider(
            env_config=DEFAULT_CONFIG,
            time_service=refresh_time_service,
            sql_repository=sql_repo,
            local_config=LOCAL_CONFIG,
            cloud_config=CLOUD_CONFIG,
            cicd_config=CICD_CONFIG,
        )
        provider_config = provider.create()

        source_db_repo = SourcePgRepository(
            db_config=provider_config.refresh_config.source_db_config, connector=connector
        )
        target_db_repo = TargetPgRepository(
            db_config=provider_config.refresh_config.target_db_config, connector=connector
        )

        refresh = AnalyticsDbRefresh(
            config=provider_config.refresh_config,
            restore_instance=provider_config.restore_source_instance,
            dump_db=dump_db,
            restore_db=restore_db,
            source_db_repository=source_db_repo,
            target_db_repository=target_db_repo,
            mattermost_client=MattermostClient(provider_config.refresh_config.alerting_config.webhook_url),
            time_service=refresh_time_service,
        )
        refresh.run(source_instance_name=provider_config.source_instance_name)
        refresh.send_refresh_summary()


if __name__ == "__main__":
    run()
