import dataclasses
import os
from functools import cached_property
from typing import Op<PERSON>

from config.consts import (
    SQL_COMMON_MIGRATIONS_PATH,
    SQL_SOURCE_MIGRATIONS_PATH,
    SQL_TARGET_MIGRATIONS_PATH,
)
from config.sleep_config import SLEEP_SECONDS
from refresh_shared.dump_db import DumpDb
from refresh_shared.migrate import FlywayParams, migrate_db
from refresh_shared.model import DbRefreshConfig
from refresh_shared.restore_db import RestoreDb
from refresh_shared.restore_instance import RestoreCloudSQLInstance
from refresh_shared.source_pg_repo import SourcePgRepository
from refresh_shared.target_pg_repo import TableCount, TargetPgRepository
from sqlalchemy.exc import ProgrammingError
from tabulate2 import tabulate

from common.logger import get_logger
from common.mattermost_client import MattermostClient, MattermostMessage
from common.pipeline_logging.pipeline_logger import log_pipeline_execution
from common.pipeline_logging.pipeline_model import SupportsPipelineExecution
from common.pipeline_logging.pipeline_reporter import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from common.sql_repository import SqlRepository
from common.time_service import TimeService
from common.timing import background_task, timing
from common.utils import get_run_id

logger = get_logger()


class AnalyticsDbRefresh(SupportsPipelineExecution):

    def __init__(
        self,
        config: DbRefreshConfig,
        restore_instance: RestoreCloudSQLInstance,
        source_db_repository: SourcePgRepository,
        target_db_repository: TargetPgRepository,
        dump_db: DumpDb,
        restore_db: RestoreDb,
        mattermost_client: MattermostClient,
        time_service: Optional[TimeService] = None,
    ) -> None:
        self._config = config
        self._restore_instance = restore_instance
        self._source_db_repository = source_db_repository
        self._target_db_repository = target_db_repository
        self._dump_db = dump_db
        self._restore_db = restore_db
        self._mattermost_client = mattermost_client
        self._time_service = time_service if time_service else TimeService()

    @timing(logger)
    @log_pipeline_execution(end=True)
    def run(self, source_instance_name: Optional[str] = None) -> None:
        """
        Refresh process flow.
        To understand it, see the `Analytics DB refresh` section in the README.md
        :param source_instance_name: Cloud SQL instance name on which pg_dump is running,
                              when done the instance is dropped. None for local run.
        """
        logger.info(
            f"Running {self.__class__.__name__} on '{self._config.env.upper()}' environment, run_id={self.run_id}..."
        )

        self.dump(source_instance_name)
        self.restore()
        self.finalize_refresh()

    @cached_property
    def run_id(self) -> str:
        """Unique ID of the run"""

        return get_run_id(self._time_service.now)

    @cached_property
    def pipeline_execution_repository(self) -> SqlRepository:
        """SQL repository used by `log_pipeline_execution` decorator"""

        return self._target_db_repository

    @log_pipeline_execution()
    @background_task(logger, sleep_seconds=SLEEP_SECONDS)
    def migrate_source_db(self) -> None:
        """
        Migrates source DB by running Flyway migrations.
        """
        params = FlywayParams(
            environment=self._config.env,
            host=self._config.source_db_config.host,
            port=self._config.source_db_config.port,
            database=self._config.source_db_config.database,
            user=self._config.source_db_config.username,
            password=self._config.source_db_config.password,
            config_files=f"{SQL_SOURCE_MIGRATIONS_PATH}/flyway.toml",
            locations=f"'filesystem:{SQL_COMMON_MIGRATIONS_PATH}, filesystem:{SQL_SOURCE_MIGRATIONS_PATH}'",
        )

        migrate_db(params=params)

    @background_task(logger, sleep_seconds=SLEEP_SECONDS)
    def fix_target_db_before_restore(self) -> None:
        """
        Fixes target DB by running Flyway special migrations to prevent some pg restore errors.
        """
        params = dataclasses.replace(
            self._config.flyway_params,
            host=self._config.target_db_config.host,
            database=self._config.target_db_config.database,
            user=self._config.target_db_config.username,
            password=self._config.target_db_config.password,
            config_files=f"{SQL_TARGET_MIGRATIONS_PATH}/flyway.toml",
            locations=f"filesystem:{SQL_TARGET_MIGRATIONS_PATH}",
        )
        migrate_db(params=params)

    @log_pipeline_execution()
    @background_task(logger, sleep_seconds=SLEEP_SECONDS)
    def migrate_target_db(self) -> None:
        """
        Run final migrations on target DB.
        """
        params = dataclasses.replace(
            self._config.flyway_params,
            host=self._config.target_db_config.host,
            database=self._config.target_db_config.database,
            user=self._config.target_db_config.username,
            password=self._config.target_db_config.password,
            locations=f"'filesystem:{SQL_COMMON_MIGRATIONS_PATH}, {self._config.flyway_params.locations}'",
            placeholders=[f"changeReason={self.run_id}"],
        )

        migrate_db(params=params)

    @log_pipeline_execution()
    def reload_order_item_offers(self) -> None:
        """
        Reloads order item offers index table.
        """
        self._source_db_repository.reload_order_item_offers()

    @log_pipeline_execution()
    def clean_offers(self) -> None:
        """
        Cleans offers table.
        """
        self._source_db_repository.clean_offers()

    def prepare_for_dump(self) -> None:
        """
        Performs all necessary preparations and creates a database dump.
        """
        self.migrate_source_db()
        self._source_db_repository.drop_tables(tables=self._config.tables_to_drop, cascade=True)
        self.reload_order_item_offers()
        self.clean_offers()
        self.run_initial_data_checks()
        self._source_db_repository.rename_schemas(self._config.pg_params.schema, self._config.temp_schema_suffix)

    def dump(self, source_instance_name: Optional[str] = None) -> None:
        """
        Runs the dump process, including its initiation and finalization phases.
        :param source_instance_name: Cloud SQL instance name on which pg_dump is running,
                              when done the instance is dropped. None for local run.
        """
        try:
            self.prepare_for_dump()
            self.dump_db()
        finally:
            if source_instance_name:
                self._restore_instance.drop_instance(source_instance_name)

    @log_pipeline_execution()
    def dump_db(self) -> None:
        """
        Dumps PostgreSQL database by using `pg_dump` command.
        """
        # Needed for pg_dump to use the source db password
        os.environ["PGPASSWORD"] = self._config.source_db_config.password

        params = dataclasses.replace(self._config.pg_params, schema=self._config.temp_schemas)
        db_config = dataclasses.replace(self._config.source_db_config, host=self._config.source_db_config.host)

        self._dump_db.dump(pg_params=params, connection_url=db_config.connection_url)

    def prepare_for_restore(self) -> None:
        """
        Performs all necessary preparations for the restore process.
        """
        self.recreate_target_database_schemas()
        self.fix_target_db_before_restore()

    @log_pipeline_execution()
    def vacuum_db(self) -> None:
        """
        Vacuums database.
        """
        self._target_db_repository.vacuum_database()

    def restore(self) -> None:
        """
        Runs the restore process, including its initiation and finalization phases.
        """
        self.prepare_for_restore()
        self.restore_db()
        self.refresh_materialized_views()

    @log_pipeline_execution()
    def restore_db(self) -> None:
        """
        Restores PostgreSQL database by using `pg_restore` command.
        """
        # Needed for pg_restore to use the target db password
        os.environ["PGPASSWORD"] = self._config.target_db_config.password

        params = dataclasses.replace(
            self._config.pg_params, schema=self._config.temp_schemas, host=self._config.target_db_config.host
        )

        db_config = dataclasses.replace(self._config.target_db_config, host=self._config.target_db_config.host)

        self._restore_db.restore(pg_params=params, connection_url=db_config.connection_url)

    def finalize_refresh(self) -> None:
        """
        Finalizes the refresh process:
            - locks database,
            - runs the switch process,
            - migrates target db,
            - unlocks database,
            - runs data checks,
            - vacuum database.
        """

        self.lock_database()
        try:
            self.switch_db_schemas()
            self.migrate_target_db()
            self._target_db_repository.revert_routines_schemas(
                self._config.temp_schemas, self._config.temp_schema_suffix
            )
        finally:
            self.unlock_database()

        self.run_final_data_checks()

        if self._config.run_vacuum_analyze_weekday == self._time_service.today.isoweekday():
            self.vacuum_db()
        else:
            logger.info("Vacuum analyze skipped.")

    @log_pipeline_execution()
    def switch_db_schemas(self) -> None:
        """
        Switches target db schemas from temporary to the destination.
        Reverts source db schema to the initial name.
        Needed for blue & green refresh.
        """
        logger.info("Switching schemas...")
        self._target_db_repository.create_schemas(self._config.pg_params.schema)

        for schema in self._config.temp_schemas:
            initial_schema_name = schema.split(f"{self._config.temp_schema_suffix}")[0]
            backup_schema_name = f"{initial_schema_name}{self._config.backup_schema_suffix}"

            self._target_db_repository.rename_schema(initial_schema_name, backup_schema_name)
            try:
                self._target_db_repository.rename_schema(schema, initial_schema_name)
                self._target_db_repository.drop_schemas(self._config.backed_up_schemas)
            except Exception as error:
                logger.error(f"Cannot switch schemas, rolling back, {error=}...")
                self._target_db_repository.rename_schema(backup_schema_name, initial_schema_name)
                raise

    def recreate_target_database_schemas(self) -> None:
        """
        Recreates target database and schemas.
        """
        self._target_db_repository.create_schemas(self._config.temp_schemas)

    def lock_database(self) -> None:
        """
        Tries to lock database before the final refresh steps.
        """
        self._target_db_repository.lock_non_system_users()
        try:
            self._target_db_repository.kill_all_running_sessions()
        except ProgrammingError as error:
            logger.warning(f"Cannot kill all running sessions, {error=}")

    @log_pipeline_execution()
    def refresh_materialized_views(self) -> None:
        """
        Refreshes materialized views.
        """
        self._target_db_repository.refresh_materialized_views(self._config.temp_materialized_views_to_refresh)

    def unlock_database(self) -> None:
        """
        Unlocks database after the refresh.
        """
        self._target_db_repository.unlock_non_system_users()

    @log_pipeline_execution()
    def run_initial_data_checks(self) -> None:
        """
        Runs initial data checks on the platform source instance.
        """
        self._source_db_repository.persist_platform_tables_count(
            run_id=self.run_id, schemas=self._config.pg_params.schema
        )

    @log_pipeline_execution()
    def run_final_data_checks(self) -> None:
        """
        Runs final data checks.
        """
        self._target_db_repository.load_platform_tables_count()
        self._target_db_repository.load_analytics_tables_count(
            run_id=self.run_id, schemas=self._config.pg_params.schema
        )
        self._notify_tables_count_discrepancies()

    def send_refresh_summary(self) -> None:
        """
        Sends execution summary to MM `Analytics > Info`.
        """
        channel = self._config.alerting_config.info_channel
        reporter = PipelineReporter(self, self._mattermost_client)
        reporter.send_report(channel)

    def _notify_tables_count_discrepancies(self) -> None:
        """
        Sends notification to MM `Analytics > Warning` when tables count discrepancies found.
        """
        diff_count = self._target_db_repository.get_refresh_tables_summary(run_id=self.run_id, show_diff_only=True)

        if diff_count:
            markdown = tabulate(diff_count, headers=TableCount.fields(), tablefmt="github")
            message_text = (
                f":warning: [{self._config.env.upper()}] Found following table count discrepancies "
                f"after Analytics DB refresh:"
                f"\n\n{markdown}"
            )
            self._mattermost_client.send_message(
                message=MattermostMessage(text=message_text, channel=self._config.alerting_config.warning_channel)
            )
