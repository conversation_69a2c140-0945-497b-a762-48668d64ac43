# IMAGE_VERSION version passed from terraform module
ARG IMAGE_VERSION

FROM flyway/flyway:11.8.1 as flyway
FROM $IMAGE_VERSION

# Copy Flyway CLI and Java runtime from the Flyway image
COPY --from=flyway /flyway /flyway
COPY --from=flyway /opt/java/openjdk /opt/java/openjdk

# Set PATH to include Java and Flyway
ENV PATH="${PATH}:/opt/java/openjdk/bin:/flyway/"

RUN apt-get update -y && apt-get upgrade -y
RUN apt-get install -y postgresql-client-15 postgresql-client-common libpq-dev

# Validate Flyway installation
RUN echo "Validating Flyway and Java versions..." && flyway --version && java --version && python --version

# Validate PostgreSQL client tools are installed with correct version
RUN echo "Validating PostgreSQL client tools..." && \
    PG_DUMP_VERSION=$(pg_dump --version | grep -oE '[0-9]+' | head -1) && \
    PG_RESTORE_VERSION=$(pg_restore --version | grep -oE '[0-9]+' | head -1) && \
    echo "Found pg_dump major version: $PG_DUMP_VERSION" && \
    echo "Found pg_restore major version: $PG_RESTORE_VERSION" && \
    if [ "$PG_DUMP_VERSION" != "15" ]; then \
        echo "ERROR: pg_dump version 15 not found. Got major version: $PG_DUMP_VERSION" && exit 1; \
    fi && \
    if [ "$PG_RESTORE_VERSION" != "15" ]; then \
        echo "ERROR: pg_restore version 15 not found. Got major version: $PG_RESTORE_VERSION" && exit 1; \
    fi && \
    echo "✅ PostgreSQL client tools validation passed!"

# Set our working directory
WORKDIR /app

# Copy build files
COPY ./requirements.txt .

# Then install all dependencies
RUN python -m venv venv
RUN . venv/bin/activate
RUN pip install -r requirements.txt

# Copy over the rest of the code
COPY . .

# https://stackoverflow.com/a/59812588
ENV PYTHONUNBUFFERED=1
CMD ["python", "main.py"]
