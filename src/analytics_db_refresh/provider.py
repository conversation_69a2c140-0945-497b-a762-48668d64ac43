import dataclasses
from dataclasses import dataclass
from functools import cached_property
from os import environ
from typing import Optional

from config.base_config import RefreshBaseConfig
from refresh import AnalyticsDbRefresh
from refresh_shared.model import DbRefreshConfig
from refresh_shared.restore_instance import RestoreCloudSQLInstance

from common.cloud_sql_instance_client.instance_client import CloudSQLInstance
from common.cloud_sql_instance_client.instance_model import Backup
from common.logger import Config, get_logger
from common.pipeline_logging.pipeline_logger import log_pipeline_execution
from common.pipeline_logging.pipeline_model import SupportsPipelineExecution
from common.sql_repository import SqlRepository
from common.time_service import TimeService
from common.utils import get_run_id, string_to_bool, to_space_separated

logger = get_logger()


class ProviderError(ValueError):
    """Provider error"""


@dataclass(frozen=True)
class ProviderConfig:
    """
    - restore_source_instance: RestoreCloudSQLInstance needed to drop the source instance after pg_dump is done
    - refresh_config: config for the whole refresh process
    - source_instance: object describing temporary Cloud SQL instance when we run pg_dump, dropped when pg_dump is done
    - target_instance: object describing Cloud SQL instance when we run pg_restore, real analytics db instance on prod
    """

    restore_source_instance: RestoreCloudSQLInstance
    refresh_config: DbRefreshConfig
    source_instance: Optional[CloudSQLInstance]
    target_instance: Optional[CloudSQLInstance]

    @property
    def source_instance_name(self) -> Optional[str]:
        return self.source_instance.name if self.source_instance else None


class InstanceProvider(SupportsPipelineExecution):
    """
    Provides Cloud SQL instances for the refresh process.
    """

    def __init__(
        self,
        env_config: Config,
        local_config: RefreshBaseConfig,
        cloud_config: RefreshBaseConfig,
        cicd_config: RefreshBaseConfig,
        time_service: TimeService,
        sql_repository: SqlRepository,
    ):
        self._env_config = env_config
        self._time_service = time_service
        self._sql_repository = sql_repository

        self._refresh_config = (
            local_config if self._env_config.is_local else cicd_config if self.is_cicd_run else cloud_config
        )

    @property
    def is_cicd_run(self) -> bool:
        """
        `CICD` environment variable with "True" value has to be passed from the test
        """
        is_cicd = string_to_bool(environ.get("CICD"))

        return self._env_config.is_staging and is_cicd

    @cached_property
    def run_id(self) -> str:
        """Unique ID of the run"""

        return get_run_id(self._time_service.now)

    @cached_property
    def pipeline_execution_repository(self) -> SqlRepository:
        """SQL repository used by `log_pipeline_execution` decorator"""

        return self._sql_repository

    @log_pipeline_execution(start=True, end=True, pipeline_name=to_space_separated(AnalyticsDbRefresh.__name__))
    def create(self) -> ProviderConfig:
        """
        Creates Cloud SQL instances and provides environment-specific configurations.

        For production, it creates a new source instance from the latest platform production backup
        and describes the existing target instance which is analytics db.

        For CICD on staging, it creates a new source instance from the latest analytics db staging backup
        and describes the existing target instance which is analytics db on staging.

        For staging, it creates a new source instance from the latest platform production backup
        and a new target instance from the latest analytics db production backup.

        For local run, it returns None for all instances, since local run uses local docker db.
        """

        logger.info("Providing Cloud SQL instances before the refresh starts...")

        source_instance = self.create_source_instance()
        self.restore_source_backup(source_instance)

        target_instance = self.prepare_target_instance()
        if self._env_config.is_staging:
            self.restore_target_backup(target_instance)

        refresh_db_config = self.update_db_config(
            self._refresh_config.refresh_db_config, source_instance, target_instance
        )

        return ProviderConfig(
            restore_source_instance=self._refresh_config.source_restore_instance,
            refresh_config=refresh_db_config,
            source_instance=source_instance,
            target_instance=target_instance,
        )

    @log_pipeline_execution(pipeline_name=to_space_separated(AnalyticsDbRefresh.__name__))
    def create_source_instance(self) -> Optional[CloudSQLInstance]:
        """
        Provides the source Cloud SQL instance.
        On staging (including CICD flow) and production, it creates a new instance from the latest backup.
        In local, it returns None.
        """

        if self._env_config.is_local:
            return None

        logger.info(f"Creating Cloud SQL '{self._refresh_config.source_db_config.instance_name}' instance...")
        source_instance = self._refresh_config.source_restore_instance.recreate_instance()

        return source_instance

    @log_pipeline_execution(pipeline_name=to_space_separated(AnalyticsDbRefresh.__name__))
    def restore_source_backup(self, instance: CloudSQLInstance) -> Optional[Backup]:
        """
        Restores the source Cloud SQL instance from the latest backup.
        On staging (including CICD flow) and production, it restores the latest backup.
        In local, it returns None.
        :param instance: CloudSQLInstance object describing existing instance
        :param config: environment-specific config
        :returns: Backup object describing existing backup
        """

        if self._env_config.is_local:
            return None

        logger.info(
            f"Restoring backup into Cloud SQL '{self._refresh_config.source_db_config.instance_name}' instance..."
        )
        backup = self._refresh_config.source_restore_instance.restore_backup(
            instance, self._refresh_config.wait_for_today_backup
        )
        logger.info(f"Restored backup '{backup.id}' into Cloud SQL '{instance.name}' instance.")

        return backup

    @log_pipeline_execution(pipeline_name=to_space_separated(AnalyticsDbRefresh.__name__))
    def prepare_target_instance(self) -> Optional[CloudSQLInstance]:
        """
        Provides the target Cloud SQL instance.
        On staging (including CICD flow), it creates a new instance.
        On production, it describes the existing instance.
        In local, it returns None.
        """

        if self._env_config.is_local:
            return None

        if self.is_cicd_run or self._env_config.is_production:
            logger.info(f"Describing Cloud SQL '{self._refresh_config.target_db_config.instance_name}' instance...")
            target_instance = self._refresh_config.target_restore_instance.describe_instance(
                self._refresh_config.target_db_config.instance_name
            )
        else:
            logger.info(f"Creating Cloud SQL '{self._refresh_config.target_db_config.instance_name}' instance...")
            target_instance = self._refresh_config.target_restore_instance.recreate_instance()

        return target_instance

    @log_pipeline_execution(pipeline_name=to_space_separated(AnalyticsDbRefresh.__name__))
    def restore_target_backup(self, instance: CloudSQLInstance) -> Optional[Backup]:
        """
        On staging, restores the target Cloud SQL instance from the latest backup.
        In other environments, it returns None.
        """

        if not self._env_config.is_staging:
            return None

        logger.info(
            f"Restoring backup into Cloud SQL '{self._refresh_config.target_db_config.instance_name}' instance..."
        )
        backup = self._refresh_config.target_restore_instance.restore_backup(
            instance, self._refresh_config.wait_for_today_backup
        )
        logger.info(f"Restored backup '{backup.id}' into Cloud SQL '{instance.name}' instance.")

        return backup

    @staticmethod
    def update_db_config(
        refresh_config: DbRefreshConfig,
        source_instance: Optional[CloudSQLInstance],
        target_instance: Optional[CloudSQLInstance],
    ) -> DbRefreshConfig:
        """
        Assigns private IP address to config from the already created/described new Cloud SQL instance
        :param refresh_config: environment-specific config
        :param source_instance: CloudSQLInstance object describing source instance
        :param target_instance: CloudSQLInstance object describing target instance

        :returns: updated config
        :raises AnalyticsDbRefreshError: when no private IP found for already created new instance
        """

        if not source_instance or not target_instance:
            return refresh_config

        if not source_instance.private_ip_address:
            raise ProviderError(
                f"Source Cloud SQL '{source_instance.name}' instance does not expose private IP address!"
            )

        if not target_instance.private_ip_address:
            raise ProviderError(
                f"Target Cloud SQL '{target_instance.name}' instance does not expose private IP address!"
            )

        refresh_config = dataclasses.replace(
            refresh_config,
            source_db_config=dataclasses.replace(
                refresh_config.source_db_config, host=source_instance.private_ip_address
            ),
            target_db_config=dataclasses.replace(
                refresh_config.target_db_config, host=target_instance.private_ip_address
            ),
        )

        return refresh_config
