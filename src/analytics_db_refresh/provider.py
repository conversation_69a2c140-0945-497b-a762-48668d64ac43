import dataclasses
from dataclasses import dataclass
from os import environ
from typing import Optional

from refresh_shared.model import DbRefreshConfig
from refresh_shared.restore_instance import RestoreCloudSQLInstance

from common.cloud_sql_instance_client.instance_client import CloudSQLInstance
from common.logger import Config, get_logger
from common.utils import string_to_bool

logger = get_logger()


class ProviderError(ValueError):
    """Provider error"""


@dataclass(frozen=True)
class ProviderConfig:
    """
    - restore_source_instance: RestoreCloudSQLInstance needed to drop the source instance after pg_dump is done
    - refresh_config: config for the whole refresh process
    - source_instance: object describing temporary Cloud SQL instance when we run pg_dump, dropped when pg_dump is done
    - target_instance: object describing Cloud SQL instance when we run pg_restore, real analytics db instance on prod
    """

    restore_source_instance: RestoreCloudSQLInstance
    refresh_config: DbRefreshConfig
    source_instance: Optional[CloudSQLInstance]
    target_instance: Optional[CloudSQLInstance]

    @property
    def source_instance_name(self) -> Optional[str]:
        return self.source_instance.name if self.source_instance else None


class InstanceProvider:

    def __init__(self, config: Config):
        self._env_config = config

    @property
    def is_cicd_run(self) -> bool:
        """
        `CICD` environment variable with "True" value has to be passed from the test
        """
        is_cicd = string_to_bool(environ.get("CICD"))

        return self._env_config.is_staging and is_cicd

    def create(self) -> ProviderConfig:
        """Creates Cloud SQL instances and provides environment-specific configurations"""

        logger.info("Providing Cloud SQL instances before the refresh starts...")

        if self._env_config.is_local:
            """
            For local env we run the process on one
            Docker Postgres instance but target pg_restore to a new database.
            """
            from config.local_config import LOCAL_CONFIG

            config = LOCAL_CONFIG
            source_instance = None
            target_instance = None
        else:
            from config.cloud_config import CLOUD_CONFIG

            config = CLOUD_CONFIG

            if self.is_cicd_run:
                """
                For CI/CD, to speed up the test, first restore one small instance from the staging one.
                The target instance is a staging small instance.
                """
                from config.cicd_config import CICD_CONFIG

                config = CICD_CONFIG

                logger.info(f"Creating Cloud SQL '{config.source_db_config.instance_name}' instance...")
                source_instance = config.source_restore_instance.run(should_wait=False)
                logger.info(f"Describing Cloud SQL '{config.target_db_config.instance_name}' instance...")
                target_instance = config.target_restore_instance.describe_instance(
                    config.target_db_config.instance_name
                )
            elif self._env_config.is_staging:
                """
                For staging env first restore 2 instances:
                source instance where we run all cleanups and pg_dump and finally drop it,
                target instance to mimic production analytics db instance where we run pg_restore.
                Note that target instance is not dropped automatically, so you have to do this manually
                after you finish testing the solution.
                """
                logger.info(f"Creating Cloud SQL '{config.source_db_config.instance_name}' instance...")
                source_instance = config.source_restore_instance.run(should_wait=True)
                logger.info(f"Creating Cloud SQL '{config.target_db_config.instance_name}' instance...")
                target_instance = config.target_restore_instance.run(should_wait=False)
            else:
                """
                For production env restore one source instance only,
                where we run all cleanups and pg_dump and finally drop it.
                Target instance is an existing production analytics db.
                """
                logger.info(f"Creating Cloud SQL '{config.source_db_config.instance_name}' instance...")
                source_instance = config.source_restore_instance.run(should_wait=True)
                logger.info(f"Describing Cloud SQL '{config.target_db_config.instance_name}' instance...")
                target_instance = config.target_restore_instance.describe_instance(
                    config.target_db_config.instance_name
                )

        refresh_db_config = self._update_db_config(config.refresh_db_config, source_instance, target_instance)

        return ProviderConfig(
            restore_source_instance=config.source_restore_instance,
            refresh_config=refresh_db_config,
            source_instance=source_instance,
            target_instance=target_instance,
        )

    @staticmethod
    def _update_db_config(
        refresh_config: DbRefreshConfig,
        source_instance: CloudSQLInstance,
        target_instance: CloudSQLInstance,
    ) -> DbRefreshConfig:
        """
        Assigns private IP address to config from the already created new Cloud SQL instance
        :param refresh_config: environment-specific config
        :param source_instance: CloudSQLInstance object describing source instance
        :param target_instance: CloudSQLInstance object describing target instance

        :returns: updated config
        :raises AnalyticsDbRefreshError: when no private IP found for already created new instance
        """

        if not source_instance.private_ip_address:
            raise ProviderError(
                f"Source Cloud SQL '{source_instance.name}' instance does not expose private IP address!"
            )

        if not target_instance.private_ip_address:
            raise ProviderError(
                f"Target Cloud SQL '{target_instance.name}' instance does not expose private IP address!"
            )

        refresh_config = dataclasses.replace(
            refresh_config,
            source_db_config=dataclasses.replace(
                refresh_config.source_db_config, host=source_instance.private_ip_address
            ),
            target_db_config=dataclasses.replace(
                refresh_config.target_db_config, host=target_instance.private_ip_address
            ),
        )

        return refresh_config
