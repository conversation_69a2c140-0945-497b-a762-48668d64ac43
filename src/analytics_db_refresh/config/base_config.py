from abc import ABC, abstractmethod
from functools import cached_property

from config.consts import SQL_MIGRATIONS_PATH
from refresh_shared.dump_restore import PgParams
from refresh_shared.migrate import FlywayParams
from refresh_shared.model import DbRefreshConfig
from refresh_shared.restore_instance import RestoreCloudSQLInstance

from common.config import Config, MattermostAlertingConfig
from common.sql_client import DatabaseConfig


class RefreshBaseConfig(ABC):
    """
    Abstract base class for environment-specific configurations.
    Provides a common interface for accessing configuration properties.
    """

    def __init__(
        self,
        config: Config,
        alerting_config: MattermostAlertingConfig,
    ):
        self._config = config
        self._alerting_config = alerting_config

    @cached_property
    def wait_for_today_backup(self) -> bool:
        """Whether to wait for today backup."""
        return self._config.is_production

    @cached_property
    @abstractmethod
    def source_db_config(self) -> DatabaseConfig:
        """Provides the source database configuration."""
        pass

    @cached_property
    @abstractmethod
    def target_db_config(self) -> DatabaseConfig:
        """Provides the target database configuration."""
        pass

    @cached_property
    def pg_params(self) -> PgParams:
        """Provides the Flyway migration parameters."""
        return PgParams()

    @cached_property
    def flyway_params(self) -> FlywayParams:
        """Provides the Flyway migration parameters."""

        return FlywayParams(
            host=self.pg_params.host,
            port=self.source_db_config.port,
            database=self.source_db_config.database,
            user=self.source_db_config.username,
            password=self.source_db_config.password,
            config_files=f"{SQL_MIGRATIONS_PATH}/flyway.toml",
            locations=f"filesystem:{SQL_MIGRATIONS_PATH}",
            environment=self._config.env,
            placeholders=["runTest=false"],
        )

    @cached_property
    @abstractmethod
    def source_restore_instance(self) -> RestoreCloudSQLInstance:
        """Provides the configuration for restoring the source Cloud SQL instance."""
        pass

    @cached_property
    @abstractmethod
    def target_restore_instance(self) -> RestoreCloudSQLInstance:
        """
        Provides the configuration for restoring the target Cloud SQL instance.
        Returns None if not applicable for the environment.
        """
        pass

    @cached_property
    def refresh_db_config(self) -> DbRefreshConfig:
        """Provides the database refresh configuration."""

        return DbRefreshConfig(
            source_db_config=self.source_db_config,
            target_db_config=self.target_db_config,
            pg_params=self.pg_params,
            flyway_params=self.flyway_params,
            alerting_config=self._alerting_config,
        )
