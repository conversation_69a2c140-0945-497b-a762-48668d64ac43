from functools import cached_property

from config.base_config import RefreshBaseConfig
from config.common_config import mm_alerting_config, refresh_time_service
from config.consts import (
    ANALYTICS_PROJECT_NAME,
    ANALYTICS_SQL_INSTANCE,
    PLATFORM_PROJECT_NAME,
    PLATFORM_SQL_INSTANCE,
    db_flags,
    db_maintenance_window,
)
from refresh_shared.restore_instance import (
    RestoreCloudSQLInstance,
    RestoreCloudSQLInstanceConfig,
)

from common.cloud_sql_instance_client.instance_client import (
    CloudSQLInstanceClient,
    CloudSQLInstanceClientConfig,
)
from common.cloud_sql_instance_client.instance_model import (
    BackupConfiguration,
    CloudSQLCreateInstanceParams,
    DatabaseFlag,
    InstanceCreateSettings,
    IpConfiguration,
    MaintenanceWindow,
)
from common.config import DEFAULT_CONFIG, Config, MattermostAlertingConfig
from common.secret_client import get_secret
from common.sql_client import DatabaseConfig
from common.time_service import TimeService


class CloudConfig(RefreshBaseConfig):
    """Config for cloud runs only."""

    def __init__(
        self,
        config: Config,
        database_flags: list[DatabaseFlag],
        maintenance_window: MaintenanceWindow,
        alerting_config: MattermostAlertingConfig,
        time_service: TimeService,
    ):
        super().__init__(config, alerting_config)
        self._database_flags = database_flags
        self._maintenance_window = maintenance_window
        self._time_service = time_service

    @cached_property
    def source_db_config(self) -> DatabaseConfig:
        secret = get_secret("analytics-db-refresh-source-secret", self._config.project_id)

        return DatabaseConfig.from_json(secret)

    @cached_property
    def target_db_config(self) -> DatabaseConfig:
        secret = get_secret("analytics-db-refresh-target-secret", self._config.project_id)

        return DatabaseConfig.from_json(secret)

    @cached_property
    def instance_client_config(self) -> CloudSQLInstanceClientConfig:
        return CloudSQLInstanceClientConfig(sleep_in_seconds=10)

    @cached_property
    def instance_settings(self) -> InstanceCreateSettings:
        return InstanceCreateSettings(
            tier="db-custom-32-65536",  # 32 CPUs, 64 GB RAM
            data_disk_size_gb=600,
            backup_configuration=BackupConfiguration(),
            ip_configuration=IpConfiguration(),
            database_flags=self._database_flags,
            maintenance_window=self._maintenance_window,
        )

    @cached_property
    def source_restore_instance(self) -> RestoreCloudSQLInstance:
        instance_params = CloudSQLCreateInstanceParams(
            name=self.source_db_config.instance_name,
            root_password=self.source_db_config.password,
            settings=self.instance_settings,
        )

        instance_config = RestoreCloudSQLInstanceConfig(
            new_instance_params=instance_params,
            backup_instance_name=PLATFORM_SQL_INSTANCE,
            backup_project_name=PLATFORM_PROJECT_NAME,
        )

        return RestoreCloudSQLInstance(
            instance_client=CloudSQLInstanceClient(config=self.instance_client_config),
            config=instance_config,
            time_service=self._time_service,
        )

    @cached_property
    def target_restore_instance(self) -> RestoreCloudSQLInstance:
        instance_params = CloudSQLCreateInstanceParams(
            name=self.target_db_config.instance_name,
            root_password=self.target_db_config.password,
            settings=self.instance_settings,
        )

        instance_config = RestoreCloudSQLInstanceConfig(
            new_instance_params=instance_params,
            backup_instance_name=ANALYTICS_SQL_INSTANCE,
            backup_project_name=ANALYTICS_PROJECT_NAME,
        )

        return RestoreCloudSQLInstance(
            instance_client=CloudSQLInstanceClient(config=self.instance_client_config),
            config=instance_config,
            time_service=self._time_service,
        )


CLOUD_CONFIG = CloudConfig(
    config=DEFAULT_CONFIG,
    database_flags=db_flags,
    maintenance_window=db_maintenance_window,
    alerting_config=mm_alerting_config,
    time_service=refresh_time_service,
)
