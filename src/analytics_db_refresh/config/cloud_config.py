from config.base_config import (
    ANALYTICS_PROJECT_NAME,
    ANALYTICS_SQL_INSTANCE,
    DEFAULT_CONFIG,
    PLATFORM_PROJECT_NAME,
    PLATFORM_SQL_INSTANCE,
    SLEEP_SECONDS,
    SQL_MIGRATIONS_PATH,
    database_flags,
    maintenance_window,
)
from config.common_config import pg_params, refresh_time_service
from refresh_shared.migrate import FlywayParams
from refresh_shared.restore_instance import (
    RestoreCloudSQLInstance,
    RestoreCloudSQLInstanceConfig,
)

from common.cloud_sql_instance_client.instance_client import (
    CloudSQLInstanceClient,
    CloudSQLInstanceClientConfig,
)
from common.cloud_sql_instance_client.instance_model import (
    BackupConfiguration,
    CloudSQLCreateInstanceParams,
    InstanceCreateSettings,
    IpConfiguration,
)
from common.secret_client import get_secret
from common.sql_client import DatabaseConfig

"""Config for cloud runs only."""

source_db_secret = get_secret("analytics-db-refresh-source-secret", DEFAULT_CONFIG.project_id)
source_db_config = DatabaseConfig.from_json(source_db_secret)
target_db_secret = get_secret("analytics-db-refresh-target-secret", DEFAULT_CONFIG.project_id)
target_db_config = DatabaseConfig.from_json(target_db_secret)

source_instance_params = CloudSQLCreateInstanceParams(
    name=source_db_config.instance_name,
    root_password=source_db_config.password,
    settings=InstanceCreateSettings(
        tier="db-custom-32-65536",  # 32 CPUs, 64 GB RAM
        data_disk_size_gb=600,
        backup_configuration=BackupConfiguration(),
        ip_configuration=IpConfiguration(),
        database_flags=database_flags,
        maintenance_window=maintenance_window,
    ),
)

source_restore_instance_config = RestoreCloudSQLInstanceConfig(
    new_instance_params=source_instance_params,
    backup_instance_name=PLATFORM_SQL_INSTANCE,
    backup_project_name=PLATFORM_PROJECT_NAME,
)

sql_instance_client_config = CloudSQLInstanceClientConfig(sleep_in_seconds=SLEEP_SECONDS)

source_restore_instance = RestoreCloudSQLInstance(
    instance_client=CloudSQLInstanceClient(config=sql_instance_client_config),
    config=source_restore_instance_config,
    time_service=refresh_time_service,
)

target_instance_params = CloudSQLCreateInstanceParams(
    name=target_db_config.instance_name,
    root_password=target_db_config.password,
    settings=InstanceCreateSettings(
        tier="db-custom-32-65536",  # 32 CPUs, 64 GB RAM
        data_disk_size_gb=600,
        backup_configuration=BackupConfiguration(),
        ip_configuration=IpConfiguration(),
        database_flags=database_flags,
        maintenance_window=maintenance_window,
    ),
)

target_restore_instance_config = RestoreCloudSQLInstanceConfig(
    new_instance_params=target_instance_params,
    backup_instance_name=ANALYTICS_SQL_INSTANCE,
    backup_project_name=ANALYTICS_PROJECT_NAME,
)

target_restore_instance = RestoreCloudSQLInstance(
    instance_client=CloudSQLInstanceClient(config=sql_instance_client_config),
    config=target_restore_instance_config,
    time_service=refresh_time_service,
)

flyway_params = FlywayParams(
    host=pg_params.host,
    port=source_db_config.port,
    database=source_db_config.database,
    user=source_db_config.username,
    password=source_db_config.password,
    config_files=f"{SQL_MIGRATIONS_PATH}/flyway.toml",
    locations=f"filesystem:{SQL_MIGRATIONS_PATH}",
    environment=DEFAULT_CONFIG.env,
)
