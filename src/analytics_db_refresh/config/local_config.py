import dataclasses
import os
from functools import cached_property

from config.base_config import SQL_MIGRATIONS_PATH, BaseEnvConfig
from config.common_config import mm_alerting_config
from refresh_shared.dump_restore import PgParams
from refresh_shared.migrate import FlywayParams
from refresh_shared.model import DbRefreshConfig
from refresh_shared.restore_instance import (
    RestoreCloudSQLInstance,
    RestoreCloudSQLInstanceConfig,
)

from common.cloud_sql_instance_client.instance_client import (
    CloudSQLInstanceClient,
    CloudSQLInstanceClientConfig,
)
from common.cloud_sql_instance_client.instance_model import (
    BackupConfiguration,
    CloudSQLCreateInstanceParams,
    InstanceCreateSettings,
)
from common.config import DEFAULT_CONFIG, Config, MattermostAlertingConfig
from common.sql_client import DatabaseConfig


class LocalConfig(BaseEnvConfig):
    """Config for local runs only."""

    def __init__(
        self,
        config: Config,
        alerting_config: MattermostAlertingConfig,
    ):
        super().__init__(config, alerting_config)

    @cached_property
    def source_db_config(self) -> DatabaseConfig:
        return DatabaseConfig()

    @cached_property
    def target_db_config(self) -> DatabaseConfig:
        """Provides the target database configuration."""
        return dataclasses.replace(self.source_db_config, database="analytics")

    @cached_property
    def source_restore_instance(self) -> RestoreCloudSQLInstance:
        instance_config = RestoreCloudSQLInstanceConfig(
            new_instance_params=CloudSQLCreateInstanceParams(
                name=self.source_db_config.instance_name,
                root_password=self.source_db_config.password,
                settings=InstanceCreateSettings(backup_configuration=BackupConfiguration()),
            ),
            backup_instance_name="localhost",
            backup_project_name="local",
        )

        return RestoreCloudSQLInstance(
            instance_client=CloudSQLInstanceClient(config=CloudSQLInstanceClientConfig()), config=instance_config
        )

    @cached_property
    def target_restore_instance(self) -> RestoreCloudSQLInstance:
        return self.source_restore_instance

    @cached_property
    def pg_params(self) -> PgParams:
        return PgParams()

    @cached_property
    def flyway_params(self) -> FlywayParams:
        return FlywayParams(
            host=self.pg_params.host,
            port=self.source_db_config.port,
            database=self.source_db_config.database,
            user=self.source_db_config.username,
            password=self.source_db_config.password,
            config_files=f"{SQL_MIGRATIONS_PATH}/flyway.toml",
            locations=f"filesystem:{SQL_MIGRATIONS_PATH}",
            environment=self._config.env,
        )

    @cached_property
    def refresh_db_config(self) -> DbRefreshConfig:
        return DbRefreshConfig(
            source_db_config=self.source_db_config,
            target_db_config=self.target_db_config,
            pg_params=self.pg_params,
            flyway_params=self.flyway_params,
            alerting_config=mm_alerting_config,
        )


LOCAL_CONFIG = LocalConfig(config=DEFAULT_CONFIG, alerting_config=mm_alerting_config)

# Needed for local pg_dump/pg_restore runs
os.environ["PGPASSWORD"] = LOCAL_CONFIG.source_db_config.password
