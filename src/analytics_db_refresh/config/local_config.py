import dataclasses
import os

from config.base_config import DEFAULT_CONFIG, SQL_MIGRATIONS_PATH
from config.common_config import alerting_config, pg_params
from refresh_shared.migrate import FlywayParams
from refresh_shared.model import DbRefreshConfig
from refresh_shared.restore_instance import (
    RestoreCloudSQLInstance,
    RestoreCloudSQLInstanceConfig,
)

from common.cloud_sql_instance_client.instance_client import (
    CloudSQLInstanceClient,
    CloudSQLInstanceClientConfig,
)
from common.cloud_sql_instance_client.instance_model import (
    BackupConfiguration,
    CloudSQLCreateInstanceParams,
    InstanceCreateSettings,
)
from common.sql_client import DatabaseConfig

"""Config for local runs only."""

local_db_source_config = DatabaseConfig()

# Run the refresh on the same instance but target a new database.
local_db_target_config = dataclasses.replace(local_db_source_config, database="analytics")

# Needed for local pg_dump/pg_restore runs
os.environ["PGPASSWORD"] = local_db_source_config.password

flyway_params = FlywayParams(
    host=local_db_source_config.host,
    port=local_db_source_config.port,
    database=local_db_source_config.database,
    user=local_db_source_config.username,
    password=local_db_source_config.password,
    config_files=f"{SQL_MIGRATIONS_PATH}/flyway.toml",
    locations=f"filesystem:{SQL_MIGRATIONS_PATH}",
    environment=DEFAULT_CONFIG.env,
)

local_refresh_db_config = DbRefreshConfig(
    source_db_config=local_db_source_config,
    target_db_config=local_db_target_config,
    pg_params=pg_params,
    flyway_params=flyway_params,
    alerting_config=alerting_config,
)

target_instance_config = RestoreCloudSQLInstanceConfig(
    new_instance_params=CloudSQLCreateInstanceParams(
        name=local_db_source_config.instance_name,
        root_password=local_db_source_config.password,
        settings=InstanceCreateSettings(backup_configuration=BackupConfiguration()),
    ),
    backup_instance_name="localhost",
    backup_project_name="local",
)

source_restore_instance = RestoreCloudSQLInstance(
    instance_client=CloudSQLInstanceClient(config=CloudSQLInstanceClientConfig()), config=target_instance_config
)
