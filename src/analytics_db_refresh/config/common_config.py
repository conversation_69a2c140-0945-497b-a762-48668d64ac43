from config.consts import DUMP_BASE_PATH
from refresh_shared.dump_db import DumpDb
from refresh_shared.restore_db import RestoreDb

from common.config import DEFAULT_CONFIG, MattermostAlertingConfig
from common.secret_client import get_secret
from common.time_service import TimeService

"""Common config for all environments."""

mm_secret = get_secret(
    secret_id=MattermostAlertingConfig.MATTERMOST_ALERTING_SECRET_ID, project_id=DEFAULT_CONFIG.project_id
)
mm_alerting_config = MattermostAlertingConfig.from_json(mm_secret)

refresh_time_service = TimeService()

dump_db = DumpDb(dump_base_path=DUMP_BASE_PATH, time_service=refresh_time_service)
restore_db = RestoreDb(dump_base_path=DUMP_BASE_PATH, time_service=refresh_time_service)
