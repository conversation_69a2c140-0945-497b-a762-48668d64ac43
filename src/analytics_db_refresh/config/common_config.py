import os
from pathlib import Path

from refresh_shared.dump_db import DumpDb
from refresh_shared.restore_db import RestoreDb

from common.cloud_sql_instance_client.instance_model import (
    DatabaseFlag,
    MaintenanceWindow,
)
from common.config import DEFAULT_CONFIG, MattermostAlertingConfig
from common.secret_client import get_secret
from common.time_service import TimeService

"""Common config for all environments."""

PLATFORM_SQL_INSTANCE = "platform-19af-15"
PLATFORM_PROJECT_NAME = "refb-platform-production"
ANALYTICS_SQL_INSTANCE = "analytics-08ce"
ANALYTICS_PROJECT_NAME = "refb-analytics"

DUMP_BASE_PATH = Path("/tmp/dumps/platform_dump")
SQL_MIGRATIONS_PATH = Path(os.environ.get("SQL_MIGRATIONS_PATH", "../sql-pg")).resolve()
SQL_COMMON_MIGRATIONS_PATH = Path("./sql/common").resolve()
SQL_SOURCE_MIGRATIONS_PATH = Path("./sql/source").resolve()
SQL_TARGET_MIGRATIONS_PATH = Path("./sql/target").resolve()

db_flags = [
    DatabaseFlag(name="autovacuum", value="off"),
]

db_maintenance_window = MaintenanceWindow(day=7, hour=20)

mm_secret = get_secret(
    secret_id=MattermostAlertingConfig.MATTERMOST_ALERTING_SECRET_ID, project_id=DEFAULT_CONFIG.project_id
)
mm_alerting_config = MattermostAlertingConfig.from_json(mm_secret)

refresh_time_service = TimeService()

dump_db = DumpDb(dump_base_path=DUMP_BASE_PATH, time_service=refresh_time_service)
restore_db = RestoreDb(dump_base_path=DUMP_BASE_PATH, time_service=refresh_time_service)
