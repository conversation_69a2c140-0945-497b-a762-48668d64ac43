import dataclasses

from config.base_config import (
    DEFAULT_CONFIG,
    SLEEP_SECONDS,
    SQL_MIGRATIONS_PATH,
    database_flags,
    maintenance_window,
)
from config.common_config import pg_params, refresh_time_service
from refresh_shared.migrate import FlywayParams
from refresh_shared.restore_instance import (
    RestoreCloudSQLInstance,
    RestoreCloudSQLInstanceConfig,
)

from common.cloud_sql_instance_client.instance_client import (
    CloudSQLInstanceClient,
    CloudSQLInstanceClientConfig,
)
from common.cloud_sql_instance_client.instance_model import (
    BackupConfiguration,
    CloudSQLCreateInstanceParams,
    InstanceCreateSettings,
    IpConfiguration,
)
from common.secret_client import get_secret
from common.sql_client import DatabaseConfig

"""Config for CICD"""

ANALYTICS_STAGING_SQL_INSTANCE = "analytics-0ad7e796"
ANALYTICS_STAGING_PROJECT_NAME = "refb-analytics-staging"

db_secret = get_secret("analytics-cloud-sql-config", DEFAULT_CONFIG.project_id)
db_config = DatabaseConfig.from_json(db_secret)

source_db_config = dataclasses.replace(
    db_config, host=db_config.host.replace(db_config.instance_name, "cicd-source-instance")
)
target_db_config = dataclasses.replace(
    db_config, host=db_config.host.replace(db_config.instance_name, "cicd-target-instance")
)

source_instance_params = CloudSQLCreateInstanceParams(
    name=source_db_config.instance_name,
    root_password=source_db_config.password,
    settings=InstanceCreateSettings(
        tier="db-custom-4-4096",  # 4 CPUs, 4 GB RAM
        data_disk_size_gb=15,
        backup_configuration=BackupConfiguration(),
        ip_configuration=IpConfiguration(),
        database_flags=database_flags,
        maintenance_window=maintenance_window,
    ),
)

source_restore_instance_config = RestoreCloudSQLInstanceConfig(
    new_instance_params=source_instance_params,
    backup_instance_name=ANALYTICS_STAGING_SQL_INSTANCE,
    backup_project_name=ANALYTICS_STAGING_PROJECT_NAME,
)

sql_instance_client_config = CloudSQLInstanceClientConfig(sleep_in_seconds=SLEEP_SECONDS)

source_restore_instance = RestoreCloudSQLInstance(
    instance_client=CloudSQLInstanceClient(config=sql_instance_client_config),
    config=source_restore_instance_config,
    time_service=refresh_time_service,
)

target_instance_params = CloudSQLCreateInstanceParams(
    name=target_db_config.instance_name,
    root_password=target_db_config.password,
    settings=InstanceCreateSettings(
        tier="db-custom-4-4096",  # 4 CPUs, 4 GB RAM
        data_disk_size_gb=15,
        backup_configuration=BackupConfiguration(),
        ip_configuration=IpConfiguration(),
        database_flags=database_flags,
        maintenance_window=maintenance_window,
    ),
)

target_restore_instance_config = RestoreCloudSQLInstanceConfig(
    new_instance_params=target_instance_params,
    backup_instance_name=ANALYTICS_STAGING_SQL_INSTANCE,
    backup_project_name=ANALYTICS_STAGING_PROJECT_NAME,
)

target_restore_instance = RestoreCloudSQLInstance(
    instance_client=CloudSQLInstanceClient(config=sql_instance_client_config),
    config=target_restore_instance_config,
    time_service=refresh_time_service,
)

flyway_params = FlywayParams(
    host=pg_params.host,
    port=source_db_config.port,
    database=source_db_config.database,
    user=source_db_config.username,
    password=source_db_config.password,
    config_files=f"{SQL_MIGRATIONS_PATH}/flyway.toml",
    locations=f"filesystem:{SQL_MIGRATIONS_PATH}",
    environment=DEFAULT_CONFIG.env,
)
