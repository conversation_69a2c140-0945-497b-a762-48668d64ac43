import time
from dataclasses import dataclass
from datetime import UTC, datetime
from typing import Optional

from config.base_config import SLEEP_SECONDS
from dataclasses_json import DataClassJsonMixin
from tenacity import retry

from common.cloud_sql_instance_client.instance_client import CloudSQLInstanceClient
from common.cloud_sql_instance_client.instance_model import (
    Backup,
    CloudSQLCreateInstanceParams,
    CloudSQLInstance,
    CloudSQLRestoreBackupParams,
    RestoreBackupContext,
)
from common.logger import get_logger
from common.retry_config import RETRY_CONFIG
from common.time_service import TimeService

logger = get_logger()


@dataclass(frozen=True)
class RestoreCloudSQLInstanceConfig(DataClassJsonMixin):
    new_instance_params: CloudSQLCreateInstanceParams
    backup_instance_name: str
    backup_project_name: str
    wait_for_backup_in_seconds: int = SLEEP_SECONDS
    timeout_in_seconds: int = 3600 * 4  # 4 hours


class RestoreCloudSQLInstanceError(ValueError):
    """Restore Cloud SQL Instance error"""


class RestoreCloudSQLInstance:
    """
    Creates empty Cloud SQL instance then restore latest today backup.
    Raises timeout exception when today backup not ready.
    """

    def __init__(
        self,
        instance_client: CloudSQLInstanceClient,
        config: RestoreCloudSQLInstanceConfig,
        time_service: Optional[TimeService] = None,
    ):
        self._instance_client = instance_client
        self._config = config
        self._time_service = time_service if time_service else TimeService()

    def run(self, should_wait: bool) -> CloudSQLInstance:
        """
        Restore Cloud SQL backup process flow.
        :param should_wait: whether to wait for today backup
        :returns: CloudSQLInstance object describing existing instance
        """
        instance = self.recreate_instance()
        backup = self.try_get_today_backup(should_wait)
        self.restore_backup_into_instance(instance, backup)

        return instance

    def recreate_instance(self) -> CloudSQLInstance:
        """
        Recreates Cloud SQL instance for given CloudSQLCreateInstanceParams.
        Waits for operation to complete.
        :returns: CloudSQLInstance object describing existing instance
        """
        new_instance_name = self._config.new_instance_params.name

        existing_instance = self._instance_client.describe_instance(new_instance_name)
        if existing_instance:
            logger.warning(f"Cloud SQL Instance '{new_instance_name}' already exists, dropping the instance...")
            self._instance_client.drop_instance(name=existing_instance.name, wait_for_completion=True)

        params = self._config.new_instance_params
        return self._instance_client.create_instance(params=params, wait_for_completion=True)

    def restore_backup_into_instance(self, instance: CloudSQLInstance, backup: Backup) -> None:
        """
        Restores Cloud SQL backup into existing Cloud SQL instance
        Waits for operation to complete.
        :param instance: CloudSQLInstance object describing existing instance
        :param backup: Backup object describing existing backup
        """

        backup_params = CloudSQLRestoreBackupParams(
            restore_backup_context=RestoreBackupContext(
                instance_id=self._config.backup_instance_name,
                project=self._config.backup_project_name,
                backup_run_id=backup.id,
            )
        )

        self._instance_client.restore_backup(
            instance_name=instance.name, backup_params=backup_params, wait_for_completion=True
        )

    def try_get_today_backup(self, should_wait: bool = True) -> Backup:
        """
        Tries to get the latest Cloud SQL backup from today
        While waiting, if backup doesn't exist, wait a predefined time until it is generated, otherwise break processing
        :param should_wait: whether to wait for today backup
        :raises RestoreCloudSQLInstanceError: while waiting for backup, when after predefined time it's still not ready
        :returns: Backup object describing existing backup
        """

        today = datetime.combine(date=self._time_service.today, time=datetime.min.time(), tzinfo=UTC)
        latest_backup = self._get_latest_backup()

        if should_wait:
            total_seconds = 0
            while latest_backup.end_time < today:
                logger.info(f"Found Cloud SQL latest backup from '{latest_backup.end_time}'.")
                logger.info(f"Waiting for Cloud SQL instance backup created later than '{today}'...")

                time.sleep(self._config.wait_for_backup_in_seconds)
                total_seconds = total_seconds + self._config.wait_for_backup_in_seconds

                if total_seconds >= self._config.timeout_in_seconds:
                    raise RestoreCloudSQLInstanceError(
                        f"Waiting for today backup more than {self._config.timeout_in_seconds} seconds, giving up..."
                    )

                latest_backup = self._get_latest_backup()

        logger.info(f"Got Cloud SQL instance today backup: '{latest_backup}'.")

        return latest_backup

    @retry(reraise=True, wait=RETRY_CONFIG)
    def describe_instance(self, instance_name: str) -> Optional[CloudSQLInstance]:
        """
        Describe instance, retry in case of error
        :param instance_name: Name of the Cloud SQL instance. This does not include the project ID
        """
        return self._instance_client.describe_instance(instance_name)

    def drop_instance(self, instance_name: str) -> None:
        """
        Drops Cloud SQL instance if exists
        Does not wait for completion.
        :param instance_name: Name of the Cloud SQL instance. This does not include the project ID
        """
        instance = self.describe_instance(instance_name)
        if instance:
            self._instance_client.drop_instance(name=instance.name, wait_for_completion=False)

    # @retry(reraise=True, wait=RETRY_CONFIG)
    def _get_latest_backup(self) -> Backup:
        """
        Get latest successful Cloud SQL backup
        :returns: Backup object describing existing backup
        """

        return self._instance_client.get_latest_backup(
            instance_name=self._config.backup_instance_name, project_id=self._config.backup_project_name
        )
