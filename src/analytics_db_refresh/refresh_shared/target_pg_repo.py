from dataclasses import dataclass
from typing import Optional

from config.base_config import SLEEP_SECONDS
from dataclasses_json import DataClassJsonMixin
from sqlalchemy import text

from common.logger import get_logger
from common.sql_model import SqlFunction, SqlProcedure
from common.sql_repository import SqlRepository
from common.timing import background_task

logger = get_logger()


@dataclass(frozen=True, slots=True)
class TableCount(DataClassJsonMixin):
    schema_name: str
    table_name: str
    platform_db: Optional[int]
    analytics_db: Optional[int]
    diff: int

    @classmethod
    def fields(cls) -> list[str]:
        """List of class fields"""

        return list(TableCount.__annotations__)


class TargetPgRepository(SqlRepository):
    """
    Analytics DB refresh PostgreSQL target repo
    """

    def create_schemas(self, schemas: list[str]) -> None:
        """
        Creates database schemas.
        """
        for schema in schemas:
            self.create_schema(schema)

    def revert_routines_schemas(self, schemas: list[str], suffix: str) -> None:
        """
        Reverts routines schemas to the initial name.
        Needed for blue & green refresh.
        """

        for schema in schemas:
            new_schema_name = schema.replace(suffix, "")
            logger.info(f"Reverting routines schema '{schema}' to '{new_schema_name}'...")
            self.call(
                procedure=SqlProcedure(schema_name="maintenance", procedure_name="rename_routines_schema"),
                arguments=f"source_schema=>'{schema}', target_schema=>'{new_schema_name}'",
            )

    def drop_schemas(self, schemas: list[str]) -> None:
        """
        Drops database schemas.
        """
        for schema in schemas:
            self.drop_schema(schema, cascade=True)

    def lock_non_system_users(self) -> None:
        """
        Locks all non system users.
        """
        logger.info("Locking all non system users...")
        self.call(procedure=SqlProcedure(schema_name="maintenance", procedure_name="lock_non_system_users"))

    def unlock_non_system_users(self) -> None:
        """
        Unlocks all non system users.
        """
        logger.info("Unlocking all non system users...")
        self.call(procedure=SqlProcedure(schema_name="maintenance", procedure_name="unlock_non_system_users"))

    def kill_all_running_sessions(self) -> None:
        """
        Kills all running sessions.
        """
        sql = text("select distinct pg_terminate_backend(session_id) from monitoring.who_is_active();")
        logger.info("Killing all running sessions...")
        self._run_sql(sql)

    @background_task(logger, sleep_seconds=SLEEP_SECONDS)
    def refresh_materialized_views(self, views: list[str]) -> None:
        """
        Refreshes given materialized views.
        """
        for view in views:
            logger.info(f"Refreshing materialized {view=}...")
            self._run_sql(text(f"refresh materialized view {view};"))

    def load_platform_tables_count(self) -> None:
        """
        Loads table count statistics from the platform database into tracking table.
        """
        logger.info("Getting tables count statistics for platform db...")
        self.call(
            procedure=SqlProcedure(schema_name="maintenance", procedure_name="load_platform_tables_count"),
        )

    @background_task(logger, sleep_seconds=SLEEP_SECONDS)
    def load_analytics_tables_count(self, run_id: str, schemas: list[str]) -> None:
        """
        Loads table count statistics from the analytics database into tracking table.
        :param run_id: refresh db unique run id
        :param schemas: schemas for which tables count will be persisted
        """
        schemas_arg = ", ".join([f"'{schema}'" for schema in schemas])

        logger.info(f"Getting tables count statistics for analytics db schemas: {schemas_arg}...")
        self.call(
            procedure=SqlProcedure(schema_name="maintenance", procedure_name="load_analytics_tables_count"),
            arguments=f"run_id => '{run_id}', schemas => array [{schemas_arg}]",
        )

    def get_refresh_tables_summary(self, run_id: str, show_diff_only: bool) -> list[TableCount]:
        """
        Gets tables count summary
        :param run_id: refresh db unique run id
        :param show_diff_only: whether to get tables only with different counts
        :returns: list of tables count
        """

        return [
            TableCount.from_dict(rows._mapping)  # noqa
            for rows in self.select_from_function(
                function=SqlFunction(schema_name="maintenance", function_name="get_refresh_tables_summary"),
                args=[run_id, show_diff_only],
            )
        ]

    @background_task(logger, sleep_seconds=SLEEP_SECONDS)
    def vacuum_database(self) -> None:
        """
        Vacuum database, adding @background_task with custom SLEEP_IN_SEC.
        """
        super().vacuum_database(do_analyze=True)
