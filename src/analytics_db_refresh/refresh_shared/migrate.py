from dataclasses import dataclass, field
from functools import cached_property
from typing import Optional

import sh
from dataclasses_json import DataClassJsonMixin
from sh import ErrorReturnCode

from common.logger import get_logger
from common.utils import to_camel_case

logger = get_logger()


@dataclass(unsafe_hash=True)
class FlywayDatabase(DataClassJsonMixin):
    host: str
    port: int
    database: str


@dataclass(unsafe_hash=True)
class FlywayParams(FlywayDatabase):
    """
    Flyway params
    Ref. https://documentation.red-gate.com/flyway/flyway-cli-and-api/configuration/parameters
    """

    config_files: str
    locations: str
    environment: str
    user: str
    password: str
    url: str = field(init=False)
    placeholders: Optional[list[str]] = None

    def __post_init__(self) -> None:
        self.url = f"jdbc:postgresql://{self.host}:{self.port}/{self.database}"

    @cached_property
    def command_params(self) -> str:
        """
        Prepares Flyway command line params
        """
        params = []
        for key, value in self.to_dict().items():
            if key not in FlywayDatabase.__annotations__.keys():
                if key == "placeholders":
                    if self.placeholders:
                        for val in value:
                            k, v = val.split("=")
                            params.append(f"-{to_camel_case(key)}.{k}={v}")
                elif key == "password":
                    # See https://github.com/flyway/flyway/issues/1670#issuecomment-309207197
                    params.append(f"'-{to_camel_case(key)}={value}'")
                else:
                    params.append(f"-{to_camel_case(key)}={value}")

        return " ".join(params)


def migrate_db(params: FlywayParams, flyway_command: str = "repair migrate") -> None:
    """
    Migrates DB by running Flyway migrations.
    :param params: see FlywayParams
    :param flyway_command: flyway command to run
    """

    flyway_command = f"flyway {flyway_command} {params.command_params}"

    logger.info(f"Running SQL migrations on the '{params.url}' instance...")
    logger.info(f"Flyway command: '{flyway_command}'")

    try:
        sh.sh("-c", flyway_command)  # noqa
    except ErrorReturnCode as e:
        logger.error(f"Flyway error(s): {str(e.stderr)}")
        raise
