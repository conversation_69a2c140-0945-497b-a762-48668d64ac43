import functions_framework
from cloudevents.http import Cloud<PERSON>vent
from google.cloud.sql.connector import Connector
from workflow_shared.local_execution import get_test_execution
from workflow_shared.workflow_model import WorkflowExecution

from common.cloud_events import create_http_event, get_event_message
from common.config import Config
from common.consts import ANALYTICS_CLOUD_SQL_SECRET_ID
from common.logger import setup_logger
from common.pipeline_logging.distributed_logger import DistributedPipelineLogger
from common.pipeline_logging.pipeline_model import PipelineRun
from common.secret_client import get_secret
from common.sql_client import DatabaseConfig
from common.sql_repository import SqlRepository
from common.timing import timing
from common.typings import APPLICATION_JSON_HEADER, HttpResponse

config = Config()
logger = setup_logger(config)

pg_secret = get_secret(ANALYTICS_CLOUD_SQL_SECRET_ID, config.project_id)
pg_config = DatabaseConfig.from_json(pg_secret)


@timing(logger, "workflow-starter")
@functions_framework.http
def run(request: CloudEvent) -> HttpResponse:
    """
    Workflow starter, called from workflows, which support pipeline execution logging.
    """

    event = get_event_message(request)
    logger.debug(f"Invoked with {event=}")

    execution = WorkflowExecution.from_json(event)
    logger.info(f"Workflow {execution=}")

    if DistributedPipelineLogger.supports_pipeline_logging(execution):
        with Connector() as connector:
            repository = SqlRepository(db_config=pg_config, connector=connector)
            starter = DistributedPipelineLogger(execution, repository)
            pipeline_run = starter.start_logging()
    else:
        # Return empty run for non-monitored pipelines
        pipeline_run = PipelineRun(
            pipeline_name="",
            run_id="",
            log_id=0,
        )

    return HttpResponse(body=pipeline_run.to_json(), headers=APPLICATION_JSON_HEADER)


if __name__ == "__main__":
    # Overwrite db_config with local docker db to test your SQL before releasing to staging
    pg_config = DatabaseConfig()

    test_execution = get_test_execution()
    run(create_http_event(test_execution.to_json()))
