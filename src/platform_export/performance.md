# Performance of platform jobs

## 2025.05.29 Platform Jobs Timing

Test run from `staging` environment using `analytics-db-refresh` cloud-sql as a source.

- 1st run (27.05): full export after `truncate` of all target BQ tables;
- 2nd run (28.05): incremental export;
- 3rd run (29.05):
  - `order_item_offers` changed to snapshot due to data discrepancies;
  - `order_item_offers` assigned to Large job size;
  - `EXPORT_CHUNK_SIZE` increased to 1,000,000;
  - [00:31:38.01s] in medium vs [00:27:32.01s] in large job size.

| Job Size | BQ Table                      | Export Type | 1st [hh:mm:ss.ff]  | 2nd [hh:mm:ss.ff]  |
|----------|-------------------------------|-------------|--------------------|--------------------|
| Large    | offers                        | time travel | **[03:06:38.01s]** | **[00:06:37.01s]** |
| Large    | order_items                   | incremental | **[00:51:15.01s]** | [00:00:22.01s]     |
| Medium   | order_item_offers             | incremental | **[00:32:30.01s]** | [00:00:18.01s]     |
| Large    | orders                        | incremental | **[00:26:43.01s]** | [00:00:42.01s]     |
| Medium   | order_financial_revenue       | snapshot    | [00:09:04.01s]     | **[00:08:12.01s]** |
| Medium   | order_item_exchange_rate      | snapshot    | [00:02:21.01s]     | [00:02:33.01s]     |
| Medium   | full_order_item_refunds       | snapshot    | [00:01:42.01s]     | [00:01:36.01s]     |
| Small    | offer_properties              | snapshot    | [00:00:57.01s]     | [00:00:56.01s]     |
| Small    | instance_attribute_values     | snapshot    | [00:00:37.01s]     | [00:00:34.01s]     |
| Medium   | instances                     | incremental | [00:00:28.01s]     | [00:00:09.01s]     |
| Small    | attributes                    | snapshot    | [00:00:18.01s]     | [00:00:15.01s]     |
| Small    | exchange_rates                | time travel | [00:00:14.01s]     | [00:00:07.01s]     |
| Small    | product_attribute_values      | snapshot    | [00:00:11.01s]     | [00:00:11.01s]     |
| Small    | shipping_profile_destinations | time travel | [00:00:09.01s]     | [00:00:07.01s]     |
| Small    | shipping_profiles             | time travel | [00:00:08.01s]     | [00:00:06.01s]     |
| Small    | product_categories            | snapshot    | [00:00:08.01s]     | [00:00:07.01s]     |
| Small    | merchants                     | incremental | [00:00:06.01s]     | [00:00:06.01s]     |
| Small    | categories                    | incremental | [00:00:06.01s]     | [00:00:05.01s]     |
| Small    | countries                     | snapshot    | [00:00:05.01s]     | [00:00:06.01s]     |
| Small    | products                      | incremental | [00:00:08.01s]     | [00:00:06.01s]     |

## 2025.05.23 Platform Jobs Timing

Cloud run jobs timing ordered by Time descending.

| Job Size | BQ Table                      | Export Type | Time [mm:ss.ff] |
|----------|-------------------------------|-------------|-----------------|
| Medium   | order_item_offers             | snapshot    | **42:03.01**    |
| Medium   | order_financial_revenue       | snapshot    | **09:05.01**    |
| Medium   | full_order_item_refunds       | snapshot    | 01:33.01        |
| Medium   | order_item_exchange_rate      | snapshot    | 02:30.01        |
| Large    | offers                        | time travel | 06:31.01        |
| Large    | order_items                   | incremental | 00:20.01        |
| Large    | orders                        | incremental | 00:17.01        |
| Medium   | instances                     | incremental | 00:08.01        |
| Small    | offer_properties              | snapshot    | 00:54.01        |
| Small    | instance_attribute_values     | snapshot    | 00:33.01        |
| Small    | attributes                    | snapshot    | 00:14.01        |
| Small    | product_attribute_values      | snapshot    | 00:10.01        |
| Small    | product_categories            | snapshot    | 00:07.01        |
| Small    | shipping_profile_destinations | time travel | 00:07.01        |
| Small    | exchange_rates                | time travel | 00:07.01        |
| Small    | categories                    | incremental | 00:05.01        |
| Small    | shipping_profiles             | time travel | 00:05.01        |
| Small    | countries                     | snapshot    | 00:05.01        |
| Small    | merchants                     | incremental | 00:05.01        |
| Small    | products                      | incremental | 00:05.01        |

### Legend

**Large jobs:**

- [platform-export-large-job](https://console.cloud.google.com/run/jobs/details/europe-west3/platform-export-large-job/executions?hl=en&inv=1&invt=AbyIzw&project=refb-analytics)
- RAM: 16 GiB
- CPU: 4

**Medium jobs:**

- [platform-export-medium-job](https://console.cloud.google.com/run/jobs/details/europe-west3/platform-export-medium-job/executions?hl=en&inv=1&invt=AbyIzw&project=refb-analytics)
- RAM: 4 GiB
- CPU: 1

**Small jobs:**

- [platform-export-small-job](https://console.cloud.google.com/run/jobs/details/europe-west3/platform-export-small-job/executions?hl=en&inv=1&invt=AbyIzw&project=refb-analytics)
- RAM: 2 GiB
- CPU: 1

## 2025.03.05 Performance testing of extract (as a cloud run function)

### RAM: 8GB, CPU: 2, no VPC

- chunk_size=500000, max_row_buffer=1000 => [00:24:41.01s]
- chunk_size=500000, max_row_buffer=500000 => [00:22:05.01s]
- chunk_size=500000, max_row_buffer=500000 => [00:21:49.01s] (second run)
- chunk_size=100000, max_row_buffer=100000 => [00:25:48.01s]
- chunk_size=1000000,max_row_buffer=1000000 => [00:29:05.01s]
- chunk_size=500000, max_row_buffer=1000000 => [00:28:57.01s]
- chunk_size=2000000,max_row_buffer=2000000 => [00:20:59.01s]

### RAM: 16GB, CPU: 4, no VPC

- chunk_size=2000000,max_row_buffer=2000000 => [00:20:14.01s]

### RAM: 8GB, CPU: 2, VPC: direct, route all traffic to the VPC

- chunk_size=500000, max_row_buffer=500000 => [00:23:24.01s]
- chunk_size=1000000,max_row_buffer=1000000 => [00:19:36.01s]

### RAM: 8GB, CPU: 2, no VPC, pool_size: = 10

- chunk_size=500000, max_row_buffer=500000 => [00:23:53.01s]

### RAM: 8GB, CPU: 2, no VPC, no analytics.order_financial_revenue

- chunk_size=500000, max_row_buffer=500000 => [00:20:38.01s]

## 2025.03.07 Performance testing of extract and load (as cloud run job)

### RAM: 8GB, CPU: 2, no VPC

- chunk_size=500000, max_row_buffer=500000 => [00:33:09.01s]
