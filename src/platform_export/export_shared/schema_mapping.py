"""
SOURCE Postgres export view to TARGET BigQuery table and schema mapping.
"""

from export_shared.export_model import ExportSchema
from export_shared.schema_incremental import (
    CATEGORIES_SCHEMA,
    INSTANCES_SCHEMA,
    MERCHANTS_SCHEMA,
    ORDER_ITEMS_SCHEMA,
    ORDER_REFUNDS_SCHEMA,
    ORDERS_SCHEMA,
    PRODUCTS_SCHEMA,
    USERS_SCHEMA,
)
from export_shared.schema_snapshot import (
    ATTRIBUTES_SCHEMA,
    COUNTRIES_SCHEMA,
    FULL_ORDER_ITEM_REFUNDS_SCHEMA,
    INSTANCE_ATTRIBUTE_VALUES_SCHEMA,
    MERCHANT_ORDERS_SCHEMA,
    OFFER_PROPERTIES_SCHEMA,
    ORDER_FINANCIAL_REVENUE_SCHEMA,
    ORDER_ITEM_EXCHANGE_RATE_SCHEMA,
    ORDER_ITEM_OFFERS_SCHEMA,
    ORDER_ITEM_REFUNDS_SCHEMA,
    PARTNERS_SCHEMA,
    PRODUCT_ATTRIBUTE_VALUES_SCHEMA,
    PRODUCT_CATEGORIES_SCHEMA,
    PRODUCT_RANKINGS_SCHEMA,
)
from export_shared.schema_time_travel import (
    ADDRESSES_SCHEMA,
    EXCHANGE_RATES_SCHEMA,
    OFFERS_SCHEMA,
    ORDER_DOCUMENTS_SCHEMA,
    ORDER_ITEM_DETAILS_SCHEMA,
    SHIPPING_PROFILE_DESTINATIONS_SCHEMA,
    SHIPPING_PROFILES_SCHEMA,
)
from typing_extensions import NamedTuple

from common.sql_model import SqlTable

# src/sql-pg/schema/views/R__3020_export_products.sql
PRODUCTS_SOURCE = SqlTable(schema_name="export_etl", table_name="v_products")
PRODUCTS_TARGET = "products"

# src/sql-pg/schema/views/R__3021_export_categories.sql
CATEGORIES_SOURCE = SqlTable(schema_name="export_etl", table_name="v_categories")
CATEGORIES_TARGET = "categories"

# src/sql-pg/schema/views/R__3022_export_merchants.sql
MERCHANTS_SOURCE = SqlTable(schema_name="export_etl", table_name="v_merchants")
MERCHANTS_TARGET = "merchants"

# src/sql-pg/schema/views/R__3023_export_instances.sql
INSTANCES_SOURCE = SqlTable(schema_name="export_etl", table_name="v_instances")
INSTANCES_TARGET = "instances"

# src/sql-pg/schema/views/R__3024_export_exchange_rates.sql
EXCHANGE_RATES_SOURCE = SqlTable(schema_name="export_etl", table_name="v_exchange_rates")
EXCHANGE_RATES_TARGET = "exchange_rates"

# src/sql-pg/schema/views/R__3025_export_attributes.sql
ATTRIBUTES_SOURCE = SqlTable(schema_name="export_etl", table_name="v_attributes")
ATTRIBUTES_TARGET = "attributes"

# src/sql-pg/schema/views/R__3026_export_instance_attribute_values.sql
INSTANCE_ATTRIBUTE_VALUES_SOURCE = SqlTable(schema_name="export_etl", table_name="v_instance_attribute_values")
INSTANCE_ATTRIBUTE_VALUES_TARGET = "instance_attribute_values"

# src/sql-pg/schema/views/R__3027_export_product_attribute_values.sql
PRODUCT_ATTRIBUTE_VALUES_SOURCE = SqlTable(schema_name="export_etl", table_name="v_product_attribute_values")
PRODUCT_ATTRIBUTE_VALUES_TARGET = "product_attribute_values"

# src/sql-pg/schema/views/R__3028_export_users.sql
USERS_SOURCE = SqlTable(schema_name="export_etl", table_name="v_users")
USERS_TARGET = "users"

# src/sql-pg/schema/views/R__3029_export_partners.sql
PARTNERS_SOURCE = SqlTable(schema_name="export_etl", table_name="v_partners")
PARTNERS_TARGET = "partners"

# src/sql-pg/schema/views/R__3030_export_addresses.sql
ADDRESSES_SOURCE = SqlTable(schema_name="export_etl", table_name="v_addresses")
ADDRESSES_TARGET = "addresses"

# src/sql-pg/schema/views/R__3031_export_shipping_profiles.sql
SHIPPING_PROFILES_SOURCE = SqlTable(schema_name="export_etl", table_name="v_shipping_profiles")
SHIPPING_PROFILES_TARGET = "shipping_profiles"

# src/sql-pg/schema/views/R__3032_export_shipping_profile_destinations.sql
SHIPPING_PROFILE_DESTINATIONS_SOURCE = SqlTable(schema_name="export_etl", table_name="v_shipping_profile_destinations")
SHIPPING_PROFILE_DESTINATIONS_TARGET = "shipping_profile_destinations"

# src/sql-pg/schema/views/R__3057_export_order_item_offers.sql
ORDER_ITEM_OFFERS_SOURCE = SqlTable(schema_name="export_etl", table_name="v_order_item_offers")
ORDER_ITEM_OFFERS_TARGET = "order_item_offers"

# src/sql-pg/schema/views/R__3040_export_offers.sql
OFFERS_SOURCE = SqlTable(schema_name="export_etl", table_name="v_offers")
OFFERS_TARGET = "offers"

# sql-pg/schema/views/R__3041_export_orders.sql
ORDERS_SOURCE = SqlTable(schema_name="export_etl", table_name="v_orders")
ORDERS_TARGET = "orders"

# src/sql-pg/schema/views/R__3042_export_order_items.sql
ORDER_ITEMS_SOURCE = SqlTable(schema_name="export_etl", table_name="v_order_items")
ORDER_ITEMS_TARGET = "order_items"

# src/sql-pg/schema/views/R__3044_export_order_item_details.sql
ORDER_ITEM_DETAILS_SOURCE = SqlTable(schema_name="export_etl", table_name="v_order_item_details")
ORDER_ITEM_DETAILS_TARGET = "order_item_details"

# src/sql-pg/schema/views/R__3046_export_order_item_refunds.sql
ORDER_ITEM_REFUNDS_SOURCE = SqlTable(schema_name="export_etl", table_name="v_order_item_refunds")
ORDER_ITEM_REFUNDS_TARGET = "order_item_refunds"

# src/sql-pg/migrations/V37__add_order_item_exchange_rate.sql
ORDER_ITEM_EXCHANGE_RATE_SOURCE = SqlTable(schema_name="analytics", table_name="order_item_exchange_rate")
ORDER_ITEM_EXCHANGE_RATE_TARGET = "order_item_exchange_rate"

# src/sql-pg/schema/views/R__3045_export_order_refunds.sql
ORDER_REFUNDS_SOURCE = SqlTable(schema_name="export_etl", table_name="v_order_refunds")
ORDER_REFUNDS_TARGET = "order_refunds"

# src/sql-pg/schema/views/R__3047_export_order_documents.sql
ORDER_DOCUMENTS_SOURCE = SqlTable(schema_name="export_etl", table_name="v_order_documents")
ORDER_DOCUMENTS_TARGET = "order_documents"

# src/sql-pg/schema/views/R__3008_order_financial_revenue.sql
ORDER_FINANCIAL_REVENUE_SOURCE = SqlTable(schema_name="analytics", table_name="order_financial_revenue")
ORDER_FINANCIAL_REVENUE_TARGET = "order_financial_revenue"

# src/sql-pg/migrations/V43__refactor_order_item_refund_commission.sql
FULL_ORDER_ITEM_REFUNDS_SOURCE = SqlTable("analytics", "full_order_item_refunds")
FULL_ORDER_ITEM_REFUNDS_TARGET = "full_order_item_refunds"

# src/sql-pg/schema/views/R__3048_export_merchant_orders.sql
MERCHANT_ORDERS_SOURCE = SqlTable(schema_name="export_etl", table_name="v_merchant_orders")
MERCHANT_ORDERS_TARGET = "merchant_orders"

# src/sql-pg/schema/views/R__3013_export_countries.sql
COUNTRIES_SOURCE = SqlTable(schema_name="export_etl", table_name="v_countries")
COUNTRIES_TARGET = "countries"

# src/sql-pg/schema/views/R__3055_export_product_categories.sql
PRODUCT_CATEGORIES_SOURCE = SqlTable(schema_name="export_etl", table_name="v_product_categories")
PRODUCT_CATEGORIES_TARGET = "product_categories"

# src/sql-pg/migrations/V44__offer_properties.sql
OFFER_PROPERTIES_SOURCE = SqlTable(schema_name="analytics", table_name="offer_properties")
OFFER_PROPERTIES_TARGET = "offer_properties"

# src/sql-pg/schema/views/R__3058_export_product_rankings.sql
PRODUCT_RANKINGS_SOURCE = SqlTable(schema_name="export_etl", table_name="v_product_rankings")
PRODUCT_RANKINGS_TARGET = "product_rankings"


class TargetSchema(NamedTuple):
    schema: ExportSchema
    table_name: str


# Mapping of source PG SQL views to their target schema and BQ table
SOURCE_TO_TARGET: dict[SqlTable, TargetSchema] = {
    PRODUCTS_SOURCE: TargetSchema(PRODUCTS_SCHEMA, PRODUCTS_TARGET),
    CATEGORIES_SOURCE: TargetSchema(CATEGORIES_SCHEMA, CATEGORIES_TARGET),
    MERCHANTS_SOURCE: TargetSchema(MERCHANTS_SCHEMA, MERCHANTS_TARGET),
    INSTANCES_SOURCE: TargetSchema(INSTANCES_SCHEMA, INSTANCES_TARGET),
    EXCHANGE_RATES_SOURCE: TargetSchema(EXCHANGE_RATES_SCHEMA, EXCHANGE_RATES_TARGET),
    ATTRIBUTES_SOURCE: TargetSchema(ATTRIBUTES_SCHEMA, ATTRIBUTES_TARGET),
    INSTANCE_ATTRIBUTE_VALUES_SOURCE: TargetSchema(INSTANCE_ATTRIBUTE_VALUES_SCHEMA, INSTANCE_ATTRIBUTE_VALUES_TARGET),
    PRODUCT_ATTRIBUTE_VALUES_SOURCE: TargetSchema(PRODUCT_ATTRIBUTE_VALUES_SCHEMA, PRODUCT_ATTRIBUTE_VALUES_TARGET),
    OFFER_PROPERTIES_SOURCE: TargetSchema(OFFER_PROPERTIES_SCHEMA, OFFER_PROPERTIES_TARGET),
    USERS_SOURCE: TargetSchema(USERS_SCHEMA, USERS_TARGET),
    PARTNERS_SOURCE: TargetSchema(PARTNERS_SCHEMA, PARTNERS_TARGET),
    ADDRESSES_SOURCE: TargetSchema(ADDRESSES_SCHEMA, ADDRESSES_TARGET),
    SHIPPING_PROFILES_SOURCE: TargetSchema(SHIPPING_PROFILES_SCHEMA, SHIPPING_PROFILES_TARGET),
    SHIPPING_PROFILE_DESTINATIONS_SOURCE: TargetSchema(
        SHIPPING_PROFILE_DESTINATIONS_SCHEMA, SHIPPING_PROFILE_DESTINATIONS_TARGET
    ),
    ORDER_ITEM_OFFERS_SOURCE: TargetSchema(ORDER_ITEM_OFFERS_SCHEMA, ORDER_ITEM_OFFERS_TARGET),
    OFFERS_SOURCE: TargetSchema(OFFERS_SCHEMA, OFFERS_TARGET),
    ORDERS_SOURCE: TargetSchema(ORDERS_SCHEMA, ORDERS_TARGET),
    ORDER_ITEMS_SOURCE: TargetSchema(ORDER_ITEMS_SCHEMA, ORDER_ITEMS_TARGET),
    ORDER_ITEM_DETAILS_SOURCE: TargetSchema(ORDER_ITEM_DETAILS_SCHEMA, ORDER_ITEM_DETAILS_TARGET),
    ORDER_ITEM_EXCHANGE_RATE_SOURCE: TargetSchema(ORDER_ITEM_EXCHANGE_RATE_SCHEMA, ORDER_ITEM_EXCHANGE_RATE_TARGET),
    ORDER_REFUNDS_SOURCE: TargetSchema(ORDER_REFUNDS_SCHEMA, ORDER_REFUNDS_TARGET),
    ORDER_ITEM_REFUNDS_SOURCE: TargetSchema(ORDER_ITEM_REFUNDS_SCHEMA, ORDER_ITEM_REFUNDS_TARGET),
    ORDER_DOCUMENTS_SOURCE: TargetSchema(ORDER_DOCUMENTS_SCHEMA, ORDER_DOCUMENTS_TARGET),
    ORDER_FINANCIAL_REVENUE_SOURCE: TargetSchema(ORDER_FINANCIAL_REVENUE_SCHEMA, ORDER_FINANCIAL_REVENUE_TARGET),
    FULL_ORDER_ITEM_REFUNDS_SOURCE: TargetSchema(FULL_ORDER_ITEM_REFUNDS_SCHEMA, FULL_ORDER_ITEM_REFUNDS_TARGET),
    MERCHANT_ORDERS_SOURCE: TargetSchema(MERCHANT_ORDERS_SCHEMA, MERCHANT_ORDERS_TARGET),
    COUNTRIES_SOURCE: TargetSchema(COUNTRIES_SCHEMA, COUNTRIES_TARGET),
    PRODUCT_CATEGORIES_SOURCE: TargetSchema(PRODUCT_CATEGORIES_SCHEMA, PRODUCT_CATEGORIES_TARGET),
    PRODUCT_RANKINGS_SOURCE: TargetSchema(PRODUCT_RANKINGS_SCHEMA, PRODUCT_RANKINGS_TARGET),
}
