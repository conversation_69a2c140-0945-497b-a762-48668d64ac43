from export_shared.export_model import ExportStatus, PartitionValues
from google.api_core.exceptions import BadRequest

from common.bq_repository import BigQueryRepository, BQProcedure
from common.logger import get_logger

logger = get_logger()


class AnalyticsBQRepository(BigQueryRepository):
    """
    Analytics BQ repository for loading tables exported from Postgres platform database.
    """

    DATASET = "analytics_transformed"
    TABLE = "platform_export_status"

    PLATFORM_EXPORT_STATUS = BQProcedure(DATASET, TABLE)

    def get_export_status(self, table: str) -> ExportStatus:
        """
        Gets platform export status of the given `table` returning:
        - last_id: last loaded `id`
        - last_modified: last `modified_at` timestamp
        - default to ChangeTracking() default values if target table was empty and returned Nulls

        :param table: platform table being checked
        :returns: change tracking object
        """
        result = self.call_procedure(self.PLATFORM_EXPORT_STATUS, [table])

        if (rows := result.rows) and len(rows) == 1:
            row = rows[0]
            default = ExportStatus()

            return ExportStatus(
                last_id=row.last_id if row.last_id else default.last_id,
                last_modified=row.last_modified if row.last_modified else default.last_modified,
            )
        else:
            raise ValueError(
                f"'{self.PLATFORM_EXPORT_STATUS}' returned no rows! "
                f"Possibly '{table}' is not supported by platform export?!"
            )

    def load_from_raw(self, table_name: str, pv: PartitionValues) -> int:
        """
        Loads given `analytics_transformed` target table:
        - from the `analytics_raw` corresponding source
        - for the given partition values
        - using corresponding `load` stored procedure

        Example call for `products` table:
        `call analytics_transformed.load_products (2024, 7, 3, 1719993196);`

        :param table_name: table being loaded
        :param pv: partition values
        :returns: total bytes processed
        """
        procedure = BQProcedure(self.DATASET, f"load_{table_name}")
        result = self.call_procedure(procedure, [pv.year, pv.month, pv.day, pv.timestamp])

        return result.total_bytes_processed

    def check_table(self, table_name: str) -> bool:
        """
        Checks table for data anomalies.

        :param table_name: table with data already loaded
        :returns: true if table has no anomalies, false otherwise
        """
        procedure = BQProcedure(self.DATASET, f"check_{table_name}")
        try:
            result = self.call_procedure(procedure)
        except BadRequest as ex:
            logger.warning(f"Quality check failed for {table_name=}: {ex.errors}")
            return False

        return result.total_rows == 0
