"""
Incremental table schemas definitions:
- tables that enable change tracking by timestamp columns;
- expected columns: created_at, updated_at, deleted_at;

`export_type: ExportType = ExportType.INCREMENTAL`
"""

from db_dtypes import JSONDtype
from export_shared.export_model import ExportSchema
from pandas import BooleanDtype, Float32Dtype, Int32Dtype, StringDtype

DATE_COLUMNS = ["created_at", "updated_at", "deleted_at"]

PRODUCTS_SCHEMA = ExportSchema(
    date_columns=DATE_COLUMNS,
    data_types={
        "id": Int32Dtype(),
        "category_id": Int32Dtype(),
        "listing_mode": StringDtype(),
        "name": StringDtype(),
        "name_en": StringDtype(),
        "slug": StringDtype(),
    },
)

CATEGORIES_SCHEMA = ExportSchema(
    date_columns=DATE_COLUMNS,
    data_types={
        "id": Int32Dtype(),
        "product_category": StringDtype(),
        "brand": StringDtype(),
    },
)

MERCHANTS_SCHEMA = ExportSchema(
    date_columns=DATE_COLUMNS,
    data_types={
        "id": Int32Dtype(),
        "country": StringDtype(),
        "state": StringDtype(),
        "is_addon_support": BooleanDtype(),
        "name": StringDtype(),
        "email": StringDtype(),
        "public_id": StringDtype(),
        "handicap": Float32Dtype(),
        "payment_commission_pc": Float32Dtype(),
        "payout_commission_pc": Float32Dtype(),
    },
)

INSTANCES_SCHEMA = ExportSchema(
    date_columns=DATE_COLUMNS,
    data_types={
        "id": Int32Dtype(),
        "published": BooleanDtype(),
        "product_id": Int32Dtype(),
        "name": StringDtype(),
        "name_en": StringDtype(),
        "srp": Float32Dtype(),
    },
)

USERS_SCHEMA = ExportSchema(
    date_columns=DATE_COLUMNS,
    data_types={
        "id": Int32Dtype(),
        "email": StringDtype(),
        "first_name": StringDtype(),
        "family_name": StringDtype(),
        "type": StringDtype(),
        "language": StringDtype(),
        "merchant_id": Int32Dtype(),
    },
)

ORDERS_SCHEMA = ExportSchema(
    date_columns=DATE_COLUMNS + ["paid_at", "invoice_addr_valid_from", "shipping_addr_valid_from"],
    data_types={
        "id": Int32Dtype(),
        "country": StringDtype(),
        "state": StringDtype(),
        "is_released": BooleanDtype(),
        "user_id": Int32Dtype(),
        "partner_id": Int32Dtype(),
        "invoice_addr_id": Int32Dtype(),
        "shipping_addr_id": Int32Dtype(),
        "presentment_currency": StringDtype(),
        "settlement_currency": StringDtype(),
        "presentment_to_settlement_exchange_rate": Float32Dtype(),
        "payment_provider": StringDtype(),
        "payment_info": StringDtype(),
        "payment_handle": StringDtype(),
        "payment_provider_category": StringDtype(),
        "payment_provider_info": JSONDtype(),
    },
)

ORDER_ITEMS_SCHEMA = ExportSchema(
    date_columns=DATE_COLUMNS
    + [
        "offer_valid_from",
        "offer_price_valid_from",
        "addon_valid_from",
        "shipping_profile_destination_valid_from",
    ],
    data_types={
        "id": Int32Dtype(),
        "order_id": Int32Dtype(),
        "offer_id": Int32Dtype(),
        "offer_price_id": Int32Dtype(),
        "type": StringDtype(),
        "is_addon": BooleanDtype(),
        "charged": Float32Dtype(),
        "discount": Float32Dtype(),
        "net": BooleanDtype(),
        "vat": Float32Dtype(),
        "vat_country": StringDtype(),
        "price": Float32Dtype(),
        "flex_price": Float32Dtype(),
        "addon_id": Int32Dtype(),
        "addon_assigned_order_item_id": Int32Dtype(),
        "addon_details": JSONDtype(),
        "shipping_profile_destination_id": Int32Dtype(),
        "shipping_costs": Float32Dtype(),
        "is_pre_shipping_versioning": BooleanDtype(),
        "presentment_price_gross": Float32Dtype(),
        "presentment_flex_price_gross": Float32Dtype(),
        "presentment_shipping_costs_gross": Float32Dtype(),
        "presentment_base_commission": Float32Dtype(),
        "presentment_payment_commission": Float32Dtype(),
        "presentment_payout_commission": Float32Dtype(),
        "presentment_target_price_commission": Float32Dtype(),
        "presentment_commissions": Float32Dtype(),
        "presentment_exchange_rate_modifier": Float32Dtype(),
        "presentment_to_settlement_exchange_rate": Float32Dtype(),
        "settlement_price_gross": Float32Dtype(),
        "settlement_flex_price_gross": Float32Dtype(),
        "settlement_shipping_costs_gross": Float32Dtype(),
        "settlement_currency": StringDtype(),
        "settlement_base_commission": Float32Dtype(),
        "settlement_payout_commission": Float32Dtype(),
        "settlement_payment_commission": Float32Dtype(),
        "settlement_target_price_commission": Float32Dtype(),
        "settlement_commissions": Float32Dtype(),
        "commission_pc": Float32Dtype(),
        "payment_commission_pc": Float32Dtype(),
        "payout_commission_pc": Float32Dtype(),
        "target_price_commission_pc": Float32Dtype(),
    },
)

ORDER_REFUNDS_SCHEMA = ExportSchema(
    date_columns=DATE_COLUMNS,
    data_types={
        "id": Int32Dtype(),
        "status": StringDtype(),
        "type": StringDtype(),
        "order_id": Int32Dtype(),
        "merchant_id": Int32Dtype(),
        "refund_handle": StringDtype(),
        "refunded": Float32Dtype(),
    },
)
