import os
from dataclasses import dataclass
from datetime import UTC, datetime
from enum import IntEnum, StrEnum
from functools import cached_property
from os import environ
from typing import Self

from dataclasses_json import DataClassJsonMixin

from common.config import Config
from common.pandas_utils import DataFrameSchema
from common.pipeline_logging.pipeline_model import (
    SupportsPipelineExecution,
    SupportsPipelineRun,
)
from common.sql_model import SqlTable
from common.typings import TimestampInSec

RAW_FOLDER = "platform"
PLATFORM_DB_SECRET_ID = "platform-db-export"

CUT_OFF_DATETIME = datetime(2023, 1, 1, tzinfo=UTC)


class TableSize(StrEnum):
    """
    Table size categories for resource allocation:

    - SMALL: Small tables that can run with minimal resources
    - MEDIUM: Medium-sized tables that need moderate resources
    - LARGE: Large tables that require significant resources
    """

    SMALL = "small"
    MEDIUM = "medium"
    LARGE = "large"


@dataclass(frozen=True)
class SqlTableSize(DataClassJsonMixin):
    """
    Represents a SQL table with its size category.

    :source: The SQL table/view
    :size: The size category of the table (SMALL, MEDIUM, LARGE)
    """

    source: SqlTable
    size: TableSize = TableSize.SMALL  # Default to small table


@dataclass(frozen=True)
class PartitionColumns(DataClassJsonMixin):
    year: str = "year"
    month: str = "month"
    day: str = "day"
    timestamp: str = "timestamp"

    @cached_property
    def columns(self) -> list[str]:
        """Partitioned columns"""
        return list(self.to_dict())


@dataclass(frozen=True)
class PartitionValues(DataClassJsonMixin):
    year: int
    month: int
    day: int
    timestamp: int


@dataclass(frozen=True, kw_only=True)
class ExportConfig(Config, SupportsPipelineRun):
    """
    Common export config defining mapping between source platform view and destination BQ table.
    The Default configuration is passed via environment variables.

    :timestamp: processing timestamp used later for prefixing extract in GCS
    :source: source SQL table/view being ingested
    :chunk_size: chunk size for extracting from source SQL table, can be overridden by ENV variable
    :table_size: size category of the table (SMALL, MEDIUM, LARGE)
    """

    EXPORT_TIMESTAMP = "EXPORT_TIMESTAMP"
    EXPORT_SOURCE = "EXPORT_SOURCE"
    EXPORT_CHUNK_SIZE = "EXPORT_CHUNK_SIZE"

    timestamp: TimestampInSec = int(environ.get(EXPORT_TIMESTAMP, 1737460800))
    source: SqlTable = SqlTable.from_full_name(environ.get(EXPORT_SOURCE, "export_etl.v_products"))
    chunk_size: int = int(environ.get(EXPORT_CHUNK_SIZE, 1000000))
    table_size: TableSize = TableSize.SMALL  # Default to small table

    @cached_property
    def raw_folder(self) -> str:
        return f"{RAW_FOLDER}/{self.source.table_name}/"

    @cached_property
    def partition_columns(self) -> PartitionColumns:
        return PartitionColumns()

    @cached_property
    def partition_values(self) -> PartitionValues:
        dt = datetime.fromtimestamp(self.timestamp, tz=UTC)
        return PartitionValues(year=dt.year, month=dt.month, day=dt.day, timestamp=self.timestamp)

    @cached_property
    def pipeline_name(self) -> str:
        """SupportsPipelineRun implementation."""
        return os.environ.get(SupportsPipelineExecution.PIPELINE_NAME, "platform-export")

    @cached_property
    def run_id(self) -> str:
        """SupportsPipelineRun implementation."""
        return os.environ.get(SupportsPipelineExecution.RUN_ID, "")

    @classmethod
    def dumps(cls, items: list[Self]) -> str:
        """
        Encodes into a JSON array instances of `ExportConfig`.
        Ref.: https://pypi.org/project/dataclasses-json/

        :param items: instances of `ExportConfig`
        :returns: json encoded string
        """
        return cls.schema().dumps(items, many=True)


@dataclass(frozen=True)
class ExportStatus(DataClassJsonMixin):
    """
    Export status / change tracking columns:
    https://gitlab.com/refurbed/analytics-and-ds/analytics-pipelines/-/issues/94#note_1967922911
    """

    last_id: int = 0
    last_modified: datetime = CUT_OFF_DATETIME


class ExportType(IntEnum):
    """
    Export types:

    - INCREMENTAL: uses `id`, `updated_at`, `deleted_at` fields to identify data increment to load
    - TIME_TRAVEL: uses `id`, `valid_from`, `valid_to` fields to identify data increment to load
    - SNAPSHOT: always exports full data snapshot

    Ref.: https://gitlab.com/refurbed/analytics-and-ds/analytics-pipelines/-/issues/94#note_1967922911
    """

    INCREMENTAL = 1
    TIME_TRAVEL = 2
    SNAPSHOT = 3


@dataclass(frozen=True)
class ExportSchema(DataFrameSchema):
    """
    Extension to `DataFrameSchema`:
    :export_type: type of data being exported
    """

    export_type: ExportType = ExportType.INCREMENTAL
