"""
Time travel table schemas definitions:
- append only tables with valid_from, valid_to time travel columns;
- usually big tables with GBs / 10's GBs of data

`export_type: ExportType = ExportType.TIME_TRAVEL`
"""

from export_shared.export_model import ExportSchema, ExportType
from pandas import BooleanDtype, Float32Dtype, Int16Dtype, Int32Dtype, StringDtype

DATE_COLUMNS = ["valid_from", "valid_to"]

EXCHANGE_RATES_SCHEMA = ExportSchema(
    export_type=ExportType.TIME_TRAVEL,
    date_columns=DATE_COLUMNS,
    data_types={
        "id": Int32Dtype(),
        "base": StringDtype(),
        "target": StringDtype(),
        # Rates are with 6 digits precision (i.e. 1.*********), so float32 is enough.
        "rate": Float32Dtype(),
    },
)

ADDRESSES_SCHEMA = ExportSchema(
    export_type=ExportType.TIME_TRAVEL,
    date_columns=DATE_COLUMNS,
    data_types={
        "id": Int32Dtype(),
        # Mandatory address fields:
        "owner_id": Int32Dtype(),
        "first_name": StringDtype(),
        "family_name": StringDtype(),
        "country": StringDtype(),
        "post_code": StringDtype(),
        "town": StringDtype(),
        "street_name": StringDtype(),
        "house_no": StringDtype(),
        "phone": StringDtype(),
        # Optional address fields
        "address_type": StringDtype(),
        "supplement": StringDtype(),
        "male": BooleanDtype(),
        "company": StringDtype(),
        "vatin": StringDtype(),
        "personal_vatin": StringDtype(),
    },
)

ORDER_ITEM_DETAILS_SCHEMA = ExportSchema(
    export_type=ExportType.TIME_TRAVEL,
    date_columns=DATE_COLUMNS + ["return_initiated_at"],
    data_types={
        "id": Int32Dtype(),
        "state": StringDtype(),
        "order_item_id": Int32Dtype(),
        "shipment_tracking_id": Int32Dtype(),
        "item_identifier": StringDtype(),
        "parcel_tracking_url": StringDtype(),
    },
)

ORDER_DOCUMENTS_SCHEMA = ExportSchema(
    export_type=ExportType.TIME_TRAVEL,
    date_columns=DATE_COLUMNS,
    data_types={
        "id": Int32Dtype(),
        "order_id": Int32Dtype(),
        "type": StringDtype(),
        "status": StringDtype(),
        "file_id": StringDtype(),
        "invoice_merchant_id": Int32Dtype(),
    },
)

OFFERS_SCHEMA = ExportSchema(
    export_type=ExportType.TIME_TRAVEL,
    date_columns=["created_at", "valid_from", "valid_to", "shipping_profile_valid_from"],
    data_types={
        "id": Int32Dtype(),
        "state": StringDtype(),
        "instance_id": Int32Dtype(),
        "merchant_id": Int32Dtype(),
        "warranty": Int16Dtype(),
        "stock": Int32Dtype(),
        "grading": StringDtype(),
        "hidden": BooleanDtype(),
        "tax_difference": BooleanDtype(),
        "shipping_profile_id": Int32Dtype(),
        "reference_currency_code": StringDtype(),
        "reference_price": Float32Dtype(),
        "reference_min_flex_price": Float32Dtype(),
        "sku": StringDtype(),
    },
)
SHIPPING_PROFILES_SCHEMA = ExportSchema(
    export_type=ExportType.TIME_TRAVEL,
    date_columns=["valid_from", "valid_to"],
    data_types={
        "id": Int32Dtype(),
        "merchant_id": Int32Dtype(),
        "name": StringDtype(),
        "ships_from": StringDtype(),
    },
)
SHIPPING_PROFILE_DESTINATIONS_SCHEMA = ExportSchema(
    export_type=ExportType.TIME_TRAVEL,
    date_columns=["valid_from", "valid_to", "shipping_profile_valid_to"],
    data_types={
        "id": Int32Dtype(),
        "shipping_profile_id": Int32Dtype(),
        "country": StringDtype(),
        "shipping_costs": Float32Dtype(),
        "delivery_min_days": Int16Dtype(),
        "delivery_max_days": Int16Dtype(),
        "delivery_time_cond": Int16Dtype(),
    },
)
