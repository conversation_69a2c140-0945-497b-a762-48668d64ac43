"""
Snapshot table schemas definitions:
- tables that are rebuilt from several tables;
- tables that lack timestamp columns to track the changes;

`export_type: ExportType = ExportType.SNAPSHOT`
"""

from db_dtypes import date_dtype_name
from db_dtypes.json import JSONDtype
from export_shared.export_model import ExportSchema, ExportType
from pandas import (
    BooleanDtype,
    Float32Dtype,
    Float64Dtype,
    Int16Dtype,
    Int32Dtype,
    StringDtype,
)

ATTRIBUTES_SCHEMA = ExportSchema(
    export_type=ExportType.SNAPSHOT,
    data_types={
        # PK
        "attribute_id": Int32Dtype(),
        "attribute_value_id": Int32Dtype(),
        # attributes
        "attribute_name": StringDtype(),
        "attribute_value": StringDtype(),
        "type": StringDtype(),
        "filterable": BooleanDtype(),
        "precision": Int16Dtype(),
        "unit": StringDtype(),
        "value_bool": BooleanDtype(),
        "value_numeric": Float64Dtype(),
        "value_enum_id": Int32Dtype(),
        "value": JSONDtype(),
    },
)

COUNTRIES_SCHEMA = ExportSchema(
    export_type=ExportType.SNAPSHOT,
    data_types={
        "code": StringDtype(),
        "name": StringDtype(),
        "main_language": StringDtype(),
        "currency": StringDtype(),
        "is_site": BooleanDtype(),
    },
)

FULL_ORDER_ITEM_REFUNDS_SCHEMA = ExportSchema(
    export_type=ExportType.SNAPSHOT,
    date_columns=["refunded_at", "updated_at"],
    data_types={
        "order_item_refund_id": Int32Dtype(),
        "order_item_id": Int32Dtype(),
        "refunded": Float32Dtype(),
        "refunded_charge": Float32Dtype(),
        "refunded_eur": Float32Dtype(),
        "refunded_charge_eur": Float32Dtype(),
        "min_reversed_commission_date": date_dtype_name,
        "held_base_commission": Float32Dtype(),
        "kept_base_commission": Float32Dtype(),
        "executed_base_commission": Float32Dtype(),
        "held_payment_commission": Float32Dtype(),
        "kept_payment_commission": Float32Dtype(),
        "executed_payment_commission": Float32Dtype(),
        "held_base_commission_eur": Float32Dtype(),
        "kept_base_commission_eur": Float32Dtype(),
        "executed_base_commission_eur": Float32Dtype(),
        "held_payment_commission_eur": Float32Dtype(),
        "kept_payment_commission_eur": Float32Dtype(),
        "executed_payment_commission_eur": Float32Dtype(),
    },
)

INSTANCE_ATTRIBUTE_VALUES_SCHEMA = ExportSchema(
    export_type=ExportType.SNAPSHOT,
    data_types={
        "instance_id": Int32Dtype(),
        "attribute_id": Int32Dtype(),
        "attribute_value_id": Int32Dtype(),
    },
)

# Inactive in platform-export
MERCHANT_ORDERS_SCHEMA = ExportSchema(
    export_type=ExportType.SNAPSHOT,
    data_types={
        "order_id": Int32Dtype(),
        "merchant_id": Int32Dtype(),
        "total_charged": Float32Dtype(),
        "total_paid": Float32Dtype(),
        "total_refunded": Float32Dtype(),
        "total_discount": Float32Dtype(),
    },
)

OFFER_PROPERTIES_SCHEMA = ExportSchema(
    export_type=ExportType.SNAPSHOT,
    date_columns=["offer_valid_from", "created_at"],
    data_types={
        # PK
        "offer_id": Int32Dtype(),
        # properties
        "battery_condition": StringDtype(),
        "is_new_battery": BooleanDtype(),
    },
)

ORDER_FINANCIAL_REVENUE_SCHEMA = ExportSchema(
    export_type=ExportType.SNAPSHOT,
    data_types={
        "order_item_id": Int32Dtype(),
        "order_id": Int32Dtype(),
        "revenue": Float32Dtype(),
        "addon_revenue": Float32Dtype(),
        "gmv": Float32Dtype(),
        "discount_eur": Float32Dtype(),
        "service_fee_revenue_eur": Float32Dtype(),
    },
)

ORDER_ITEM_EXCHANGE_RATE_SCHEMA = ExportSchema(
    export_type=ExportType.SNAPSHOT,
    data_types={
        "order_item_id": Int32Dtype(),
        # Rates are with 6 digits precision (i.e. 1.953281000), so float32 is enough.
        "exchange_rate": Float32Dtype(),
    },
)

ORDER_ITEM_OFFERS_SCHEMA = ExportSchema(
    export_type=ExportType.SNAPSHOT,
    date_columns=["created_at", "updated_at", "offer_valid_from", "paid_at", "valid_to"],
    data_types={
        "id": Int32Dtype(),
        "order_id": Int32Dtype(),
        "offer_id": Int32Dtype(),
        "type": StringDtype(),
        "state": StringDtype(),
        "country": StringDtype(),
        "presentment_currency": StringDtype(),
        "payment_provider": StringDtype(),
        "instance_id": Int32Dtype(),
        "merchant_id": Int32Dtype(),
        "grading": StringDtype(),
        "warranty": Int16Dtype(),
        "battery_condition": StringDtype(),
        "is_new_battery": BooleanDtype(),
    },
)

# Inactive in platform-export
ORDER_ITEM_REFUNDS_SCHEMA = ExportSchema(
    export_type=ExportType.SNAPSHOT,
    data_types={
        "id": Int32Dtype(),
        "order_item_id": Int32Dtype(),
        "order_refund_id": Int32Dtype(),
        "refunded": Float32Dtype(),
    },
)

# Inactive in platform-export
PARTNERS_SCHEMA = ExportSchema(
    export_type=ExportType.SNAPSHOT,
    data_types={
        "id": Int32Dtype(),
        "name": StringDtype(),
        "slug": StringDtype(),
    },
)

PRODUCT_ATTRIBUTE_VALUES_SCHEMA = ExportSchema(
    export_type=ExportType.SNAPSHOT,
    data_types={
        "product_id": Int32Dtype(),
        "attribute_id": Int32Dtype(),
        "attribute_value_id": Int32Dtype(),
    },
)

PRODUCT_CATEGORIES_SCHEMA = ExportSchema(
    export_type=ExportType.SNAPSHOT,
    data_types={
        "product_id": Int32Dtype(),
        "main_category_id": Int32Dtype(),
        "show_category_id": Int32Dtype(),
    },
)

PRODUCT_RANKINGS_SCHEMA = ExportSchema(
    export_type=ExportType.SNAPSHOT,
    date_columns=["updated_at"],
    data_types={
        "product_id": Int32Dtype(),
        "category_id": Int32Dtype(),
        "market_country": StringDtype(),
        "rank": Int32Dtype(),
        "rank_b": Int32Dtype(),
    },
)
