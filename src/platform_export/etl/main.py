from export_extractor import PlatformExtractorEtl
from export_loader import PlatformExportLoader
from export_shared.analytics_bq_repository import AnalyticsBQRepository
from export_shared.export_model import PLATFORM_DB_SECRET_ID, ExportConfig
from export_shared.platform_repository import PlatformRepository
from export_shared.schema_mapping import PRODUCT_RANKINGS_SOURCE
from google.cloud.sql.connector import Connector

from common.config import Config
from common.data_lake_repository import DataLakeRepository
from common.logger import setup_logger
from common.pipeline_logging.pipeline_model import (
    PipelineRun,
    SupportsPipelineExecution,
)
from common.secret_client import get_secret
from common.sql_client import DatabaseConfig
from common.timing import timing

config = Config()
logger = setup_logger(config)

analytics_bq_repository = AnalyticsBQRepository()
raw_data_repository = DataLakeRepository(config.raw_bucket)

platform_secret = get_secret(secret_id=PLATFORM_DB_SECRET_ID, project_id=config.project_id)
platform_config = DatabaseConfig.from_json(platform_secret)


@timing(logger, "platform-export-etl")
def run(export_config: ExportConfig) -> None:
    """
    Job entry point, called from `platform-export` workflow.

    Note that input parameters in a cloud run job are passed via environment variables. See `ExportConfig` for details.
    """
    logger.info(
        f"Extracting from {platform_config.host}, "
        f"source={export_config.source.full_name}, "
        f"timestamp={export_config.timestamp}, "
        f"chunk_size={export_config.chunk_size} ..."
    )

    with Connector() as connector:
        platform_repository = PlatformRepository(db_config=platform_config, connector=connector)

        # Extract data from the platform
        etl = PlatformExtractorEtl(
            config=export_config,
            platform_repository=platform_repository,
            raw_data_repository=raw_data_repository,
            analytics_bq_repository=analytics_bq_repository,
        )
        paths = etl.run()
        logger.info(f"'{export_config.source.full_name}' extracted to {paths=}.")

        # Load extracted data into BigQuery
        loader = PlatformExportLoader(
            config=export_config,
            platform_repository=platform_repository,
            analytics_bq_repository=analytics_bq_repository,
        )
        loader.run()


if __name__ == "__main__":
    if config.is_local:
        # Overwrite db_config with local docker db to test your SQL before releasing to staging
        platform_config = DatabaseConfig()
        runtime_config = ExportConfig(source=PRODUCT_RANKINGS_SOURCE)

        # Set the pipeline run environment for local execution
        pipeline_run = PipelineRun()
        SupportsPipelineExecution.set_environment(pipeline_run)
    else:
        # Cloud run job configuration should read from container env variables
        runtime_config = ExportConfig()

    # Ensure that the required environment variables are set
    SupportsPipelineExecution.supports_pipeline_logging(runtime_config, raise_error=True)
    run(export_config=runtime_config)
