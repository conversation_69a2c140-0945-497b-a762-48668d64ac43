"""
EXTRACTOR is responsible for getting source data from the platform:
- extracts changed data from the source
- enriches with time ingested partitions (to enable later diff load)
- loads into GCS RAW bucket (mapped as external BQ table in analytics_raw dataset)
- loads into Postgres analytics database table in import schema
"""

from typing import Optional

from export_shared.analytics_bq_repository import AnalyticsBQRepository
from export_shared.export_model import ExportConfig, ExportStatus
from export_shared.platform_repository import PlatformRepository
from export_shared.schema_mapping import SOURCE_TO_TARGET
from pandas import DataFrame

from common.base import BaseEtl
from common.data_lake_repository import DataLakeRepository
from common.logger import get_logger
from common.monitoring import profile
from common.pipeline_logging.pipeline_logger import log_pipeline_execution
from common.pipeline_logging.pipeline_model import SupportsPipelineExecution
from common.sql_repository import SqlRepository

logger = get_logger()


class PlatformExtractorEtlError(ValueError):
    """Platform Extractor ETL Error"""


class PlatformExtractorEtl(BaseEtl, SupportsPipelineExecution):
    _config: ExportConfig

    def __init__(
        self,
        config: ExportConfig,
        platform_repository: PlatformRepository,
        raw_data_repository: DataLakeRepository,
        analytics_bq_repository: AnalyticsBQRepository,
    ) -> None:
        super().__init__(config)
        self._platform_repository = platform_repository
        self._raw_data_repository = raw_data_repository
        self._analytics_bq_repository = analytics_bq_repository

        self._source = self._config.source
        self._schema = SOURCE_TO_TARGET[self._source].schema
        self._target = SOURCE_TO_TARGET[self._source].table_name
        self._partitions = self._config.partition_columns
        self._batch_no = 0

    @property
    def pipeline_execution_repository(self) -> SqlRepository:
        """SupportsPipelineExecution implementation."""
        return self._platform_repository

    def get_step_name(self, _: str) -> str:
        """SupportsPipelineExecution implementation."""
        return f"{self._target} extract"

    @log_pipeline_execution()
    def run(self) -> list[str]:
        """
        Runs ETL using server side cursor for changes selection to minimise RAM usage.

        :returns: list of extracted file paths in RAW bucket
        """
        self._batch_no = 0
        paths = []
        export_status = self.extract()
        with self._platform_repository.select_changes(
            table=self._source, schema=self._schema, export_status=export_status, chunk_size=self._config.chunk_size
        ) as dfs:
            for df in dfs:
                if path := self.run_batch(df):
                    paths.append(path)

        return paths

    @profile
    def run_batch(self, df: DataFrame) -> Optional[str]:
        """
        Running transform and load for a single extracted batch.

        :param df: input DataFrame with batch of records
        :returns: File path in RAW bucket
        """
        logger.info(f"Running batch={self._batch_no} for '{self._source.full_name}' with {len(df)} rows...")
        self._batch_no += 1
        transformed = self.transform(df)

        return self.load(transformed)

    @profile
    def extract(self) -> ExportStatus:
        """
        Extracts last export status of given source.

        :returns: export status
        """
        return self._analytics_bq_repository.get_export_status(table=self._target)

    @profile
    def transform(self, extracted: DataFrame) -> DataFrame:
        """
        Adds technical partitions to extracted data.

        :param extracted: extracted data
        :returns: partitioned data
        """
        if extracted.empty:
            return extracted

        extracted[self._partitions.year] = self._config.partition_values.year
        extracted[self._partitions.month] = self._config.partition_values.month
        extracted[self._partitions.day] = self._config.partition_values.day
        extracted[self._partitions.timestamp] = self._config.partition_values.timestamp

        logger.info(f"'{self._source.full_name}' partitioned with timestamp={self._config.timestamp}.")

        return extracted

    @profile
    def load(self, transformed: DataFrame) -> Optional[str]:
        """
        Loads extracted and partitioned data into data lake.

        :param transformed: transformed extract with partitions.
        :returns: File path in RAW bucket
        """
        if transformed.empty:
            return None

        raw_path = self._raw_data_repository.write_data_frame(
            data_frame=transformed,
            partition_cols=self._partitions.columns,
            bucket_directory=self._config.raw_folder,
        )
        if len(raw_path) != 1:
            raise PlatformExtractorEtlError(f"Expected single file, got {raw_path=} instead!")

        path = str(raw_path[0])
        logger.info(f"'{self._source.full_name}' saved to '{self._config.raw_bucket}' bucket under '{path}'.")

        return path
