from export_shared.analytics_bq_repository import AnalyticsBQRepository
from export_shared.export_model import ExportConfig
from export_shared.platform_repository import PlatformRepository
from export_shared.schema_mapping import SOURCE_TO_TARGET

from common.logger import get_logger
from common.pipeline_logging.pipeline_logger import log_pipeline_execution
from common.pipeline_logging.pipeline_model import SupportsPipelineExecution
from common.sql_repository import SqlRepository
from common.utils import format_bytes_pretty

logger = get_logger()


class PlatformExportLoader(SupportsPipelineExecution):
    def __init__(
        self,
        config: ExportConfig,
        platform_repository: PlatformRepository,
        analytics_bq_repository: AnalyticsBQRepository,
    ):
        self._config = config
        self._table_name = SOURCE_TO_TARGET[self._config.source].table_name
        self._platform_repository = platform_repository
        self._bq_repository = analytics_bq_repository

    @property
    def pipeline_execution_repository(self) -> SqlRepository:
        """SupportsPipelineExecution implementation."""
        return self._platform_repository

    def get_step_name(self, _: str) -> str:
        """SupportsPipelineExecution implementation."""
        return f"{self._table_name} load"

    @log_pipeline_execution()
    def run(self) -> str:
        """
        Loads according to given config into both PG and BQ target tables.
        :returns: Response message
        """
        bq_table_name = f"BigQuery table '{self._bq_repository.DATASET}.{self._table_name}'"

        logger.debug(f"{bq_table_name} is being loaded ...")
        bytes_processed = format_bytes_pretty(
            self._bq_repository.load_from_raw(table_name=self._table_name, pv=self._config.partition_values)
        )
        qa_passed = self._bq_repository.check_table(table_name=self._table_name)

        response = f"{bq_table_name} loaded with {bytes_processed}, {qa_passed=}."
        logger.info(response)

        return response
