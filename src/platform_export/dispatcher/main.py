import functions_framework
from cloudevents.abstract import CloudEvent
from export_dispatcher import PlatformExportDispatcher
from export_shared.export_model import ExportConfig
from google.cloud.sql.connector import Connector

from common.cloud_events import create_http_event, get_event_message
from common.config import Config
from common.consts import ANALYTICS_CLOUD_SQL_SECRET_ID
from common.logger import setup_logger
from common.pipeline_logging.pipeline_model import PipelineRun
from common.secret_client import get_secret
from common.sql_client import DatabaseConfig
from common.sql_repository import SqlRepository
from common.timing import timing
from common.typings import APPLICATION_JSON_HEADER, HttpResponse

config = Config()
logger = setup_logger(config)

pg_secret = get_secret(ANALYTICS_CLOUD_SQL_SECRET_ID, config.project_id)
pg_config = DatabaseConfig.from_json(pg_secret)


@timing(logger, "platform-export-dispatcher")
@functions_framework.http
def run(request: CloudEvent) -> HttpResponse:
    """
    Http function entry point, called from `platform-export` workflow.

    :returns: List of platform sources to be extracted by the `extractor` function.
    """
    event = get_event_message(request)
    logger.debug(f"Invoked with {event=}")

    pipeline_run = PipelineRun.from_json(event)
    with Connector() as connector:
        repository = SqlRepository(db_config=pg_config, connector=connector)
        dispatcher = PlatformExportDispatcher(pipeline_run, repository)
        result = dispatcher.dispatch()

    return HttpResponse(body=ExportConfig.dumps(result), headers=APPLICATION_JSON_HEADER)


if __name__ == "__main__":
    # Overwrite db_config with local docker db to test your SQL before releasing to staging
    pg_config = DatabaseConfig()
    test_pipeline = PipelineRun()
    run(create_http_event(test_pipeline.to_json()))
