"""
D<PERSON><PERSON><PERSON><PERSON><PERSON> starts platform ETL by returning source tables for extraction.
"""

from typing import Optional

from export_shared.export_model import ExportConfig, SqlTableSize, TableSize
from export_shared.schema_mapping import (
    ATTRIBUTES_SOURCE,
    CATEGORIES_SOURCE,
    COUNTRIES_SOURCE,
    EXCHANGE_RATES_SOURCE,
    FULL_ORDER_ITEM_REFUNDS_SOURCE,
    INSTANCE_ATTRIBUTE_VALUES_SOURCE,
    INSTANCES_SOURCE,
    MERCHANTS_SOURCE,
    OFFER_PROPERTIES_SOURCE,
    OFFERS_SOURCE,
    ORDER_FINANCIAL_REVENUE_SOURCE,
    ORDER_ITEM_EXCHANGE_RATE_SOURCE,
    ORDER_ITEM_OFFERS_SOURCE,
    ORDER_ITEMS_SOURCE,
    ORDERS_SOURCE,
    PRODUCT_ATTRIBUTE_VALUES_SOURCE,
    PRODUCT_CATEGORIES_SOURCE,
    PRODUCTS_SOURCE,
    SHIPPING_PROFILE_DESTINATIONS_SOURCE,
    SHIPPING_PROFILES_SOURCE,
)

from common.logger import get_logger
from common.pipeline_logging.pipeline_logger import log_pipeline_execution
from common.pipeline_logging.pipeline_model import (
    PipelineRun,
    SupportsPipelineExecution,
)
from common.sql_repository import SqlRepository
from common.time_service import TimeService

logger = get_logger()


class PlatformExportDispatcher(SupportsPipelineExecution):
    """
    Dispatches CONFIGURED_SOURCES (platform export views) for extraction and load.
    """

    # Large job: 16 GB with 4 CPU
    LARGE_SOURCES = [
        SqlTableSize(source=OFFERS_SOURCE, size=TableSize.LARGE),  # 16 GB
        SqlTableSize(source=ORDER_ITEMS_SOURCE, size=TableSize.LARGE),  # 7.8 GB
        SqlTableSize(source=ORDERS_SOURCE, size=TableSize.LARGE),  # 6.6 GB
        SqlTableSize(source=ORDER_ITEM_OFFERS_SOURCE, size=TableSize.LARGE),  # 2.5 GB
    ]

    # Medium job: 4 GB with 1 CPU
    MEDIUM_SOURCES = [
        SqlTableSize(source=FULL_ORDER_ITEM_REFUNDS_SOURCE, size=TableSize.MEDIUM),  # 0.5 GB
        SqlTableSize(source=INSTANCES_SOURCE, size=TableSize.MEDIUM),  # 0.5 GB
        SqlTableSize(source=ORDER_ITEM_EXCHANGE_RATE_SOURCE, size=TableSize.MEDIUM),  # 0.5 GB
        SqlTableSize(source=ORDER_FINANCIAL_REVENUE_SOURCE, size=TableSize.MEDIUM),  # 0.5 GB
    ]

    # Small job: 2 GB with 1 CPU
    SMALL_SOURCES = [
        SqlTableSize(source=COUNTRIES_SOURCE, size=TableSize.SMALL),  # 8 kB
        SqlTableSize(source=CATEGORIES_SOURCE, size=TableSize.SMALL),  # 369 kB
        SqlTableSize(source=ATTRIBUTES_SOURCE, size=TableSize.SMALL),  # 15 MB
        SqlTableSize(source=PRODUCTS_SOURCE, size=TableSize.SMALL),  # 30 MB
        SqlTableSize(source=PRODUCT_CATEGORIES_SOURCE, size=TableSize.SMALL),  # 31 MB
        SqlTableSize(source=PRODUCT_ATTRIBUTE_VALUES_SOURCE, size=TableSize.SMALL),  # 13 MB
        SqlTableSize(source=INSTANCE_ATTRIBUTE_VALUES_SOURCE, size=TableSize.SMALL),  # 104 MB
        SqlTableSize(source=MERCHANTS_SOURCE, size=TableSize.SMALL),  # 1.7 MB
        SqlTableSize(source=SHIPPING_PROFILES_SOURCE, size=TableSize.SMALL),  # 2.9 MB
        SqlTableSize(source=SHIPPING_PROFILE_DESTINATIONS_SOURCE, size=TableSize.SMALL),  # 2.8 MB
        SqlTableSize(source=EXCHANGE_RATES_SOURCE, size=TableSize.SMALL),  # 7.4 MB
        SqlTableSize(source=OFFER_PROPERTIES_SOURCE, size=TableSize.SMALL),  # 50 MB
    ]

    # Single list of configured sources with TableSize property
    CONFIGURED_SOURCES: list[SqlTableSize] = LARGE_SOURCES + MEDIUM_SOURCES + SMALL_SOURCES

    def __init__(
        self, pipeline_run: PipelineRun, repository: SqlRepository, time_service: Optional[TimeService] = None
    ) -> None:
        self.set_environment(pipeline_run)
        self._repository = repository
        self._time_service = time_service if time_service else TimeService()

    @property
    def pipeline_execution_repository(self) -> SqlRepository:
        """SupportsPipelineExecution implementation."""
        return self._repository

    @log_pipeline_execution()
    def dispatch(self) -> list[ExportConfig]:
        """
        Dispatches data for extraction to `platform-export` workflow.

        :returns: Export configurations.
        """
        result: list[ExportConfig] = []
        timestamp = self._time_service.timestamp_in_sec
        now = self._time_service.now.isoformat()

        logger.info(
            f"Dispatching {len(self.CONFIGURED_SOURCES)} source tables for extraction with {timestamp=} ({now})..."
        )
        for sql_table_size in self.CONFIGURED_SOURCES:
            config = ExportConfig(timestamp=timestamp, source=sql_table_size.source, table_size=sql_table_size.size)
            result.append(config)

        return result
