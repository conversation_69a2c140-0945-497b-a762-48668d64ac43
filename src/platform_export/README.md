# Platform data export into Analytics DWH

The solution is based on
the [PoC on incremental load](https://www.notion.so/refurbed/PoC-on-incremental-load-d81362e9da8c4a46ab2fcd4cebe5a2d5)
findings.

It exports platform data from `export_etl` views into BQ datasets that form a lakehouse:

- `analytics_raw` - daily extract of data increments as `parquet` files mapped to external BQ tables;
- `analytics_transformed` - target native BQ tables with data merged daily;

## Current scope

The source is `analytics` database and is dependent on daily analytics database refresh.
Currently configured sources are configured
in [export_dispatcher](https://gitlab.com/refurbed/analytics-and-ds/analytics-pipelines/blob/27f8011ecec7033b56c84c693f98412d8ac25cc7/src/platform_export/dispatcher/export_dispatcher.py#L31).

| BQ `analytics_transformed` table |                       PG export view/table | Logical Size | Physical Size |
|:---------------------------------|-------------------------------------------:|-------------:|--------------:|
| attributes                       |                    export_etl.v_attributes |     13.61 MB |      32.31 MB |
| categories                       |                    export_etl.v_categories |     24.46 KB |      44.19 KB |
| countries                        |                     export_etl.v_countries |        753 B |      25.89 KB |
| exchange_rates                   |                export_etl.v_exchange_rates |      6.27 MB |       4.68 MB |
| full_order_item_refunds          |          analytics.full_order_item_refunds |       0.5 GB |        0.5 GB |
| instance_attribute_values        |     export_etl.v_instance_attribute_values |     57.03 MB |       54.1 MB |
| instances                        |                     export_etl.v_instances |     46.05 MB |      46.75 MB |
| merchants                        |                     export_etl.v_merchants |    142.21 KB |      95.09 KB |
| offers                           |                        export_etl.v_offers |     15.44 GB |       3.16 GB |
| offer_properties                 |                 analytics.offer_properties |        50 MB |          5 MB |
| order_financial_revenue          |          analytics.order_financial_revenue |       0.5 GB |        0.5 GB |
| order_item_exchange_rate         |         analytics.order_item_exchange_rate |       0.5 GB |        0.5 GB |
| order_item_offers                |             export_etl.v_order_item_offers |      2.25 GB |       5.11 GB |
| order_items                      |                   export_etl.v_order_items |      9.44 GB |     861.13 MB |
| orders                           |                        export_etl.v_orders |      2.28 GB |     673.48 MB |
| product_attribute_values         |      export_etl.v_product_attribute_values |      7.19 MB |        7.3 MB |
| product_categories               |            export_etl.v_product_categories |      1.49 MB |       1.49 MB |
| product_rankings                 |              export_etl.v_product_rankings |         5 MB |          5 MB |
| products                         |                      export_etl.v_products |      3.69 MB |       9.28 MB |
| shipping_profile_destinations    | export_etl.v_shipping_profile_destinations |      2.57 MB |        1.1 MB |
| shipping_profiles                |             export_etl.v_shipping_profiles |      1.93 MB |       1.39 MB |

## High level architecture

The process is initiated from
the [platform-export](https://gitlab.com/refurbed/analytics-and-ds/analytics-pipelines/blob/27f8011ecec7033b56c84c693f98412d8ac25cc7/infra/workflows/platform_export.yaml#L2)
workflows.

```mermaid
sequenceDiagram
    loop FOR EVERY SOURCE
        Dispatcher ->> ETL: Pass source configuration
        ETL ->> PG (analytics): Get data changes
        ETL ->> BQ (analytics_raw): Extract changes into corresponding table bucket
        ETL ->> BQ (analytics_transformed): MERGE changes into target table
        Note right of ETL: Use BQ/SQL Merge
    end
```

The process is triggered by the [platform-export](/infra/workflows/platform_export.yaml) workflows and consists of the
following components:

**Dispatcher**

Cloud Run Function (512Mi), which:

- returns export view/table to be extracted;
- returns table size to select the right cloud run job size.

**ETL**

- Cloud Run Jobs:
    - small (2 GB and 1 CPU);
    - medium (4 GB and 1 CPU);
    - large (16 GB and 4 CPU);
- Extracts given export view as a snapshot/increment into:
    - BQ `analytics_raw` dataset -> GCS bucket in `parquet` format mapped as a BQ external table;
- Loads extracted data into BQ `analytics_transformed` dataset by means of sql procedure;

## How to extend platform export with new source

1. Create new `export_etl.view` in `analytics` database:

- ensure to cast all PG custom types to primitive types accepted by BQ,
- measure size of the source tables using `pg_relation_size`,
- check what is the primary key,
- see `R__3021_export_categories.sql` for reference

2. Create instance of `ExportSchema` in appropriate schema file:

- `schema_snapshot.py` for fully reloaded tables (no constraints),
- `schema_incremental.py` for incrementally loaded tables with
  `id`, `created_at`, `updated_at` and `deleted_at` columns,
- `schema_time_travel.py` for incrementally loaded tables with `id`, `valid_from`, `valid_to` columns,

3. Create relevant instance variables in the `schema_mapping.py` file:

- add new mapping to `SOURCE_TO_TARGET` variable

4. Test the extract using `src/platform_export/etl/main.py`:

- if extract works `parquet` file will be output to the GCS bucket:
    - RAW bucket folder: `analytics-pipelines-staging-raw/platform/view`
- the load procedure will fail, ignore it for now

5. Create BQ external table in `analytics_raw` dataset:

- see `R__1021_platform_export_categories.sql` for reference

6. Create BQ target table in `analytics_transformed` dataset:

- always add primary key constraint,
- if relevant, add foreign key constraints,
- if table is over 10 GB consider partitioning,
- always add `cluster by` with columns frequently used in filters,
- see `V021__platform_export_categories.sql` for reference.

7. Create load procedure in `analytics_transformed` dataset:

- use `MERGE` statement for incremental and time travel tables,
- use `TRUNCATE` and `INSERT` for fully reloaded tables,
- add related `check` procedure that checks for data anomalies,
- see `R__3021_platform_export_load_categories.sql` for reference

8. Add new source to the CONFIGURED_SOURCES list in `export_dispatcher.py` file.
9. Run integration test to ensure ETL is working as expected:

- run `test_platform_export` in `tests/integration` directory
