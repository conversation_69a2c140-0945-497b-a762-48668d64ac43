import dataclasses

import functions_framework
from cloudevents.abstract import CloudEvent
from etl import ZendeskAutoResponderEtl
from google.cloud.sql.connector import Connector
from pg_repository import ZendeskAutoResponderPgRepository
from zendesk_auto_responder_config import ZendeskAutoResponderConfig

from common.cloud_events import create_http_event, get_event_message
from common.consts import ANALYTICS_CLOUD_SQL_SECRET_ID
from common.google_sheet_client import GoogleSheetConfig, GoogleSheetsClient
from common.logger import setup_logger
from common.pipeline_logging.pipeline_model import PipelineRun
from common.secret_client import get_secret
from common.sql_client import DatabaseConfig
from common.timing import timing
from common.typings import HttpResponse

config = ZendeskAutoResponderConfig()
logger = setup_logger(config)

pg_secret = get_secret(ANALYTICS_CLOUD_SQL_SECRET_ID, config.project_id)
pg_config = DatabaseConfig.from_json(pg_secret)
gs_config = GoogleSheetConfig(
    spreadsheet_id=config.spreadsheet_id, sheet_name=config.sheet_name, starting_cell=config.starting_cell
)
google_sheets_client = GoogleSheetsClient(gs_config=gs_config)


@timing(logger)
@functions_framework.http
def run(request: CloudEvent) -> HttpResponse:
    """
    Http function entry point, called from analytics-workflow.
    """
    message = get_event_message(request)
    logger.info(f"Starting zendesk auto responder with {message}...")
    pipeline_run = PipelineRun.from_json(message)

    with Connector() as connector:
        pg_repo = ZendeskAutoResponderPgRepository(db_config=pg_config, connector=connector)
        etl = ZendeskAutoResponderEtl(
            config=config, pg_repo=pg_repo, pipeline_run=pipeline_run, google_sheets_client=google_sheets_client
        )
        etl.run()

    logger.info("Finished zendesk auto responder.")
    return HttpResponse()


if __name__ == "__main__":
    # Targeting the test [CI/CD] Google Sheet Workbook for testing purposes.
    # https://docs.google.com/spreadsheets/d/1tJKpKiayNipibOg-YwvIWgUzRXqhdSxEhpI_R5nmEyU/edit?gid=0#gid=0
    pg_config = DatabaseConfig()
    gs_config = dataclasses.replace(gs_config, spreadsheet_id="1tJKpKiayNipibOg-YwvIWgUzRXqhdSxEhpI_R5nmEyU")
    google_sheets_client = GoogleSheetsClient(gs_config=gs_config)
    test_pipeline = PipelineRun()
    run(create_http_event(test_pipeline.to_json()))
