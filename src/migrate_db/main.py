from google.cloud.sql.connector import Connector
from migrate_pg import MigratePg
from migration_config import MigrationConfig
from pg_repository import MigratePgRepository

from common.config import DEFAULT_CONFIG
from common.logger import PASSWORD_MASKER, setup_logger
from common.secret_client import get_secret
from common.sql_client import DatabaseConfig

pg_secret = get_secret("analytics-cloud-sql-config", DEFAULT_CONFIG.project_id)
db_config = DatabaseConfig.from_json(pg_secret)
migration_config = MigrationConfig()

logger = setup_logger(DEFAULT_CONFIG, PASSWORD_MASKER)

if __name__ == "__main__":
    if DEFAULT_CONFIG.is_local:
        logger.info("Running locally...")
        db_config = DatabaseConfig()

    with Connector() as connector:
        pg_repo = MigratePgRepository(db_config=db_config, connector=connector)
        migrate_pg = MigratePg(
            config=migration_config,
            database_config=db_config,
            pg_repository=pg_repo,
        )

        migrate_pg.run()
