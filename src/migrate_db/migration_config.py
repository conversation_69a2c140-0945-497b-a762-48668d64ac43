from dataclasses import dataclass
from enum import StrEnum
from os import environ

from common.config import Config
from common.utils import string_to_bool

SLEEP_SECONDS = 3


class FlywayCommand(StrEnum):
    """
    Flyway commands
    Ref. https://documentation.red-gate.com/flyway/reference/commands
    """

    INFO = "info -infoOfState=Pending,Outdated"
    MIGRATE = f"info -infoOfState=Pending,Outdated repair migrate"


@dataclass(frozen=True)
class MigrationConfig(Config):
    run_repeatable_scripts: bool = string_to_bool(environ.get("RUN_REPEATABLE_SCRIPTS", False))
    db_refresh_pipeline_name: str = "analytics db refresh"
    flyway_command: FlywayCommand = environ.get("FLYWAY_COMMAND", FlywayCommand.MIGRATE)

    @property
    def run_test(self) -> bool:
        run_test_variable = environ.get("RUN_FLYWAY_TEST")

        if run_test_variable:
            return string_to_bool(run_test_variable)

        return True if self.is_production else False
