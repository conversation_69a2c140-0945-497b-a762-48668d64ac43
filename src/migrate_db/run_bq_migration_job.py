from common.cloud_run_client.job_client import (
    CloudRunJobClient,
    CloudRunJobClientConfig,
)
from common.cloud_run_client.model import CloudRunJobRunRequest

cloud_run_client = CloudRunJobClient(CloudRunJobClientConfig())

if __name__ == "__main__":
    cloud_run_client.run(
        jon_name="migrate-bq-job",
        body=CloudRunJobRunRequest(environment_variables={"RUN_FLYWAY_TEST": "true"}),
        wait_for_completion=True,
    )
