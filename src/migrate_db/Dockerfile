ARG IMAGE_VERSION

FROM redgate/flyway:11.8 as flyway
FROM $IMAGE_VERSION

COPY --from=flyway /flyway /flyway
COPY --from=flyway /opt/java/openjdk /opt/java/openjdk
ENV PATH="${PATH}:/opt/java/openjdk/bin:/flyway/"

RUN apt-get update -y && apt-get upgrade -y
RUN apt-get install -y postgresql-client postgresql-client-common libpq-dev

# Set our working directory
WORKDIR /app

# Copy build files
COPY ./requirements.txt .

# Then install all dependencies
RUN python -m venv venv
RUN . venv/bin/activate
RUN pip install -r requirements.txt

# Copy over the rest of the code
COPY . .

# https://stackoverflow.com/a/59812588
ENV PYTHONUNBUFFERED=1
CMD ["python", "main.py"]
