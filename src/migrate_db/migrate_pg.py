from datetime import datetime

from migration_config import SLEEP_SECONDS, FlywayCommand, MigrationConfig
from pg_repository import MigratePgRepository

from common.config import SQL_MIGRATIONS_PATH
from common.logger import get_logger
from common.migrate_db import FlywayParams, migrate_db
from common.sql_client import DatabaseConfig
from common.timing import background_task
from common.utils import get_run_id

logger = get_logger()


class MigratePg:
    """
    Postgres migration using Flyway.
    """

    def __init__(
        self, config: MigrationConfig, database_config: DatabaseConfig, pg_repository: MigratePgRepository
    ) -> None:
        self._config = config
        self._database_config = database_config
        self._pg_repository = pg_repository

    def get_flyway_placeholders(self) -> list[str]:
        """
        Prepares Flyway placeholders for migration.
        returns: list of placeholders
        """

        placeholders = [f"runTest={str(self._config.run_test).lower()}"]

        if self._config.run_repeatable_scripts:
            change_reason = get_run_id(datetime.now())
            logger.info(f"Using current timestamp '{change_reason}' as changeReason...")
        else:
            logger.info("Getting the latest changeReason from successful DB refresh run...")
            change_reason = self._pg_repository.get_latest_db_refresh_run_id(self._config.db_refresh_pipeline_name)

        if change_reason:
            placeholders.append(f"changeReason={change_reason}")

        logger.info(f"Flyway placeholders: '{placeholders}'")

        return placeholders

    def get_flyway_params(self) -> FlywayParams:
        """
        Prepares Flyway parameters for migration.
        returns: FlywayParams
        """

        params = FlywayParams(
            host=self._database_config.host,
            port=self._database_config.port,
            database=self._database_config.database,
            user=self._database_config.username,
            password=self._database_config.password,
            config_files=f"{SQL_MIGRATIONS_PATH}/flyway.toml",
            locations=f"filesystem:{SQL_MIGRATIONS_PATH}",
            environment=self._config.env,
            placeholders=self.get_flyway_placeholders(),
        )

        logger.info(f"Flyway params: '{params}'")

        return params

    def run(self) -> None:
        """
        Runs database migrations using Flyway.
        """
        flyway_params = self.get_flyway_params()
        self.migrate(flyway_params=flyway_params, flyway_command=self._config.flyway_command)

    @background_task(logger, sleep_seconds=SLEEP_SECONDS)
    def migrate(self, flyway_params: FlywayParams, flyway_command: FlywayCommand) -> None:
        log_entries = migrate_db(params=flyway_params, flyway_command=flyway_command)

        for log_entry in log_entries.splitlines():  # type: ignore
            logger.info(log_entry)
