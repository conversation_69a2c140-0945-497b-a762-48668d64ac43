from alerting_config import (
    get_test_function_incident,
    get_test_job_incident,
    get_test_service_incident,
)
from cloudevents.http import CloudEvent
from functions_framework import cloud_event
from incident_alerting import IncidentAlerting

from common.cloud_events import create_http_event
from common.cloud_logging_client.client import CloudLoggingClient
from common.cloud_run_client.function_client import CloudRunFunctionClient
from common.cloud_run_client.job_client import (
    CloudRunJobClient,
    CloudRunJobClientConfig,
)
from common.cloud_run_client.service_client import CloudRunServiceClient
from common.config import DEFAULT_CONFIG, MattermostAlertingConfig
from common.logger import setup_logger
from common.mattermost_client import MattermostClient
from common.secret_client import get_secret

logger = setup_logger(DEFAULT_CONFIG)
mm_secret = get_secret(
    secret_id=MattermostAlertingConfig.MATTERMOST_ALERTING_SECRET_ID, project_id=DEFAULT_CONFIG.project_id
)
alerting_config = MattermostAlertingConfig.from_json(mm_secret)
mattermost_client = MattermostClient(alerting_config.webhook_url)
cloud_run_job_client = CloudRunJobClient(CloudRunJobClientConfig())
cloud_function_client = CloudRunFunctionClient(DEFAULT_CONFIG)
cloud_service_client = CloudRunServiceClient(DEFAULT_CONFIG)
cloud_logging_client = CloudLoggingClient(DEFAULT_CONFIG)

incident_alerting = IncidentAlerting(
    config=alerting_config,
    mattermost_client=mattermost_client,
    cloud_run_job_client=cloud_run_job_client,
    cloud_function_client=cloud_function_client,
    cloud_run_service_client=cloud_service_client,
    cloud_logging_client=cloud_logging_client,
)


@cloud_event
def run(event: CloudEvent) -> None:
    """
    Cloud function entry point.
    Function triggered by GCP Monitoring alerts published to PubSub topic.
    """
    incident_alerting.run(event)


def run_tests() -> None:
    """
    For local runs and integration test
    """
    logger.info(f"Running on '{DEFAULT_CONFIG.env}' environment...")

    logger.info("Testing job incident...")
    run(create_http_event(get_test_job_incident()))

    logger.info("Testing function incident...")
    run(create_http_event(get_test_function_incident()))

    logger.info("Testing service incident...")
    run(create_http_event(get_test_service_incident()))


if __name__ == "__main__":
    run_tests()
