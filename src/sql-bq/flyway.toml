# More information on the parameters can be found below:
# https://documentation.red-gate.com/flyway/flyway-cli-and-api/configuration/parameters

[environments.default]
connectRetries = 5
driver="com.simba.googlebigquery.jdbc42.Driver"

[environments.staging]
schemas = ["migrations"]
url = "*******************************************************************************************************"

[environments.production]
schemas = ["migrations"]
url = "***********************************************************************************************"

[environments.production.flyway.placeholders]
changeReason = "20250429"
gcpMultiRegion = "EU"
skipShippingBillingExternalTables = false
skipBackmarketExternalTables = false
skipPlatformExternalTables = false
skipAdtribaExternalTables = false
skipPipedriveExternalTables = false
skipNrmExternalTables = false
skipCategorySlugsMappingExternalTables = false
runTest = false

[flyway]
table = "schema_history"
baselineOnMigrate = true
baselineVersion = "0"
outOfOrder = true
validateMigrationNaming = false
batch = true

[flyway.placeholders]
changeReason = "20250411"
gcpMultiRegion = "EU"
skipShippingBillingExternalTables = false
skipBackmarketExternalTables = false
skipPlatformExternalTables = false
skipAdtribaExternalTables = false
skipPipedriveExternalTables = false
skipNrmExternalTables = false
skipCategorySlugsMappingExternalTables = false
runTest = true
