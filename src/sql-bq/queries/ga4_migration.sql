/*
    COPY production GA4 events to staging for testing purposes:
    - run it manually from staging
    - set `dry_run` = false to actually copy the data

    Output:
    [2025-05-06 12:45:48] 9,981 rows affected in 1 m 33 s 378 ms
*/

begin
    declare dry_run bool default true;
    declare start_date date default current_date() - 10;
    declare end_date date default current_date() - 1;

    declare date_suffix string;
    declare sql string;

    -- Loop through each date from start_date to end_date
    for date_value in (
        select dt from unnest(generate_date_array(start_date, end_date)) as dt
    ) do
        -- Create a formatted date string for table suffix (YYYYMMDD)
        set date_suffix = format_date('%Y%m%d', date_value.dt);

        -- Create a sharded table in staging with data from production
        set sql = concat(
            'create or replace table analytics_304326635.events_fresh_',
            date_suffix,
            ' as select * from refb-analytics.analytics_304326635.events_fresh_',
            date_suffix,
            " where privacy_info.analytics_storage = 'Yes'",
            " and event_name in ('purchase', 'view_item')",
            ' limit 1000;'
        );

        if dry_run then
            select concat(date_suffix, ': ', sql) as sql;
        else
            execute immediate sql;
            call google_analytics.load_purchases_and_clicks (date_suffix);
        end if;
    end for;
end;
