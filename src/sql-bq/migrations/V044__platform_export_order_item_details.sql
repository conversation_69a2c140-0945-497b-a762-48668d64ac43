drop table if exists analytics_transformed.order_item_details;
create table if not exists analytics_transformed.order_item_details
( -- noqa
    id int64 not null,

    -- change tracking attributes
    valid_from timestamp not null,
    valid_to timestamp not null,

    -- supportive fields that ease merge
    modified_at timestamp not null,
    is_active bool not null,

    -- order_item_details
    state string not null,
    order_item_id int64 not null,
    shipment_tracking_id int64,
    item_identifier string not null,
    parcel_tracking_url string not null,
    return_initiated_at timestamp,

    primary key (id, valid_from) not enforced,
    foreign key (order_item_id) references analytics_transformed.order_items (id) not enforced
)
cluster by
-- LEFT JOIN public.order_item_details AS order_item_details
-- ON (order_items."id") = (order_item_details."order_item_id") and (order_item_details."valid_to") = 'infinity'
order_item_id,
valid_to;
