drop table if exists analytics_transformed.order_refunds;
create table if not exists analytics_transformed.order_refunds
( -- noqa
    id int64 primary key not enforced,

    -- change tracking attributes
    created_at timestamp not null,
    updated_at timestamp not null,
    deleted_at timestamp,

    -- supportive fields that ease merge
    modified_at timestamp not null,
    is_active bool not null,

    -- order_refunds
    status string not null,
    type string not null, -- noqa
    order_id int64 not null,
    merchant_id int64,
    refund_handle string not null,
    refunded numeric(14, 4) not null,

    foreign key (order_id) references analytics_transformed.orders (id) not enforced,
    foreign key (merchant_id) references analytics_transformed.merchants (id) not enforced
)
cluster by
-- join order_refunds orr on oir.order_refund_id = orr.id
id;
