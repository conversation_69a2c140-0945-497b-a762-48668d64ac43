drop table if exists analytics_transformed.merchant_orders;
create table if not exists analytics_transformed.merchant_orders
( -- noqa
    order_id int64 not null,
    merchant_id int64 not null,

    total_charged numeric(14, 4) not null,
    total_paid numeric(14, 4) not null,
    total_refunded numeric(14, 4) not null,
    total_discount numeric(14, 4) not null,

    primary key (order_id, merchant_id) not enforced,
    foreign key (order_id) references analytics_transformed.orders (id) not enforced,
    foreign key (merchant_id) references analytics_transformed.merchants (id) not enforced
)
cluster by
order_id,
merchant_id;
