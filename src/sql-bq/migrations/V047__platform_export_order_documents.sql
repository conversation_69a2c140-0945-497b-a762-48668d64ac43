drop table if exists analytics_transformed.order_documents;
create table if not exists analytics_transformed.order_documents
( -- noqa
    id int64 not null,

    -- change tracking attributes
    valid_from timestamp not null,
    valid_to timestamp not null,

    -- supportive fields that ease merge
    modified_at timestamp not null,
    is_active bool not null,

    -- order_documents
    order_id int64 not null,
    `type` string not null,
    status string not null,
    file_id string,
    invoice_merchant_id int64,

    primary key (id, valid_from) not enforced,
    foreign key (order_id) references analytics_transformed.orders (id) not enforced
)
cluster by
-- LEFT JOIN public.order_documents  AS order_documents
-- ON (orders."id") = (order_documents."order_id") and (order_documents."valid_to") = 'infinity'
order_id,
valid_to;
