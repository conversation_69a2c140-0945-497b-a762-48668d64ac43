drop table if exists analytics_transformed.order_item_refunds;
create table if not exists analytics_transformed.order_item_refunds
( -- noqa
    id int64 primary key not enforced,
    order_item_id int64 not null,
    order_refund_id int64 not null,
    refunded numeric(14, 4) not null,

    foreign key (order_item_id) references analytics_transformed.order_items (id) not enforced,
    foreign key (order_refund_id) references analytics_transformed.order_refunds (id) not enforced
)
cluster by
-- LEFT JOIN public.order_item_refunds  AS order_item_refunds
-- ON (order_items."id") = (order_item_refunds."order_item_id")
order_item_id,
order_refund_id;
