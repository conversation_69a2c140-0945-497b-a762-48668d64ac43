/*
View to get statuses for all partitions of `google_analytics.purchases_and_clicks`:
- Note UPPER casing for `INFORMATION_SCHEMA.PARTITIONS`, it is case sensitive!
- Bytes billed: 10 MB

References:
- https://cloud.google.com/bigquery/docs/information-schema-partitions
*/
create or replace view google_analytics.purchases_and_clicks_partitions
as
select
    table_schema,
    table_name,
    parse_date("%Y%m%d", partition_id) as table_partition,
    partition_id,
    last_modified_time as last_modified,
    total_rows,
    total_logical_bytes as size_bytes,
    timestamp_add(last_modified_time, interval 360 day) as expiration_time
from
    google_analytics.`INFORMATION_SCHEMA.PARTITIONS`
where
    table_schema = "google_analytics"
    and table_name = "purchases_and_clicks"
    and partition_id != "__NULL__";

begin
    if ("${runTest}" = "true") then -- noqa
        -- test
        -- select * from google_analytics.`INFORMATION_SCHEMA.PARTITIONS` limit 10;
        select *
        from google_analytics.purchases_and_clicks_partitions
        order by expiration_time;
    end if;
end;
