/*
View over Google Analytics table: analytics_304326635.events_fresh_:
- Use `view` suffix to differentiate from the physical table with the same name!
- It is the source for the `purchases_and_clicks` partitioned table thus inside a migration script
- DATE functions: If no time zone is specified with timestamp_expression, the default time zone, UTC, is used.
- Remember to update the ` google_analytics.purchases_and_clicks ` table should the schema change!
*/
create or replace view google_analytics.purchases_and_clicks_view
as
select distinct
    -- table partition
    _table_suffix as table_suffix,
    date(parse_date('%Y%m%d', _table_suffix)) as table_date,

    -- logical partitions
    date(timestamp_micros(event_timestamp)) as event_date,
    timestamp_micros(event_timestamp) as event_timestamp,
    event_name,
    split(events.device.web_info.hostname, 'www.refurbed.')[safe_offset(1)] as page_country,

    -- purchases
    (
        select value.int_value from unnest(event_params)
        where key = 'ga_session_id'
    ) as session_id,
    user_pseudo_id,
    traffic_source.medium as channel,
    ecommerce.transaction_id,
    case when events.device.category = 'mobile' then 1 else 0 end as is_mobile,

    -- clicks
    device.category as device_category,
    traffic_source.name as traffic_source_name,
    geo.city as geo_city,
    geo.country as geo_country,
    device.web_info.browser,
    safe_cast(item_id as int64) as product_id,
    item_name as product_name,

    -- As purchase event doesn't have item_params defined, we need to get the grade from item_variant
    coalesce(
        (
            select value.string_value from unnest(item_params)
            where key = 'grade'
        ),
        (case
            when regexp_extract(item_variant, r'Grade\s([A-Z])') is not null
                then regexp_extract(item_variant, r'Grade\s([A-Z])')
            when regexp_extract(item_variant, r'Grade\s(\d+)') = '1'
                then 'A'
            when regexp_extract(item_variant, r'Grade\s(\d+)') = '2'
                then 'B'
            when regexp_extract(item_variant, r'Grade\s(\d+)') = '3'
                then 'C'
            else 'Unknown'
        end)
    ) as grade,

    (
        select value.int_value from unnest(item_params)
        where key = 'offer_id'
    ) as offer_id,
    (
        select safe_cast(coalesce(value.int_value, value.double_value, value.float_value) as numeric)
        from unnest(item_params)
        where key = 'price2'
    ) as price,
    safe_cast(
        split(coalesce(
            (
                select value.string_value from unnest(item_params)
                where key = 'dynamic_pricing_model'
            ),
            (
                select value.string_value from unnest(item_params)
                where key = 'sp_model'
            )
        ), '|')[
            safe_offset(0)
        ] as int64
    ) as dynamic_pricing_model,

    -- As purchase event has item_category set as product_category, whereas view_item has item_category3
    (case
        when item_category3 = '(notset)' then item_category
        else item_category3
    end) as product_category,

    coalesce(
        (
            select value.string_value from unnest(item_params)
            where key = 'dynamic_pricing_model'
        ),
        (
            select value.string_value from unnest(item_params)
            where key = 'sp_model'
        )
    ) as target_price_label,

    safe_cast(
        split((
            select value.string_value from unnest(item_params)
            where key = 'delivery_time'
        ), '-')[safe_offset(0
        )]
        as int64
    ) as delivery_min_days,

    safe_cast(
        split((
            select value.string_value from unnest(item_params)
            where key = 'delivery_time'
        ), '-')[safe_offset(1
        )]
        as int64
    ) as delivery_max_days,

    -- instance_id under item_params in "purchase" event is missing so we use item_varant.
    -- We cannot use item_variant on "view_item" event for this purpose becuase the instance id
    -- part on item_variant is empty most of the time for sports items.
    -- So, we use coalesce, where we use instance_id in view_item, but item_varint in purchase
    safe_cast(
        regexp_extract(
            coalesce(
                (
                    select value.string_value from unnest(item_params)
                    where key = 'instance_id'
                ),
                item_variant
            ),
            r'\((\d+)\)$'
        ) as int64
    ) as instance_id,
    (
        select value.string_value from unnest(item_params)
        where key = 'all_devices_warranty_options')
        as warranty_options,
    safe_cast(
        regexp_extract(
            (
                select value.string_value from unnest(item_params)
                where key = 'all_devices_warranty_options'
            ),
            r'(\d+) M [+-]€?\s?0(?:[,.]0{1,2})?\s?€?'
        ) as int64
    ) as warranty,
    (
        select value.string_value from unnest(item_params)
        where key = 'battery_condition'
    ) as battery_condition
from
    analytics_304326635.`events_fresh_*` as events, unnest(items) as items
where
    privacy_info.analytics_storage = 'Yes'
    and event_name in ('view_item', 'purchase');

begin
    declare test_date string default '20240511';

    if ("${runTest}" = "true") then -- noqa
        -- test me!
        call google_analytics.load_purchases_and_clicks (test_date);

        select * from google_analytics.purchases_and_clicks_view
        where table_suffix = test_date
        limit 5;
    end if;
end;
