begin
    if "${skipPlatformExternalTables}" = "false" then -- noqa
        -- Drop is safe for external table!
        drop external table if exists analytics_raw.order_item_offers;
        create external table analytics_raw.order_item_offers
        (
            -- order_items: incremental condition
            id int64,
            created_at timestamp,
            updated_at timestamp,

            -- order_items: business attributes
            order_id int64,
            offer_id int64,
            offer_valid_from timestamp,
            `type` string,

            -- orders
            paid_at timestamp,
            state string,
            country string,
            presentment_currency string,
            payment_provider string,

            -- offers: nullable
            valid_to timestamp,
            instance_id int64,
            merchant_id int64,
            grading string,
            warranty int64,

            -- offer_properties: nullable
            battery_condition string,
            is_new_battery boolean
        )
        with partition columns (
            year int64,
            month int64,
            day int64,
            `timestamp` int64
        )
        options (
            format = "PARQUET",
            uris = ["gs://analytics-pipelines-${flyway:environment}-raw/platform/v_order_item_offers/*"],
            hive_partition_uri_prefix
            = "gs://analytics-pipelines-${flyway:environment}-raw/platform/v_order_item_offers",
            require_hive_partition_filter = true,
            description = "External table mapped to parquet files and triggered from the platform-export workflow.\n"
            || "source: export_etl.v_order_item_offers,\n"
            || "owner: data_engineering"
        );

        -- test: analytics_raw.order_item_offers
        -- analytics-pipelines-staging-raw/platform/v_order_item_offers/year=2025/month=1/day=21/timestamp=1737460800
        select *
        from analytics_raw.order_item_offers
        where
            year = 2025 and month = 1 and day = 21 and `timestamp` > 0
            and is_new_battery is not null
        limit 5;
    end if;
end;
