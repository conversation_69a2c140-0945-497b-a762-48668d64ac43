/**
# External tables
- https://cloud.google.com/bigquery/docs/reference/standard-sql/data-definition-language#create_external_table_statement
*/
begin
    if "${skipPlatformExternalTables}" = "false" then -- noqa
        -- Drop is safe for external table!
        drop external table if exists analytics_raw.products;
        create external table analytics_raw.products
        with partition columns (
            year int64,
            month int64,
            day int64,
            `timestamp` int64
        )
        options (
            format = "PARQUET",
            uris = ["gs://analytics-pipelines-${flyway:environment}-raw/platform/v_products/*"],
            hive_partition_uri_prefix = "gs://analytics-pipelines-${flyway:environment}-raw/platform/v_products",
            require_hive_partition_filter = true,
            description = "External table mapped to parquet files and triggered from the platform-export workflow.\n"
            || "source: export_etl.v_products,\n"
            || "owner: data_engineering"
        );

        -- test: analytics_raw.products
        select t.*
        from analytics_raw.products as t
        where
            t.year = 2025 and t.month > 0 and t.day > 0 and t.`timestamp` > 0
        limit 5;
    end if;
end;
