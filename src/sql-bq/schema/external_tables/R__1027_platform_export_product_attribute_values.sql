/**
# External tables
- https://cloud.google.com/bigquery/docs/reference/standard-sql/data-definition-language#create_external_table_statement
*/
begin
    if "${skipPlatformExternalTables}" = "false" then -- noqa
        -- Drop is safe for external table!
        drop external table if exists analytics_raw.product_attribute_values;
        create external table analytics_raw.product_attribute_values
        with partition columns (
            year int64,
            month int64,
            day int64,
            `timestamp` int64
        )
        options (
            format = "PARQUET",
            uris = ["gs://analytics-pipelines-${flyway:environment}-raw/platform/v_product_attribute_values/*"],
            hive_partition_uri_prefix
            = "gs://analytics-pipelines-${flyway:environment}-raw/platform/v_product_attribute_values",
            require_hive_partition_filter = true,
            description = "External table mapped to parquet files and triggered from the platform-export workflow.\n"
            || "source: export_etl.v_product_attribute_values,\n"
            || "owner: data_engineering"
        );

        -- test: analytics_raw.product_attribute_values
        select *
        from analytics_raw.product_attribute_values
        where
            year = 2025 and month > 0 and day > 0 and `timestamp` > 0
        limit 5;
    end if;
end;
