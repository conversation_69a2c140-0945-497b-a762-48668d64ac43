/**
# External tables
- https://cloud.google.com/bigquery/docs/reference/standard-sql/data-definition-language#create_external_table_statement
*/
begin
    if "${skipPlatformExternalTables}" = "false" then -- noqa
        -- Drop is safe for external table!
        drop external table if exists analytics_raw.order_refunds;
        create external table analytics_raw.order_refunds
        with partition columns (
            year int64,
            month int64,
            day int64,
            `timestamp` int64
        )
        options (
            format = "PARQUET",
            uris = ["gs://analytics-pipelines-${flyway:environment}-raw/platform/v_order_refunds/*"],
            hive_partition_uri_prefix
            = "gs://analytics-pipelines-${flyway:environment}-raw/platform/v_order_refunds",
            require_hive_partition_filter = true
        );

        -- test: analytics_raw.order_refunds
        select *
        from analytics_raw.order_refunds
        where
            year = 2025 and month > 0 and day > 0 and `timestamp` > 0
        limit 5;
    end if;
end;
