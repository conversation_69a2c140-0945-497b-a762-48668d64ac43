begin
    if "${skipPlatformExternalTables}" = "false" then -- noqa
        -- Drop is safe for external table!
        drop external table if exists analytics_raw.product_rankings;
        create external table analytics_raw.product_rankings (
            product_id int64 not null,
            category_id int64 not null,
            market_country string not null,
            updated_at timestamp not null,
            rank int64 not null,
            rank_b int64 not null
        )
        with partition columns (
            year int64,
            month int64,
            day int64,
            `timestamp` int64
        )
        options (
            format = "PARQUET",
            uris = ["gs://analytics-pipelines-${flyway:environment}-raw/platform/v_product_rankings/*"],
            hive_partition_uri_prefix
            = "gs://analytics-pipelines-${flyway:environment}-raw/platform/v_product_rankings",
            require_hive_partition_filter = true,
            description = "External table mapped to parquet files and triggered from the platform-export workflow.\n"
            || "source: export_etl.v_product_rankings,\n"
            || "owner: data_engineering"
        );

        -- test: analytics_raw.product_rankings
        select *
        from analytics_raw.product_rankings
        where
            year = 2025 and month = 1 and day = 21 and `timestamp` > 0
        limit 5;
    end if;
end;
