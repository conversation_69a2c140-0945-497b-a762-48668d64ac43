begin
    if "${skipPlatformExternalTables}" = "false" then -- noqa
        drop external table if exists analytics_raw.order_item_exchange_rate;
        create external table analytics_raw.order_item_exchange_rate
        (
            order_item_id int64,
            exchange_rate float64
        )
        with partition columns (
            year int64,
            month int64,
            day int64,
            `timestamp` int64
        )
        options (
            format = "PARQUET",
            uris = ["gs://analytics-pipelines-${flyway:environment}-raw/platform/order_item_exchange_rate/*"],
            hive_partition_uri_prefix
            = "gs://analytics-pipelines-${flyway:environment}-raw/platform/order_item_exchange_rate",
            require_hive_partition_filter = true,
            description = "External table mapped to parquet files and triggered from the platform-export workflow.\n"
            || "source: analytics.order_item_exchange_rate,\n"
            || "owner: data_engineering"
        );

        -- test: analytics_raw.order_item_exchange_rate
        -- analytics-pipelines-staging-raw/platform/order_item_exchange_rate/year=2025/month=1/day=21/timestamp=1737460800 -- noqa
        select *
        from analytics_raw.order_item_exchange_rate
        where
            year = 2025 and month > 0 and day > 0 and `timestamp` > 0
        limit 5;
    end if;
end;
