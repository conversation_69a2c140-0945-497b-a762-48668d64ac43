begin
    if "${skipPlatformExternalTables}" = "false" then -- noqa
        drop external table if exists analytics_raw.order_financial_revenue;
        create external table analytics_raw.order_financial_revenue
        (
            order_item_id int64,
            order_id int64,
            revenue float64,
            addon_revenue float64,
            gmv float64,
            discount_eur float64,
            service_fee_revenue_eur float64
        )
        with partition columns (
            year int64,
            month int64,
            day int64,
            `timestamp` int64
        )
        options (
            format = "PARQUET",
            uris = ["gs://analytics-pipelines-${flyway:environment}-raw/platform/order_financial_revenue/*"],
            hive_partition_uri_prefix
            = "gs://analytics-pipelines-${flyway:environment}-raw/platform/order_financial_revenue",
            require_hive_partition_filter = true,
            description = "External table mapped to parquet files and triggered from the platform-export workflow.\n"
            || "source: analytics.order_financial_revenue,\n"
            || "owner: data_engineering"
        );

        -- test: analytics_raw.order_financial_revenue
        select *
        from analytics_raw.order_financial_revenue
        where
            year = 2025 and month > 0 and day > 0 and `timestamp` > 0
        limit 5;
    end if;
end;
