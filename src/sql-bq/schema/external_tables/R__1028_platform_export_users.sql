/**
# External tables
- https://cloud.google.com/bigquery/docs/reference/standard-sql/data-definition-language#create_external_table_statement
*/
begin
    if "${skipPlatformExternalTables}" = "false" then -- noqa
        -- Drop is safe for external table!
        drop external table if exists analytics_raw.users;
        create external table analytics_raw.users
        with partition columns (
            year int64,
            month int64,
            day int64,
            `timestamp` int64
        )
        options (
            format = "PARQUET",
            uris = ["gs://analytics-pipelines-${flyway:environment}-raw/platform/v_users/*"],
            hive_partition_uri_prefix = "gs://analytics-pipelines-${flyway:environment}-raw/platform/v_users",
            require_hive_partition_filter = true
        );

        -- test: analytics_raw.users
        select t.*
        from analytics_raw.users as t
        where
            t.year = 2025 and t.month > 0 and t.day > 0 and t.`timestamp` > 0
        limit 5;
    end if;
end;
