begin
    if "${skipPlatformExternalTables}" = "false" then -- noqa
        drop external table if exists analytics_raw.full_order_item_refunds;
        create external table analytics_raw.full_order_item_refunds
        (
            order_item_refund_id int64,
            order_item_id int64,
            refunded_at timestamp,
            updated_at timestamp,
            refunded float64,
            refunded_charge float64,
            refunded_eur float64,
            refunded_charge_eur float64,
            min_reversed_commission_date date,
            held_base_commission float64,
            kept_base_commission float64,
            executed_base_commission float64,
            held_payment_commission float64,
            kept_payment_commission float64,
            executed_payment_commission float64,
            held_base_commission_eur float64,
            kept_base_commission_eur float64,
            executed_base_commission_eur float64,
            held_payment_commission_eur float64,
            kept_payment_commission_eur float64,
            executed_payment_commission_eur float64
        )
        with partition columns (
            year int64,
            month int64,
            day int64,
            `timestamp` int64
        )
        options (
            format = "PARQUET",
            uris = ["gs://analytics-pipelines-${flyway:environment}-raw/platform/full_order_item_refunds/*"],
            hive_partition_uri_prefix
            = "gs://analytics-pipelines-${flyway:environment}-raw/platform/full_order_item_refunds",
            require_hive_partition_filter = true,
            description = "External table mapped to parquet files and triggered from the platform-export workflow.\n"
            || "source: analytics.full_order_item_refunds,\n"
            || "owner: data_engineering"
        );

        -- test: analytics_raw.full_order_item_refunds
        select *
        from analytics_raw.full_order_item_refunds
        where
            year = 2025 and month > 0 and day > 0 and `timestamp` > 0
        limit 5;
    end if;
end;
