begin
    if "${skipPlatformExternalTables}" = "false" then -- noqa
        -- Drop is safe for external table!
        drop external table if exists analytics_raw.offer_properties;
        create external table analytics_raw.offer_properties (
            offer_id int64 not null,
            offer_valid_from timestamp not null,
            created_at timestamp not null,
            battery_condition string not null,
            is_new_battery bool not null
        )
        with partition columns (
            year int64,
            month int64,
            day int64,
            `timestamp` int64
        )
        options (
            format = "PARQUET",
            uris = ["gs://analytics-pipelines-${flyway:environment}-raw/platform/offer_properties/*"],
            hive_partition_uri_prefix
            = "gs://analytics-pipelines-${flyway:environment}-raw/platform/offer_properties",
            require_hive_partition_filter = true,
            description = "External table mapped to parquet files and triggered from the platform-export workflow.\n"
            || "source: analytics.offer_properties,\n"
            || "owner: data_engineering"
        );

        -- test: analytics_raw.offer_properties
        select *
        from analytics_raw.offer_properties
        where
            year = 2025 and month = 1 and day = 21 and `timestamp` > 0
        limit 5;
    end if;
end;
