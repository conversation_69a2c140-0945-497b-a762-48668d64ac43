/**
# External tables
- https://cloud.google.com/bigquery/docs/reference/standard-sql/data-definition-language#create_external_table_statement
*/
begin
    if "${skipPlatformExternalTables}" = "false" then -- noqa
        -- Drop is safe for external table!
        drop external table if exists analytics_raw.instances;
        create external table analytics_raw.instances
        (
            id int64 not null,
            created_at timestamp not null,
            updated_at timestamp not null,
            deleted_at timestamp,
            published bool not null,
            product_id int64 not null,
            `name` string not null,
            name_en string,
            srp float64
        )
        with partition columns (
            year int64,
            month int64,
            day int64,
            `timestamp` int64
        )
        options (
            format = "PARQUET",
            uris = ["gs://analytics-pipelines-${flyway:environment}-raw/platform/v_instances/*"],
            hive_partition_uri_prefix = "gs://analytics-pipelines-${flyway:environment}-raw/platform/v_instances",
            require_hive_partition_filter = true,
            description = "External table mapped to parquet files and triggered from the platform-export workflow.\n"
            || "source: export_etl.v_instances,\n"
            || "owner: data_engineering"
            # Simba JDBCDriver does not support labels yet
            # labels=[("source", "export_etl.v_instances"), ("owner", "data_engineering")]
        );

        -- test: analytics_raw.instances
        -- analytics-pipelines-staging-raw/platform/v_instances/year=2025/month=1/day=21/timestamp=1737460800
        select t.*
        from analytics_raw.instances as t
        where
            t.year = 2025 and t.month = 1 and t.day > 0 and t.`timestamp` > 0
        limit 5;
    end if;
end;
