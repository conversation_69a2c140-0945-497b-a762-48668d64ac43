begin
    if "${skipPlatformExternalTables}" = "false" then -- noqa
        -- Drop is safe for external table!
        drop external table if exists analytics_raw.countries;
        create external table analytics_raw.countries
        with partition columns (
            year int64,
            month int64,
            day int64,
            `timestamp` int64
        )
        options (
            format = "PARQUET",
            uris = ["gs://analytics-pipelines-${flyway:environment}-raw/platform/v_countries/*"],
            hive_partition_uri_prefix = "gs://analytics-pipelines-${flyway:environment}-raw/platform/v_countries",
            require_hive_partition_filter = true,
            description = "External table mapped to parquet files and triggered from the platform-export workflow.\n"
            || "source: export_etl.v_countries,\n"
            || "owner: data_engineering"
        );

        -- test: analytics_raw.countries
        select *
        from analytics_raw.countries
        where
            year = 2025 and month = 5 and day = 2 and `timestamp` > 0
        limit 5;
    end if;
end;
