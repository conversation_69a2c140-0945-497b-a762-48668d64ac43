create or replace function analytics_transformed.is_infinity(valid_to timestamp) returns bool
as
/*
    Used for backward compatibility with PG `infinity` type.
    See `analytics.infinity_to_timestamp` in `src/sql-pg/schema/functions/R__0005_infinity.sql` for reference.
*/
(
    valid_to = timestamp("2099-12-31 23:59:59")
);

begin
    if ("${runTest}" = "true") then -- noqa
        select
            analytics_transformed.is_infinity(current_timestamp) as current_time,
            analytics_transformed.is_infinity("2099-12-31 23:59:59") as infinity_sec,
            analytics_transformed.is_infinity("2099-12-31 23:59:59.000000") as infinity_ns;
    end if;
end;
