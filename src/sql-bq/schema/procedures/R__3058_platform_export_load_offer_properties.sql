-- Flyway will re-run this script if ${changeReason} is updated
drop procedure if exists analytics_transformed.load_offer_properties;
create or replace procedure analytics_transformed.load_offer_properties (
    p_year int64,
    p_month int64,
    p_day int64,
    p_timestamp int64
)
options (
    strict_mode = false,  -- Required to avoid `Cannot query over table without a filter` error.
    description = """Loads analytics_transformed.offer_properties target table from analytics_raw.offer_properties
source table using MERGE statement. Updates only when properties have changed to avoid unnecessary updates."""
)
begin
    merge into analytics_transformed.offer_properties as t
    using (
        with raw as (
            select distinct
                offer_id,
                offer_valid_from,
                created_at,
                battery_condition,
                is_new_battery
            from analytics_raw.offer_properties
            where
                year = p_year and month = p_month and day = p_day and timestamp = p_timestamp
        )

        select *
        from raw
    ) as s
    on t.offer_id = s.offer_id
    when not matched by target then
        insert row
    when matched and (
        t.offer_valid_from != s.offer_valid_from
        or t.created_at != s.created_at
        or t.battery_condition != s.battery_condition
        or t.is_new_battery != s.is_new_battery
    ) then
        update set
            offer_valid_from = s.offer_valid_from,
            created_at = s.created_at,
            battery_condition = s.battery_condition,
            is_new_battery = s.is_new_battery
    ;
end;

drop procedure if exists analytics_transformed.check_offer_properties;
create or replace procedure analytics_transformed.check_offer_properties ()
options (
    description = "Throws error if duplicate rows are found."
)
begin
    select
        offer_id,
        if(count(*) > 1, error("Duplicate rows found!"), 0) as duplicate_rows
    from analytics_transformed.offer_properties
    group by offer_id
    having count(*) > 1;
end;

begin
    if ("${runTest}" = "true") then -- noqa
        -- test: analytics-pipelines-staging-raw/platform/offer_properties
        --      /year=2025/month=1/day=21/timestamp=1737460800
        -- select * from analytics_transformed.offer_properties limit 5;
        truncate table analytics_transformed.offer_properties;
        call analytics_transformed.load_offer_properties (2025, 1, 21, 1737460800);
        call analytics_transformed.check_offer_properties ();
    end if;
end;
