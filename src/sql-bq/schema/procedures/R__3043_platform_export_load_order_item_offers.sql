drop procedure if exists analytics_transformed.load_order_item_offers;
create or replace procedure analytics_transformed.load_order_item_offers (
    p_year int64,
    p_month int64,
    p_day int64,
    p_timestamp int64
) options (
    strict_mode = false,  -- Required to avoid `Cannot query over table without a filter` error.
    description = """Loads `analytics_transformed.order_item_offers` target table
from the `analytics_raw.order_item_offers` source table using MERGE statement.
1. Uses `distinct` as there could be duplicate files for the same timestamp.
2. Uses `farm_fingerprint` to generate a row_hash for change tracking.
3. Delete rows not present in the source index table."""
)
begin
    merge into analytics_transformed.order_item_offers as t
    using (
        with raw as (
            select distinct
                -- order_items
                id,
                created_at,
                updated_at,
                order_id,
                offer_id,
                offer_valid_from,
                type,

                -- orders
                paid_at,
                state,
                country,
                presentment_currency,
                payment_provider,

                -- offers: nullable
                valid_to,
                instance_id,
                merchant_id,
                grading,
                warranty,

                -- offer_properties: nullable
                battery_condition,
                is_new_battery
            from analytics_raw.order_item_offers
            where
                `year` = p_year and `month` = p_month and `day` = p_day and `timestamp` = p_timestamp
        )

        select
            id, -- PK
            created_at,
            updated_at,
            order_id,
            offer_id,
            offer_valid_from,
            type,
            paid_at,
            state,
            country,
            presentment_currency,
            payment_provider,
            valid_to,
            instance_id,
            merchant_id,
            grading,
            warranty,
            battery_condition,
            is_new_battery,
            farm_fingerprint(
                concat(
                    coalesce(cast(created_at as string), ""),
                    coalesce(cast(updated_at as string), ""),
                    coalesce(cast(order_id as string), ""),
                    coalesce(cast(offer_id as string), ""),
                    coalesce(cast(offer_valid_from as string), ""),
                    coalesce(type, ""),
                    coalesce(cast(paid_at as string), ""),
                    coalesce(state, ""),
                    coalesce(country, ""),
                    coalesce(presentment_currency, ""),
                    coalesce(payment_provider, ""),
                    coalesce(cast(valid_to as string), ""),
                    coalesce(cast(instance_id as string), ""),
                    coalesce(cast(merchant_id as string), ""),
                    coalesce(grading, ""),
                    coalesce(cast(warranty as string), ""),
                    coalesce(battery_condition, ""),
                    coalesce(cast(is_new_battery as string), "")
                )
            ) as row_hash
        from raw
    ) as s
    on t.id = s.id
    when not matched by target then
        insert row
    when matched and s.row_hash is distinct from t.row_hash then
        update set
            t.created_at = s.created_at,
            t.updated_at = s.updated_at,
            t.order_id = s.order_id,
            t.offer_id = s.offer_id,
            t.offer_valid_from = s.offer_valid_from,
            t.type = s.type,
            t.paid_at = s.paid_at,
            t.state = s.state,
            t.country = s.country,
            t.presentment_currency = s.presentment_currency,
            t.payment_provider = s.payment_provider,
            t.valid_to = s.valid_to,
            t.instance_id = s.instance_id,
            t.merchant_id = s.merchant_id,
            t.grading = s.grading,
            t.warranty = s.warranty,
            t.battery_condition = s.battery_condition,
            t.is_new_battery = s.is_new_battery,
            t.row_hash = s.row_hash
    when not matched by source then
        delete
    ;
end;

drop procedure if exists analytics_transformed.check_order_item_offers;
create or replace procedure analytics_transformed.check_order_item_offers ()
options (
    description = "Throws error if duplicate rows are found."
)
begin
    select
        id,
        if(count(*) > 1, error("Duplicate rows found!"), 0) as duplicate_rows
    from analytics_transformed.order_item_offers
    group by
        id
    having count(*) > 1;
end;

begin
    if ("${runTest}" = "true") then -- noqa
        -- analytics-pipelines-staging-raw/platform/v_order_item_offers/year=2025/month=1/day=21/timestamp=1737460800
        -- select * from analytics_transformed.order_item_offers limit 5;
        call analytics_transformed.load_order_item_offers (2025, 1, 21, 1737460800);
        call analytics_transformed.check_order_item_offers ();
    end if;
end;
