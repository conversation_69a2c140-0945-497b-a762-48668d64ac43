drop procedure if exists analytics_transformed.load_products;
create or replace procedure analytics_transformed.load_products (
    p_year int64,
    p_month int64,
    p_day int64,
    p_timestamp int64
) options (
    strict_mode = false,  -- Required to avoid `Cannot query over table without a filter` error.
    description = """Loads `analytics_transformed.products` target table from the source `analytics_raw.products`
for the given partition columns using `MERGE` statement.
Uses `distinct` as there could be duplicate files for the same timestamp."""
)
begin
    merge into analytics_transformed.products as t
    using (
        with raw as (
            select distinct t.*
            from analytics_raw.products as t
            where
                `year` = p_year and `month` = p_month and `day` = p_day and `timestamp` = p_timestamp
        )

        select
            id,
            -- Change tracking attributes
            created_at,
            updated_at,
            deleted_at,
            -- Supportive fields that ease merge
            greatest(updated_at, coalesce(deleted_at, created_at)) as modified_at,
            if(deleted_at is null, true, false) as is_active,
            -- Business fields
            category_id,
            listing_mode,
            `name`,
            name_en,
            slug
        from raw
    ) as s
    on t.id = s.id
    when not matched by target then
        insert row
    when matched and s.modified_at > t.modified_at then
        update set
            created_at = s.created_at,
            updated_at = s.updated_at,
            deleted_at = s.deleted_at,
            modified_at = s.modified_at,
            is_active = s.is_active,
            category_id = s.category_id,
            listing_mode = s.listing_mode,
            name = s.name,
            name_en = s.name_en,
            slug = s.slug
    ;
end;

drop procedure if exists analytics_transformed.check_products;
create or replace procedure analytics_transformed.check_products ()
options (
    description = "Throws error if duplicate rows are found."
)
begin
    select
        id,
        if(count(*) > 1, error("Duplicate rows found!"), 0) as duplicate_rows
    from analytics_transformed.products
    group by id
    having count(*) > 1;
end;

begin
    if ("${runTest}" = "true") then -- noqa
        -- test: analytics-pipelines-staging-raw/platform/v_products/year=2025/month=1/day=21/timestamp=1737460800
        truncate table analytics_transformed.products;
        call analytics_transformed.load_products (2025, 1, 21, 1737460800);
        call analytics_transformed.check_products ();

        -- For testing purposes only
        create or replace procedure analytics_transformed.check_error ()
        options (
            description = "Throws error for testing purposes."
        )
        begin
            select error("Duplicate rows found!") as duplicate_rows
            from analytics_transformed.products
            limit 5;
        end;
    end if;
end;
