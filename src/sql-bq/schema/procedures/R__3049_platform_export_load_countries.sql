drop procedure if exists analytics_transformed.load_countries;
create procedure analytics_transformed.load_countries (
    p_year int64,
    p_month int64,
    p_day int64,
    p_timestamp int64
) options (
    strict_mode = false,  -- Required to avoid `Cannot query over table without a filter` error.
    description = """Loads `analytics_transformed.countries` target table from the source `analytics_raw.countries`
using MERGE statement. Updates only when any properties have changed."""
)
begin
    merge into analytics_transformed.countries as t
    using (
        select distinct
            t.code,
            t.name,
            t.main_language,
            t.currency,
            t.is_site
        from analytics_raw.countries as t
        where
            t.year = p_year and t.month = p_month and t.day = p_day and t.timestamp = p_timestamp
    ) as s
    on t.code = s.code
    when not matched by target then
        insert row
    when matched and (
        t.name != s.name
        or t.main_language != s.main_language
        or t.currency != s.currency
        or t.is_site != s.is_site
    ) then
        update set
            name = s.name,
            main_language = s.main_language,
            currency = s.currency,
            is_site = s.is_site;
end;

drop procedure if exists analytics_transformed.check_countries;
create or replace procedure analytics_transformed.check_countries ()
options (
    description = "Throws error if duplicate rows are found."
)
begin
    select
        code,
        if(count(*) > 1, error("Duplicate rows found!"), 0) as duplicate_rows
    from analytics_transformed.countries
    group by code
    having count(*) > 1;
end;

begin
    if ("${runTest}" = "true") then -- noqa
        -- analytics-pipelines-staging-raw/platform/v_countries/year=2025/month=5/day=2/timestamp=1746169449
        truncate table analytics_transformed.countries;
        call analytics_transformed.load_countries (2025, 5, 2, 1746169449);
        call analytics_transformed.check_countries ();
    end if;
end;
