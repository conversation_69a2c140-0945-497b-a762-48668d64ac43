-- Flyway will re-run this script if ${changeReason} is updated
drop procedure if exists analytics_transformed.load_order_items;
create or replace procedure analytics_transformed.load_order_items (
    p_year int64,
    p_month int64,
    p_day int64,
    p_timestamp int64
) options (
    strict_mode = false,  -- Required to avoid `Cannot query over table without a filter` error.
    description = """Loads `analytics_transformed.order_items` target table from the source `analytics_raw.order_items`
for the given partition columns using `MERGE` statement.
Uses `distinct` as there could be duplicate files for the same timestamp."""
)
begin
    merge into analytics_transformed.order_items as t
    using (
        with raw as (
            select distinct t.*
            from analytics_raw.order_items as t
            where
                `year` = p_year and `month` = p_month and `day` = p_day and `timestamp` = p_timestamp
        )

        select
            id,

            -- Change tracking attributes
            created_at,
            updated_at,
            deleted_at,

            -- Supportive fields that ease merge
            greatest(updated_at, coalesce(deleted_at, created_at)) as modified_at,
            if(deleted_at is null, true, false) as is_active,

            -- Business fields
            order_id,
            offer_id,
            offer_valid_from,
            offer_price_id,
            offer_price_valid_from,

            type,
            is_addon,
            cast(charged as numeric) as charged,
            cast(discount as numeric) as discount,

            net,
            cast(vat as numeric) as vat,
            vat_country,
            cast(price as numeric) as price,
            cast(flex_price as numeric) as flex_price,

            addon_id,
            addon_valid_from,
            addon_assigned_order_item_id,
            parse_json(addon_details) as addon_details,

            shipping_profile_destination_id,
            shipping_profile_destination_valid_from,
            cast(shipping_costs as numeric) as shipping_costs,
            is_pre_shipping_versioning,

            cast(presentment_price_gross as numeric) as presentment_price_gross,
            cast(presentment_flex_price_gross as numeric) as presentment_flex_price_gross,
            cast(presentment_shipping_costs_gross as numeric) as presentment_shipping_costs_gross,

            cast(presentment_base_commission as numeric) as presentment_base_commission,
            cast(presentment_payment_commission as numeric) as presentment_payment_commission,
            cast(presentment_payout_commission as numeric) as presentment_payout_commission,
            cast(presentment_target_price_commission as numeric) as presentment_target_price_commission,
            cast(presentment_commissions as numeric) as presentment_commissions,
            cast(presentment_exchange_rate_modifier as numeric) as presentment_exchange_rate_modifier,
            cast(presentment_to_settlement_exchange_rate as numeric) as presentment_to_settlement_exchange_rate,

            cast(settlement_price_gross as numeric) as settlement_price_gross,
            cast(settlement_flex_price_gross as numeric) as settlement_flex_price_gross,
            cast(settlement_shipping_costs_gross as numeric) as settlement_shipping_costs_gross,
            settlement_currency,

            cast(settlement_base_commission as numeric) as settlement_base_commission,
            cast(settlement_payout_commission as numeric) as settlement_payout_commission,
            cast(settlement_payment_commission as numeric) as settlement_payment_commission,
            cast(settlement_target_price_commission as numeric) as settlement_target_price_commission,
            cast(settlement_commissions as numeric) as settlement_commissions,

            cast(commission_pc as numeric) as commission_pc,
            cast(payment_commission_pc as numeric) as payment_commission_pc,
            cast(payout_commission_pc as numeric) as payout_commission_pc,
            cast(target_price_commission_pc as numeric) as target_price_commission_pc
        from raw
    ) as s
    on t.id = s.id
    when not matched by target then
        insert row
    when matched and s.modified_at > t.modified_at then
        update set
            created_at = s.created_at,
            updated_at = s.updated_at,
            deleted_at = s.deleted_at,
            modified_at = s.modified_at,
            is_active = s.is_active,

            order_id = s.order_id,
            offer_id = s.offer_id,
            offer_valid_from = s.offer_valid_from,
            offer_price_id = s.offer_price_id,
            offer_price_valid_from = s.offer_price_valid_from,

            type = s.type,
            is_addon = s.is_addon,
            charged = s.charged,
            discount = s.discount,

            net = s.net,
            vat = s.vat,
            vat_country = s.vat_country,
            price = s.price,
            flex_price = s.flex_price,

            addon_id = s.addon_id,
            addon_valid_from = s.addon_valid_from,
            addon_assigned_order_item_id = s.addon_assigned_order_item_id,
            addon_details = s.addon_details,

            shipping_profile_destination_id = s.shipping_profile_destination_id,
            shipping_profile_destination_valid_from = s.shipping_profile_destination_valid_from,
            shipping_costs = s.shipping_costs,
            is_pre_shipping_versioning = s.is_pre_shipping_versioning,

            presentment_price_gross = s.presentment_price_gross,
            presentment_flex_price_gross = s.presentment_flex_price_gross,
            presentment_shipping_costs_gross = s.presentment_shipping_costs_gross,

            presentment_base_commission = s.presentment_base_commission,
            presentment_payment_commission = s.presentment_payment_commission,
            presentment_payout_commission = s.presentment_payout_commission,
            presentment_target_price_commission = s.presentment_target_price_commission,
            presentment_commissions = s.presentment_commissions,
            presentment_exchange_rate_modifier = s.presentment_exchange_rate_modifier,
            presentment_to_settlement_exchange_rate = s.presentment_to_settlement_exchange_rate,

            settlement_price_gross = s.settlement_price_gross,
            settlement_flex_price_gross = s.settlement_flex_price_gross,
            settlement_shipping_costs_gross = s.settlement_shipping_costs_gross,
            settlement_currency = s.settlement_currency,

            settlement_base_commission = s.settlement_base_commission,
            settlement_payout_commission = s.settlement_payout_commission,
            settlement_payment_commission = s.settlement_payment_commission,
            settlement_target_price_commission = s.settlement_target_price_commission,
            settlement_commissions = s.settlement_commissions,

            commission_pc = s.commission_pc,
            payment_commission_pc = s.payment_commission_pc,
            payout_commission_pc = s.payout_commission_pc,
            target_price_commission_pc = s.target_price_commission_pc;
end;

drop procedure if exists analytics_transformed.check_order_items;
create or replace procedure analytics_transformed.check_order_items ()
options (
    description = "Throws error if duplicate rows are found."
)
begin
    select
        id,
        if(count(*) > 1, error("Duplicate rows found!"), 0) as duplicate_rows
    from analytics_transformed.order_items
    group by id
    having count(*) > 1;
end;

begin
    if ("${runTest}" = "true") then -- noqa
        -- test: analytics-pipelines-staging-raw/platform/v_order_items/year=2025/month=1/day=21/timestamp=1737460800
        -- select * from analytics_transformed.order_items limit 5;
        truncate table analytics_transformed.order_items;
        call analytics_transformed.load_order_items (2025, 1, 21, 1737460800);
        call analytics_transformed.check_order_items ();
    end if;
end;
