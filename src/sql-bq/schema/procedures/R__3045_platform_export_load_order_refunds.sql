drop procedure if exists analytics_transformed.load_order_refunds;
create procedure analytics_transformed.load_order_refunds (
    p_year int64,
    p_month int64,
    p_day int64,
    p_timestamp int64
) options (
    -- Required to avoid `Cannot query over table 'analytics_raw.order_refunds' without a filter` error.
    strict_mode = false
)
/*
    Loads `analytics_transformed.order_refunds` target table from the source `analytics_raw.order_refunds`
    for the given partition columns using `MERGE` statement.
*/
begin
    merge into analytics_transformed.order_refunds as t
    using (
        with raw as (
            select t.*
            from analytics_raw.order_refunds as t
            where
                `year` = p_year and `month` = p_month and `day` = p_day and `timestamp` = p_timestamp
        )

        select
            id,
            -- Change tracking attributes
            created_at,
            updated_at,
            deleted_at,
            -- Supportive fields that ease merge
            greatest(updated_at, coalesce(deleted_at, created_at)) as modified_at,
            if(deleted_at is null, true, false) as is_active,
            -- Business fields
            status,
            `type`,
            order_id,
            merchant_id,
            refund_handle,
            cast(refunded as numeric) as refunded
        from raw
    ) as s
    on t.id = s.id
    when not matched by target then
        insert row
    when matched and s.modified_at > t.modified_at then
        update set
            created_at = s.created_at,
            updated_at = s.updated_at,
            deleted_at = s.deleted_at,
            modified_at = s.modified_at,
            is_active = s.is_active,
            status = s.status,
            `type` = s.`type`,
            order_id = s.order_id,
            merchant_id = s.merchant_id,
            refund_handle = s.refund_handle,
            refunded = s.refunded
    ;
end;

begin
    if ("${runTest}" = "true") then -- noqa
        -- test: analytics-pipelines-staging-raw/platform/v_order_refunds/year=2024/month=10/day=17/timestamp=1729155137
        call analytics_transformed.load_order_refunds (2024, 10, 17, 1729155137);
        select * from analytics_transformed.order_refunds limit 5;
    end if;
end;
