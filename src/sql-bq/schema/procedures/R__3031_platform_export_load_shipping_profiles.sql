drop procedure if exists analytics_transformed.load_shipping_profiles;
create or replace procedure analytics_transformed.load_shipping_profiles (
    p_year int64,
    p_month int64,
    p_day int64,
    p_timestamp int64
) options (
    strict_mode = false,  -- Required to avoid `Cannot query over table without a filter` error.
    description
    = """Loads `analytics_transformed.shipping_profiles` target table from the source `analytics_raw.shipping_profiles`
for the given partition columns using `MERGE` statement."""
)
begin
    merge into analytics_transformed.shipping_profiles as t
    using (
        with raw as (
            select distinct
                t.*,
                analytics_transformed.is_infinity(t.valid_to) as is_active
            from analytics_raw.shipping_profiles as t
            where
                `year` = p_year and `month` = p_month and `day` = p_day and `timestamp` = p_timestamp
        )

        select
            id,
            -- Change tracking attributes
            valid_from,
            valid_to,
            -- Supportive fields that ease merge
            if(is_active, valid_from, greatest(valid_from, valid_to)) as modified_at,
            is_active,
            -- Business fields
            merchant_id,
            name,
            ships_from
        from raw
    ) as s
    on t.id = s.id and t.valid_from = s.valid_from
    when not matched by target then
        insert row
    when matched and s.valid_to != t.valid_to then
        update set
            valid_to = s.valid_to,
            modified_at = s.modified_at,
            is_active = s.is_active,
            merchant_id = s.merchant_id,
            name = s.name,
            ships_from = s.ships_from;
end;

drop procedure if exists analytics_transformed.check_shipping_profiles;
create or replace procedure analytics_transformed.check_shipping_profiles ()
options (
    description = "Throws error if duplicate rows are found."
)
begin
    select
        id,
        valid_from,
        if(count(*) > 1, error("Duplicate rows found!"), 0) as duplicate_rows
    from analytics_transformed.shipping_profiles
    group by id, valid_from
    having count(*) > 1;
end;

begin
    if ("${runTest}" = "true") then -- noqa
        -- test: analytics-pipelines-staging-raw/platform/v_shipping_profiles/
        --       year=2025/month=1/day=21/timestamp=1737460800
        -- select * from analytics_transformed.shipping_profiles limit 5;
        truncate table analytics_transformed.shipping_profiles;
        call analytics_transformed.load_shipping_profiles (2025, 1, 21, 1737460800);
        call analytics_transformed.check_shipping_profiles ();
    end if;
end;
