-- Flyway will re-run this script if ${changeReason} is updated
drop procedure if exists analytics_transformed.load_merchants;
create or replace procedure analytics_transformed.load_merchants (
    p_year int64,
    p_month int64,
    p_day int64,
    p_timestamp int64
) options (
    strict_mode = false,  -- Required to avoid `Cannot query over table without a filter` error.
    description = """Loads `analytics_transformed.merchants` target table from the source `analytics_raw.merchants`
for the given partition columns using `MERGE` statement.
Uses `distinct` as there could be duplicate files for the same timestamp."""
)
begin
    merge into analytics_transformed.merchants as t
    using (
        with raw as (
            select distinct
                t.id,
                t.created_at,
                t.updated_at,
                t.deleted_at,
                t.country,
                t.state,
                t.is_addon_support,
                t.name,
                t.email,
                t.public_id,
                cast(t.handicap as numeric) as handicap,
                cast(t.payment_commission_pc as numeric) as payment_commission_pc,
                cast(t.payout_commission_pc as numeric) as payout_commission_pc
            from analytics_raw.merchants as t
            where
                `year` = p_year and `month` = p_month and `day` = p_day and `timestamp` = p_timestamp
        )

        select
            id,
            -- Change tracking attributes
            created_at,
            updated_at,
            deleted_at,
            -- Supportive fields that ease merge
            greatest(updated_at, coalesce(deleted_at, created_at)) as modified_at,
            if(deleted_at is null, true, false) as is_active,
            -- Business fields
            country,
            state,
            is_addon_support,
            name,
            email,
            public_id,
            handicap,
            payment_commission_pc,
            payout_commission_pc
        from raw
    ) as s
    on t.id = s.id
    when not matched by target then
        insert row
    when matched and s.modified_at > t.modified_at then
        update set
            created_at = s.created_at,
            updated_at = s.updated_at,
            deleted_at = s.deleted_at,
            modified_at = s.modified_at,
            is_active = s.is_active,
            country = s.country,
            state = s.state,
            is_addon_support = s.is_addon_support,
            name = s.name,
            email = s.email,
            public_id = s.public_id,
            handicap = s.handicap,
            payment_commission_pc = s.payment_commission_pc,
            payout_commission_pc = s.payout_commission_pc
    ;
end;

drop procedure if exists analytics_transformed.check_merchants;
create or replace procedure analytics_transformed.check_merchants ()
options (
    description = "Throws error if duplicate rows are found."
)
begin
    select
        id,
        if(count(*) > 1, error("Duplicate rows found!"), 0) as duplicate_rows
    from analytics_transformed.merchants
    group by id
    having count(*) > 1;
end;

begin
    if ("${runTest}" = "true") then -- noqa
        -- test: analytics-pipelines-staging-raw/platform/v_merchants/year=2025/month=1/day=21/timestamp=1737460800
        -- select * from analytics_transformed.merchants limit 5;
        truncate table analytics_transformed.merchants;
        call analytics_transformed.load_merchants (2025, 1, 21, 1737460800);
        call analytics_transformed.check_merchants ();
    end if;
end;
