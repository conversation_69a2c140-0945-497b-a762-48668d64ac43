-- Flyway will re-run this script if ${changeReason} is updated
drop procedure if exists analytics_transformed.refresh_backmarket_product_ranges;
create procedure analytics_transformed.refresh_backmarket_product_ranges (
    refresh_date date
)
options
(description = """Refreshes Back Market product ranges.
Ranges are calculated based on start and end dates. As agreed with the Analytics Team, we track changes for two attributes only: price and grade.
This means that for every change in either of these attributes, a new row is generated with corresponding start and end dates.
It is not possible to have two rows with the same start and end dates for a given URL.
Additionally, the start date cannot be later than the end date.
If a product's attributes don't change over time (between hourly crawls),
only one row is recorded with the minimum scrapped_at as the start_date and the maximum scrapped_at as the end_date.
It is assumed that the crawler runs every hour.
Therefore, adding one hour and subtracting one millisecond from the end_date is additionally done to ensure that the ranges are continuous.
This approach also ensures that there are no gaps between the ranges.
For all other columns, the latest value is taken (from the row with the maximum scrapped_at).
Owner: data_engineering""")
begin
    -- Calculate row_id and attr_id to identify unique rows and attribute changes
    create temporary table bm_products as
    select
        farm_fingerprint(to_json_string(struct(
            bm.product_name,
            bm.instance_features,
            bm.price,
            bm.grade,
            bm.url,
            bm.country,
            bm.extra_features,
            bm.title,
            bm.id1,
            bm.id2,
            bm.currency
        ))) as row_id,
        farm_fingerprint(to_json_string(struct(bm.price, bm.grade))) as attr_id,
        *
    from
        analytics_transformed.backmarket_products as bm
    where
        bm.scrapped_at >= timestamp(refresh_date)
        and bm.scrapped_at < timestamp(refresh_date + interval 1 day);

    -- Removes duplicate rows for given url and scrapped_at
    create temporary table bm_products_unique as
    select *
    from
        (
            select
                row_number() over (
                    partition by url, scrapped_at
                    order by row_id
                ) as rn,
                *
            from
                bm_products
        ) as t
    where
        rn = 1;

    -- Identifies attribute changes and assigns a unique id to each change
    create temporary table bm_product_ranges_changes as
    select
        product_name,
        instance_features,
        price,
        grade,
        url,
        country,
        extra_features,
        title,
        id1,
        id2,
        currency,
        attr_changed_id,
        attr_changed,
        rn,
        case
            when attr_changed_id != coalesce(
                    lead(attr_changed_id, 1) over (
                        partition by url
                        order by scrapped_at
                    ), -attr_changed_id
                ) then 1
            else 0
        end as attr_last_value,
        scrapped_at
    from
        (
            select distinct
                *,
                sum(attr_changed) over (
                    partition by url
                    order by scrapped_at
                ) as attr_changed_id
            from
                (
                    select
                        *,
                        case
                            when attr_id = (
                                    lag(attr_id, 1) over (
                                        partition by url
                                        order by scrapped_at
                                    )
                                ) then 0
                            else 1
                        end as attr_changed
                    from
                        bm_products_unique
                )
        ) as t;

    -- Calculates start and end dates for each attribute change
    create temporary table bm_product_ranges as
    select
        ch1.*,
        min(ch1.scrapped_at) over (partition by ch1.url, ch1.attr_changed_id) as start_date,
        coalesce(ch2.end_date, max(ch1.scrapped_at) over (partition by ch1.url) + interval 1 hour)
        - interval 1 millisecond as end_date
    from
        bm_product_ranges_changes as ch1
    left join
        (select -- noqa
                ch.url,
                ch.attr_changed_id,
                min(ch.scrapped_at) as end_date
            from
                bm_product_ranges_changes as ch
            group by
                ch.url,
                ch.attr_changed_id
        ) as ch2
    on ch1.url = ch2.url
        and ch1.attr_changed_id + 1 = ch2.attr_changed_id;

    -- Deletes existing rows for the given date range
    delete
    from
    analytics_transformed.backmarket_product_ranges
    where
        start_date >= timestamp(refresh_date)
        and end_date < timestamp(refresh_date + interval 1 day);

    -- Inserts new rows
    insert into analytics_transformed.backmarket_product_ranges
    select
        bm.product_name,
        bm.instance_features,
        round(bm.price, 2) as price,
        bm.grade,
        bm.url,
        bm.country,
        bm.extra_features,
        bm.title,
        bm.id1,
        bm.id2,
        bm.currency,
        bm.start_date,
        bm.end_date
    from
        bm_product_ranges as bm
    where
        bm.attr_last_value = 1;

end;

drop procedure if exists analytics_transformed.check_backmarket_product_ranges;
create or replace procedure analytics_transformed.check_backmarket_product_ranges (check_date date)
options
(description = """Throws error if anomalies are found for 'analytics_transformed.backmarket_product_ranges' table.
    Owner: data_engineering""")
begin
    select
        url,
        start_date,
        end_date,
        if(count(*) > 1, error("Duplicate rows found!"), 0) as duplicate_rows
    from
        analytics_transformed.backmarket_product_ranges
    where
        start_date >= timestamp(check_date)
        and end_date < timestamp(check_date + interval 1 day)
    group by
        url,
        start_date,
        end_date
    having
        count(*) > 1;

    select error("Anomalies found for start_date and end_date!")
    from
        analytics_transformed.backmarket_product_ranges
    where
        (
            start_date >= timestamp(check_date)
            and end_date < timestamp(check_date + interval 1 day)
        )
        and start_date > end_date
        or start_date is null
        or end_date is null;
end;


begin
    if ("${runTest}" = "true") then -- noqa
        call analytics_transformed.refresh_backmarket_product_ranges (date("2025-04-28"));
        call analytics_transformed.check_backmarket_product_ranges (date("2025-04-28"));
    end if;
end;
