-- Flyway will re-run this script if ${changeReason} is updated
create or replace procedure google_analytics.load_category_view_item_clicks_purchases (p_event_date date)
options (
    strict_mode = false,  -- Required to avoid `Cannot query over table without a filter` error.
    description = """Loads `google_analytics.category_view_item_clicks_purchases` table
for the given `event_date `partition."""
)
begin
    -- 1. Delete previously loaded partition
    delete from google_analytics.category_view_item_clicks_purchases
    where event_date = p_event_date;

    -- 2. Insert the new data
    insert into google_analytics.category_view_item_clicks_purchases
    with p_clicks_per_item as (
        select
            pc.event_date,
            pcs.market,
            pcs.product_id,
            pcs.category_id,-- category where the product can be shown
            pcs.category_slug,
            coalesce(count(*), 0) as p_page_item_clicks
        from
            google_analytics.purchases_and_clicks as pc
        inner join
            analytics_reporting.product_category_slugs as pcs
        on
            pc.product_id = pcs.product_id
            and pc.page_country = pcs.market
        where
            pc.table_date = p_event_date -- noqa
            and pc.event_name = "view_item"
            and pc.product_id is not null
            and pc.instance_id is not null
            and pcs.category_id is not null
        group by all
    ),

    category_item_impressions_and_p_clicks as (
        select
            coalesce(cpic.event_date, pc.event_date) as event_date,
            coalesce(cpic.category_id, pc.category_id) as category_id,
            coalesce(cpic.category_slug, pc.category_slug) as category_name,
            coalesce(cpic.market, pc.market) as market,
            coalesce(cpic.product_id, pc.product_id) as product_id,
            coalesce(cpic.impressions, 0) as impressions,
            coalesce(cpic.avg_price, 0) as avg_price,
            coalesce(cpic.c_page_item_clicks, 0) as c_page_item_clicks,
            coalesce(pc.p_page_item_clicks, 0) as p_page_item_clicks
        from
            google_analytics.category_page_impression_and_clicks as cpic
        full join
            p_clicks_per_item as pc
        on
            cpic.event_date = pc.event_date
            and cpic.market = pc.market
            and cpic.product_id = pc.product_id
            and cpic.category_id = pc.category_id
        where
            cpic.event_date = p_event_date -- noqa
        group by all
    ),

    transaction_id_lookup as (
        select
            event_date,
            page_country as market,
            transaction_id,
            product_id
        from
            google_analytics.purchases_and_clicks
        where
            table_date = p_event_date
            and event_name = "purchase"
            and product_id is not null
            and instance_id is not null
        group by all
    ),

    transaction_db as (
        select
            date(oio.paid_at) as order_date,
            oio.country,
            i.product_id,
            cast(oio.order_id as string) as transaction_id,
            ofr.revenue as revenue_eur,
            ofr.addon_revenue as addon_revenue_eur,
            ofr.gmv as gmv_eur,
            count(*) as nbr_purchase
        from
            analytics_transformed.order_item_offers as oio
        left join
            analytics_transformed.instances as i
        on oio.instance_id = i.id
        left join
            analytics_transformed.order_financial_revenue as ofr
        on oio.id = ofr.order_item_id
        where
            date(oio.paid_at) = p_event_date -- noqa
        group by all
    ),

    aggregated_data as (
        select
            transaction_id,
            product_id,
            -- revenue for a specific product
            round(sum(revenue_eur * nbr_purchase), 2) as trx_product_revenue_total,
            -- total revenue for the transaction
            round(sum(sum(revenue_eur * nbr_purchase)) over (partition by transaction_id), 2) as trx_revenue_total,
            -- total revenue for the transaction
            round(sum(sum(addon_revenue_eur * nbr_purchase)) over (partition by transaction_id), 2) as trx_addon_revenue
        from
            transaction_db
        group by
            transaction_id,
            product_id
    ),

    purchases as (
        select
            tidl.*,
            coalesce(aggregated_data.trx_revenue_total, 0) as trx_revenue_total,
            coalesce(aggregated_data.trx_product_revenue_total, 0) as trx_product_revenue_total,
            coalesce(aggregated_data.trx_addon_revenue, 0) as trx_addon_revenue
        from
            transaction_id_lookup as tidl
        left join
            aggregated_data
        on
            tidl.transaction_id = aggregated_data.transaction_id
            and tidl.product_id = aggregated_data.product_id
    )

    select
        ic.*,
        count(distinct p.transaction_id) as transactions,
        coalesce(round(sum(p.trx_revenue_total), 2), 0) as trx_revenue_total,
        coalesce(round(sum(p.trx_product_revenue_total), 2), 0) as trx_product_revenue_total,
        coalesce(round(sum(p.trx_addon_revenue), 2), 0) as trx_addon_revenue
    from
        category_item_impressions_and_p_clicks as ic
    left join
        purchases as p
    on
        ic.event_date = p.event_date
        and ic.product_id = p.product_id
        and ic.market = p.market
    where
        ic.category_id is not null
        and ic.c_page_item_clicks < ic.p_page_item_clicks
    group by all
    having
        count(distinct p.transaction_id) < ic.p_page_item_clicks;
end;

begin
-- test me: google_analytics.load_category_view_item_clicks_purchases
    if ("${runTest}" = "true") then -- noqa
        call google_analytics.load_category_view_item_clicks_purchases ("2024-05-17");
    end if;
end;
