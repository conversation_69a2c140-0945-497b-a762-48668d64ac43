drop procedure if exists maintenance.costs_notification_report;
create procedure maintenance.costs_notification_report (
    report_date date, show_zero_cost bool
)
options (
    description = "Provides BQ costs notification report. Owner: data_engineering"
)
begin
    -- BQ costs changes over time and has to be updated here
    -- Ref. https://cloud.google.com/skus?filter=analysis%20(europe-west3)&currency=EUR&hl=en
    declare bq_price_per_tb_eur float64 default 7.53025;
    declare tb_divisor int64 default cast(power(1024, 4) as int64);

    declare report_date_costs float64;
    declare report_date_tib_billed float64;
    declare last_30_days_costs float64;
    declare sa_rn float64 default 1000;
    declare user_rn float64 default 2000;
    declare has_sa_details bool default false;
    declare has_user_details bool default false;

    create temporary table cost_by_user_day as
    select
        date(creation_time) as cost_date,
        regexp_extract(user_email, r"^([^@]+)@") as iam_user,
        coalesce(user_email like "%gserviceaccount%", false) as is_service_account,
        round(sum(total_bytes_billed) / tb_divisor, 2) as total_tib_billed,
        round(sum(total_bytes_billed) / tb_divisor * bq_price_per_tb_eur, 2) as cost_eur
    from
        `region-eu`.INFORMATION_SCHEMA.JOBS_BY_PROJECT as t -- noqa
    where
        t.start_time between timestamp(report_date - 30) and timestamp(report_date + 1)
        and statement_type != "SCRIPT"
    group by
        date(creation_time),
        user_email;

    if (select count(*) from cost_by_user_day) > 0 then
        set report_date_costs = (
            select round(sum(cost_eur), 2) from cost_by_user_day
            where cost_date = report_date
        );

        set report_date_tib_billed = (
            select round(sum(total_tib_billed), 2) from cost_by_user_day
            where cost_date = report_date
        );

        set last_30_days_costs = (
            select round(avg(cost_eur), 2)
            from
                (
                    select sum(cost_eur) as cost_eur
                    from
                        cost_by_user_day
                    where
                        cost_date < report_date
                    group by
                        cost_date
                )
        );
    end if;

    if (
        select count(*) from cost_by_user_day -- noqa
        where is_service_account = true and cost_date = report_date) > 0 then
        set has_sa_details = true;
    end if;

    if (
        select count(*) from cost_by_user_day -- noqa
        where is_service_account = false and cost_date = report_date) > 0 then
        set has_user_details = true;
    end if;


    with last_30_days_costs_by_sa as (
        select
            iam_user,
            round(avg(cost_eur), 2) as avg_cost
        from
            cost_by_user_day
        where
            cost_date < report_date
        group by
            iam_user
    ),

    report_details as (
        select
            case
                when one_day.is_service_account then one_day.iam_user else
                    initcap(replace(one_day.iam_user, ".", " "))
            end as iam_user,
            one_day.is_service_account,
            one_day.cost_eur,
            "€" || lpad(cast(one_day.cost_eur as string), 8) as cost_kpi,
            maintenance.calculate_kpi_indicator(
                one_day.cost_eur, coalesce(last_30_days_costs_by_sa.avg_cost, 0)
            )
            || " [€"
            || lpad(cast(coalesce(last_30_days_costs_by_sa.avg_cost, 0) as string), 8)
            || "]" as kpi,
            row_number() over (
                partition by one_day.is_service_account
                order by one_day.cost_eur desc
            ) as rn
        from
            (
                select * from cost_by_user_day as c -- noqa
                where c.cost_date = report_date -- noqa
            ) as one_day
        left join
            last_30_days_costs_by_sa as last_30_days_costs_by_sa
        on one_day.iam_user = last_30_days_costs_by_sa.iam_user
        where
            one_day.cost_eur > if(show_zero_cost, -1, 0) -- noqa
    ),

    report as (
        select
            row_number() over () as rn,
            metric_name,
            metric_val
        from
            unnest([
                struct("Report date" as metric_name, cast(report_date as string) as metric_val),
                struct(
                    "Total estimated costs [" || format("%'.2f", bq_price_per_tb_eur) || " €/TiB]" as metric_name,
                    "€ " || cast(report_date_costs as string) as metric_val
                ),
                struct(
                    "KPI [avg costs last 30 days]" as metric_name,
                    (
                        maintenance.calculate_kpi_indicator(report_date_costs, last_30_days_costs)
                        || " [€"
                        || lpad(cast(last_30_days_costs as string), 8)
                        || "]"
                    ) as metric_val
                ),
                struct(
                    "Total TiB billed:" as metric_name,
                    cast(report_date_tib_billed as string) || " TiB" as metric_val
                ),
                struct("Details by service account:" as metric_name, "" as metric_val)
            ])
        where
            has_sa_details
        union all
        select
            rn + sa_rn as rn, -- noqa
            " - " || iam_user as metric_name,
            cost_kpi || lpad(" ", 5) || kpi as metric_val
        from
            report_details
        where
            is_service_account = true
        union all
        select
            user_rn as rn,
            "Details by user:" as metric_name,
            "" as metric_val
        from
            (select has_user_details) as ud -- noqa
        where
            ud.has_user_details
        union all
        select
            rn + user_rn as rn, -- noqa
            " - " || iam_user as metric_name,
            cost_kpi || lpad(" ", 5) || kpi as metric_val
        from
            report_details
        where
            is_service_account = false
    )

    select
        metric_name,
        metric_val
    from
        report
    order by
        rn;
end;

begin
    if ("${runTest}" = "true") then -- noqa
        call maintenance.costs_notification_report (current_date - 1, false);
    end if;
end;
