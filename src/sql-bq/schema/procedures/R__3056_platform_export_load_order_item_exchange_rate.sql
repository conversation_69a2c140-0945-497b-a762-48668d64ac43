drop procedure if exists analytics_transformed.load_order_item_exchange_rate;
create or replace procedure analytics_transformed.load_order_item_exchange_rate (
    p_year int64,
    p_month int64,
    p_day int64,
    p_timestamp int64
) options (
    strict_mode = false,  -- Required to avoid `Cannot query over table without a filter` error.
    description = """Loads `analytics_transformed.order_item_exchange_rate` target table
from the `analytics_raw.order_item_exchange_rate` source table using MERGE statement.
Updates only when exchange_rate has changed.
1. Uses `distinct` as there could be duplicate files for the same timestamp.
2. Does not delete rows to keep historical rates."""
)
begin
    merge into analytics_transformed.order_item_exchange_rate as t
    using (
        select distinct
            order_item_id,
            cast(exchange_rate as numeric) as exchange_rate
        from analytics_raw.order_item_exchange_rate
        where
            `year` = p_year and `month` = p_month and `day` = p_day and `timestamp` = p_timestamp
    ) as s
    on t.order_item_id = s.order_item_id
    when not matched by target then
        insert row
    when matched and t.exchange_rate is distinct from s.exchange_rate then
        update set
            exchange_rate = s.exchange_rate
    ;
end;

drop procedure if exists analytics_transformed.check_order_item_exchange_rate;
create or replace procedure analytics_transformed.check_order_item_exchange_rate ()
options (
    description = "Throws error if duplicate rows are found."
)
begin
    select
        order_item_id,
        if(count(*) > 1, error("Duplicate rows found!"), 0) as duplicate_rows
    from analytics_transformed.order_item_exchange_rate
    group by
        order_item_id
    having count(*) > 1;
end;

begin
    if ("${runTest}" = "true") then -- noqa
        -- test: analytics-pipelines-staging-raw/platform/
        -- order_item_exchange_rate/year=2025/month=1/day=21/timestamp=1737460800
        -- select * from analytics_transformed.order_item_exchange_rate limit 5;
        call analytics_transformed.load_order_item_exchange_rate (2025, 1, 21, 1737460800);
        call analytics_transformed.check_order_item_exchange_rate ();
    end if;
end;
