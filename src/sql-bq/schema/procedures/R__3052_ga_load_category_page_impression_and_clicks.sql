create or replace procedure google_analytics.load_category_page_impression_and_clicks (p_event_date date)
options (
    strict_mode = false,  -- Required to avoid `Cannot query over table without a filter` error.
    description = """Loads `google_analytics.category_page_impression_and_clicks` table
using `select_category_page_impression_and_clicks` function for the given `event_date `partition."""
)
begin
    -- 1. Delete previously loaded partition
    delete from google_analytics.category_page_impression_and_clicks
    where event_date = p_event_date;

    -- 2. Insert the new data
    insert into google_analytics.category_page_impression_and_clicks
    select
        event_date,
        market,
        category_id,
        category_slug,
        product_id,
        impressions,
        avg_price,
        c_page_item_clicks
    from google_analytics.select_category_page_impression_and_clicks(p_event_date);
end;

begin
    -- test me: Reload current partition for the day
    if ("${runTest}" = "true") then -- noqa
        call google_analytics.load_category_page_impression_and_clicks ("2024-05-17");

        select * from google_analytics.category_page_impression_and_clicks
        where event_date = "2024-05-17"
        limit 5;
    end if;
end;
