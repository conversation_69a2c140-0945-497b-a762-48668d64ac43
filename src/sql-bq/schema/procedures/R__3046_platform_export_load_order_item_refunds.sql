-- <PERSON>way will re-run this script if ${changeReason} is updated
drop procedure if exists analytics_transformed.load_order_item_refunds;
create procedure analytics_transformed.load_order_item_refunds (
    p_year int64,
    p_month int64,
    p_day int64,
    p_timestamp int64
) options (
    -- Required to avoid `Cannot query over table 'xyz' without a filter` error.
    strict_mode = false
)
/*
    Fully reload `analytics_transformed.order_item_refunds` target table
    from the `analytics_raw.order_item_refunds` source table.
*/
begin
    truncate table analytics_transformed.order_item_refunds;
    insert into analytics_transformed.order_item_refunds
    select
        t.id,
        t.order_item_id,
        t.order_refund_id,
        cast(t.refunded as numeric) as refunded
    from analytics_raw.order_item_refunds as t
    where
        -- `year` = 2024 and `month` = 8 and `day` > 0 and `timestamp` > 0;
        `year` = p_year and `month` = p_month and `day` = p_day and `timestamp` = p_timestamp;
end;

begin
    if ("${runTest}" = "true") then -- noqa
        -- test: analytics-pipelines-staging-raw/platform/
        -- v_order_item_refunds/year=2024/month=10/day=17/timestamp=1729155137
        call analytics_transformed.load_order_item_refunds (2024, 10, 17, 1729155137);
        select * from analytics_transformed.order_item_refunds limit 5;
    end if;
end;
