drop procedure if exists analytics_transformed.load_full_order_item_refunds;
create or replace procedure analytics_transformed.load_full_order_item_refunds (
    p_year int64,
    p_month int64,
    p_day int64,
    p_timestamp int64
) options (
    strict_mode = false,
    description = """Loads `analytics_transformed.full_order_item_refunds` target table
from the `analytics_raw.full_order_item_refunds` source table using MERGE statement.
1. Uses `distinct` as there could be duplicate files for the same timestamp.
2. Uses `farm_fingerprint` to generate a row_hash for change tracking.
3. Deletes rows that are not present in the source index table."""
)
begin
    merge into analytics_transformed.full_order_item_refunds as t
    using (
        with raw as (
            select distinct *
            from analytics_raw.full_order_item_refunds
            where
                `year` = p_year and `month` = p_month and `day` = p_day and `timestamp` = p_timestamp
        )

        select
            -- PK
            order_item_refund_id,
            -- Business fields
            order_item_id,
            refunded_at,
            updated_at,
            cast(refunded as numeric) as refunded,
            cast(refunded_charge as numeric) as refunded_charge,
            cast(refunded_eur as numeric) as refunded_eur,
            cast(refunded_charge_eur as numeric) as refunded_charge_eur,
            min_reversed_commission_date,
            cast(held_base_commission as numeric) as held_base_commission,
            cast(kept_base_commission as numeric) as kept_base_commission,
            cast(executed_base_commission as numeric) as executed_base_commission,
            cast(held_payment_commission as numeric) as held_payment_commission,
            cast(kept_payment_commission as numeric) as kept_payment_commission,
            cast(executed_payment_commission as numeric) as executed_payment_commission,
            cast(held_base_commission_eur as numeric) as held_base_commission_eur,
            cast(kept_base_commission_eur as numeric) as kept_base_commission_eur,
            cast(executed_base_commission_eur as numeric) as executed_base_commission_eur,
            cast(held_payment_commission_eur as numeric) as held_payment_commission_eur,
            cast(kept_payment_commission_eur as numeric) as kept_payment_commission_eur,
            cast(executed_payment_commission_eur as numeric) as executed_payment_commission_eur,
            -- Change tracking field
            farm_fingerprint(
                concat(
                    coalesce(cast(order_item_id as string), ""),
                    coalesce(cast(refunded_at as string), ""),
                    coalesce(cast(updated_at as string), ""),
                    coalesce(cast(refunded as string), ""),
                    coalesce(cast(refunded_charge as string), ""),
                    coalesce(cast(refunded_eur as string), ""),
                    coalesce(cast(refunded_charge_eur as string), ""),
                    coalesce(cast(min_reversed_commission_date as string), ""),
                    coalesce(cast(held_base_commission as string), ""),
                    coalesce(cast(kept_base_commission as string), ""),
                    coalesce(cast(executed_base_commission as string), ""),
                    coalesce(cast(held_payment_commission as string), ""),
                    coalesce(cast(kept_payment_commission as string), ""),
                    coalesce(cast(executed_payment_commission as string), ""),
                    coalesce(cast(held_base_commission_eur as string), ""),
                    coalesce(cast(kept_base_commission_eur as string), ""),
                    coalesce(cast(executed_base_commission_eur as string), ""),
                    coalesce(cast(held_payment_commission_eur as string), ""),
                    coalesce(cast(kept_payment_commission_eur as string), ""),
                    coalesce(cast(executed_payment_commission_eur as string), "")
                )
            ) as row_hash
        from raw
    ) as s
    on t.order_item_refund_id = s.order_item_refund_id
    when not matched by target then
        insert row
    when matched and s.row_hash is distinct from t.row_hash then
        update set
            t.order_item_id = s.order_item_id,
            t.refunded_at = s.refunded_at,
            t.updated_at = s.updated_at,
            t.refunded = s.refunded,
            t.refunded_charge = s.refunded_charge,
            t.refunded_eur = s.refunded_eur,
            t.refunded_charge_eur = s.refunded_charge_eur,
            t.min_reversed_commission_date = s.min_reversed_commission_date,
            t.held_base_commission = s.held_base_commission,
            t.kept_base_commission = s.kept_base_commission,
            t.executed_base_commission = s.executed_base_commission,
            t.held_payment_commission = s.held_payment_commission,
            t.kept_payment_commission = s.kept_payment_commission,
            t.executed_payment_commission = s.executed_payment_commission,
            t.held_base_commission_eur = s.held_base_commission_eur,
            t.kept_base_commission_eur = s.kept_base_commission_eur,
            t.executed_base_commission_eur = s.executed_base_commission_eur,
            t.held_payment_commission_eur = s.held_payment_commission_eur,
            t.kept_payment_commission_eur = s.kept_payment_commission_eur,
            t.executed_payment_commission_eur = s.executed_payment_commission_eur,
            t.row_hash = s.row_hash
    when not matched by source then
        delete
    ;
end;

drop procedure if exists analytics_transformed.check_full_order_item_refunds;
create or replace procedure analytics_transformed.check_full_order_item_refunds ()
options (
    description = "Throws error if duplicate rows are found."
)
begin
    select
        order_item_refund_id,
        if(count(*) > 1, error("Duplicate rows found!"), 0) as duplicate_rows
    from analytics_transformed.full_order_item_refunds
    group by order_item_refund_id
    having count(*) > 1;
end;

begin
    if ("${runTest}" = "true") then -- noqa
        -- test: analytics-pipelines-staging-raw/platform/full_order_item_refunds/year=2025/month=1/day=21/timestamp=1737460800 -- noqa
        -- select * from analytics_transformed.full_order_item_refunds limit 5;
        call analytics_transformed.load_full_order_item_refunds (2025, 1, 21, 1737460800);
        call analytics_transformed.check_full_order_item_refunds ();
    end if;
end;
