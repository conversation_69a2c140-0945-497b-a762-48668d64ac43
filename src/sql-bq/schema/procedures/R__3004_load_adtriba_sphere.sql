drop procedure if exists analytics_transformed.load_adtriba;
create or replace procedure analytics_transformed.load_adtriba (
    p_run_id string
) options (
    -- Required to avoid `Cannot query over table without a filter` error.
    strict_mode = false
)
/*
    Full reloads `analytics_transformed.adtriba` target table from the source `analytics_raw.adtriba`
    for the given run_id.
*/
begin
    truncate table analytics_transformed.adtriba;
    insert into analytics_transformed.adtriba
    select
        brand,
        country,
        conversion_event,
        device_type,
        `date`,
        sphere_source,
        attributed_conversions,
        attributed_revenue,
        type,
        spend,
        roas,
        cpa,
        optimization_target
    from
        analytics_raw.adtriba_sphere
    where
        run_id = p_run_id;
end;

begin
    if ("${runTest}" = "true") then -- noqa
        -- test
        call analytics_transformed.load_adtriba ("20240712_130530170");
    end if;
end;
