-- <PERSON>way will re-run this script if ${changeReason} is updated
drop procedure if exists analytics_transformed.load_order_item_details;
create or replace procedure analytics_transformed.load_order_item_details (
    p_year int64,
    p_month int64,
    p_day int64,
    p_timestamp int64
) options (
    -- Required to avoid `Cannot query over table 'analytics_raw.order_item_details' without a filter` error.
    strict_mode = false
)
/*
    Loads `analytics_transformed.order_item_details` target table from the source `analytics_raw.order_item_details`
    for the given partition columns using `MERGE` statement.
*/
begin
    merge into analytics_transformed.order_item_details as t
    using (
        with raw as (
            select
                t.*,
                analytics_transformed.is_infinity(t.valid_to) as is_active
            from analytics_raw.order_item_details as t
            where
                `year` = p_year and `month` = p_month and `day` = p_day and `timestamp` = p_timestamp
        )

        select
            id,
            -- Change tracking attributes
            valid_from,
            valid_to,

            -- Supportive fields that ease merge
            if(is_active, valid_from, greatest(valid_from, valid_to)) as modified_at,
            is_active,

            -- Business fields
            state,
            order_item_id,
            shipment_tracking_id,
            item_identifier,
            parcel_tracking_url,
            return_initiated_at
        from raw
    ) as s
    on t.id = s.id and t.valid_from = s.valid_from
    when not matched by target then
        insert row
    when matched and s.valid_to != t.valid_to then
        update set
            valid_to = s.valid_to,
            modified_at = s.modified_at,
            is_active = s.is_active,
            state = s.state,
            order_item_id = s.order_item_id,
            shipment_tracking_id = s.shipment_tracking_id,
            item_identifier = s.item_identifier,
            parcel_tracking_url = s.parcel_tracking_url,
            return_initiated_at = s.return_initiated_at
    ;
end;

begin
    if ("${runTest}" = "true") then -- noqa
        -- analytics-pipelines-staging-raw/platform/v_order_item_details/year=2024/month=10/day=17/timestamp=1729155137
        call analytics_transformed.load_order_item_details (2024, 10, 17, 1729155137);
        select * from analytics_transformed.order_item_details limit 5;
    end if;
end;
