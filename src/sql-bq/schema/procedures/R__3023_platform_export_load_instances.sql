-- <PERSON>way will re-run this script if ${changeReason} is updated
drop procedure if exists analytics_transformed.load_instances;
create or replace procedure analytics_transformed.load_instances (
    p_year int64,
    p_month int64,
    p_day int64,
    p_timestamp int64
) options (
    strict_mode = false,  -- Required to avoid `Cannot query over table without a filter` error.
    description = """Loads `analytics_transformed.instances` target table from the source `analytics_raw.instances`
for the given partition columns using `MERGE` statement.
Uses `distinct` as there could be duplicate files for the same timestamp."""
)
begin
    merge into analytics_transformed.instances as t
    using (
        with raw as (
            select distinct t.*
            from analytics_raw.instances as t
            where
                `year` = p_year and `month` = p_month and `day` = p_day and `timestamp` = p_timestamp
        )

        select
            id,
            -- Change tracking attributes
            created_at,
            updated_at,
            deleted_at,
            -- Supportive fields that ease merge
            greatest(updated_at, coalesce(deleted_at, created_at)) as modified_at,
            if(deleted_at is null, true, false) as is_active,
            -- Business fields
            published,
            product_id,
            name,
            name_en,
            cast(srp as numeric) as srp
        from raw
    ) as s
    on t.id = s.id
    when not matched by target then
        insert row
    when matched and s.modified_at > t.modified_at then
        update set
            created_at = s.created_at,
            updated_at = s.updated_at,
            deleted_at = s.deleted_at,
            modified_at = s.modified_at,
            is_active = s.is_active,
            published = s.published,
            product_id = s.product_id,
            name = s.name,
            name_en = s.name_en,
            srp = s.srp
    ;
end;

drop procedure if exists analytics_transformed.check_instances;
create or replace procedure analytics_transformed.check_instances ()
options (
    description = "Throws error if duplicate rows are found."
)
begin
    select
        id,
        if(count(*) > 1, error("Duplicate rows found!"), 0) as duplicate_rows
    from analytics_transformed.instances
    group by id
    having count(*) > 1;
end;

begin
    if ("${runTest}" = "true") then -- noqa
        -- test: analytics-pipelines-staging-raw/platform/v_instances/year=2025/month=1/day=21/timestamp=1737460800
        -- select * from analytics_transformed.instances limit 5;
        truncate table analytics_transformed.instances;
        call analytics_transformed.load_instances (2025, 1, 21, 1737460800);
        call analytics_transformed.check_instances ();
    end if;
end;
