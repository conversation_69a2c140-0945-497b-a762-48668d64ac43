drop procedure if exists analytics_transformed.load_offers;
create or replace procedure analytics_transformed.load_offers (
    p_year int64,
    p_month int64,
    p_day int64,
    p_timestamp int64
) options (
    -- Required to avoid `Cannot query over table 'analytics_raw.offers' without a filter` error.
    strict_mode = false,
    description = """Loads `analytics_transformed.offers` target table from the source `analytics_raw.offers`
for the given partition columns using `MERGE` statement:
- uses `distinct` as there could be duplicate files for the same timestamp.
- updates only when valid_to changes to avoid unnecessary updates."""
)
begin
    merge into analytics_transformed.offers as t
    using (
        with raw as (
            select distinct
                t.*,
                analytics_transformed.is_infinity(t.valid_to) as is_active
            from analytics_raw.offers as t
            where
                `year` = p_year and `month` = p_month and `day` = p_day and `timestamp` = p_timestamp
        )

        select
            id,
            -- Change tracking attributes
            created_at,
            valid_from,
            valid_to,
            -- Supportive fields that ease merge
            if(is_active, valid_from, greatest(valid_from, valid_to)) as modified_at,
            is_active,
            -- Business fields
            state,
            instance_id,
            merchant_id,
            warranty,
            stock,
            grading,
            hidden,
            tax_difference,
            shipping_profile_id,
            shipping_profile_valid_from,
            reference_currency_code,
            cast(reference_price as numeric) as reference_price,
            cast(reference_min_flex_price as numeric) as reference_min_flex_price,
            sku
        from raw
    ) as s
    on t.id = s.id and t.valid_from = s.valid_from
    when not matched by target then
        insert row
    when matched and s.valid_to != t.valid_to then
        update set
            valid_to = s.valid_to,
            modified_at = s.modified_at,
            is_active = s.is_active,
            state = s.state,
            instance_id = s.instance_id,
            merchant_id = s.merchant_id,
            warranty = s.warranty,
            stock = s.stock,
            grading = s.grading,
            hidden = s.hidden,
            tax_difference = s.tax_difference,
            shipping_profile_id = s.shipping_profile_id,
            shipping_profile_valid_from = s.shipping_profile_valid_from,
            reference_currency_code = s.reference_currency_code,
            reference_price = s.reference_price,
            reference_min_flex_price = s.reference_min_flex_price,
            sku = s.sku
    ;
end;

drop procedure if exists analytics_transformed.check_offers;
create or replace procedure analytics_transformed.check_offers ()
options (
    description = "Throws error if duplicate rows are found."
)
begin
    select
        valid_from,
        id,
        if(count(*) > 1, error("Duplicate rows found!"), 0) as duplicate_rows
    from analytics_transformed.offers
    group by valid_from, id
    having count(*) > 1;
end;

begin
    if ("${runTest}" = "true") then -- noqa
        -- test: analytics-pipelines-staging-raw/platform/v_offers/year=2025/month=1/day=21/timestamp=1737460800
        -- select * from analytics_transformed.offers limit 5;
        truncate table analytics_transformed.offers;
        call analytics_transformed.load_offers (2025, 1, 21, 1737460800);
        call analytics_transformed.check_offers ();
    end if;
end;
