-- Flyway will re-run this script if ${changeReason} is updated
drop procedure if exists analytics_transformed.load_product_rankings;
create or replace procedure analytics_transformed.load_product_rankings (
    p_year int64,
    p_month int64,
    p_day int64,
    p_timestamp int64
)
options (
    strict_mode = false,  -- Required to avoid `Cannot query over table without a filter` error.
    description = """Loads analytics_transformed.product_rankings target table from analytics_raw.product_rankings
source table using MERGE statement. Updates only when rankings have changed to avoid unnecessary updates."""
)
begin
    merge into analytics_transformed.product_rankings as t
    using (
        with raw as (
            select distinct *
            from analytics_raw.product_rankings
            where year = p_year
                and month = p_month
                and day = p_day
                and timestamp = p_timestamp
        )

        select
            -- PK
            product_id,
            category_id,
            market_country,
            -- Change tracking attributes
            updated_at,
            -- Rank fields
            rank,
            rank_b
        from raw
    ) as s
    on t.product_id = s.product_id
        and t.category_id = s.category_id
        and t.market_country = s.market_country
    when not matched by target then
        insert row
    when matched and s.updated_at > t.updated_at then
        update set
            rank = s.rank,
            rank_b = s.rank_b,
            updated_at = s.updated_at
    ;
end;

drop procedure if exists analytics_transformed.check_product_rankings;
create or replace procedure analytics_transformed.check_product_rankings ()
options (
    description = "Throws error if duplicate rows are found."
)
begin
    select
        product_id,
        category_id,
        market_country,
        if(count(*) > 1, error("Duplicate rows found!"), 0) as duplicate_rows
    from analytics_transformed.product_rankings
    group by product_id, category_id, market_country
    having count(*) > 1;
end;

begin
    if ("${runTest}" = "true") then -- noqa
        -- test: analytics-pipelines-staging-raw/platform/v_product_rankings
        --      /year=2025/month=1/day=21/timestamp=1737460800
        -- select * from analytics_transformed.product_rankings limit 5;
        call analytics_transformed.load_product_rankings (2025, 1, 21, 1737460800);
        call analytics_transformed.check_product_rankings ();
    end if;
end;
