drop procedure if exists analytics_transformed.load_instance_attribute_values;
create or replace procedure analytics_transformed.load_instance_attribute_values (
    p_year int64,
    p_month int64,
    p_day int64,
    p_timestamp int64
) options (
    strict_mode = false,  -- Required to avoid `Cannot query over table without a filter` error.
    description = """Loads `analytics_transformed.instance_attribute_values` target table
from the `analytics_raw.instance_attribute_values` source table using `MERGE` statement.
Uses `distinct` as there could be duplicate files for the same timestamp."""
)
begin
    merge into analytics_transformed.instance_attribute_values as t
    using (
        select distinct
            instance_id,
            attribute_id,
            attribute_value_id
        from analytics_raw.instance_attribute_values
        where
            `year` = p_year and `month` = p_month and `day` = p_day and `timestamp` = p_timestamp
    ) as s
    on t.instance_id = s.instance_id and t.attribute_value_id = s.attribute_value_id
    when not matched by target then
        insert row
    when matched and t.attribute_id != s.attribute_id then
        update set
            attribute_id = s.attribute_id
    when not matched by source then
        delete
    ;
end;

drop procedure if exists analytics_transformed.check_instance_attribute_values;
create or replace procedure analytics_transformed.check_instance_attribute_values ()
options (
    description = "Throws error if duplicate rows are found."
)
begin
    select
        instance_id,
        attribute_value_id,
        if(count(*) > 1, error("Duplicate rows found!"), 0) as duplicate_rows
    from analytics_transformed.instance_attribute_values
    group by
        instance_id,
        attribute_value_id
    having count(*) > 1;
end;

begin
    if ("${runTest}" = "true") then -- noqa
        -- test: platform/v_instance_attribute_values/year=2025/month=1/day=21/timestamp=1737460800
        -- select * from analytics_transformed.instance_attribute_values limit 10;
        call analytics_transformed.load_instance_attribute_values (
            2025,
            1,
            21,
            1737460800
        );
        call analytics_transformed.check_instance_attribute_values ();
    end if;
end;
