create or replace procedure google_analytics.load_purchases_and_clicks (partition_id string)
/*
    The stored procedure replaces a whole day partition with the new data:

    param: partition_id cannot have default value, thus pass '' if you want to reload for current day
    - use string as data type to easily pass it from python code
    - use '%Y%m%d' date format compatible with the `purchases_and_clicks_view.table_date`

    returns: table_partition being reloaded

    Ref.: https://cloud.google.com/bigquery/docs/using-dml-with-partitioned-tables#multi-statement_transaction
*/
begin
    declare date_format string default "%Y%m%d";
    declare table_partition date default current_date();

    if length(partition_id) > 0 then
        set table_partition = date(parse_date(date_format, partition_id));
    end if;

    -- 1. Delete the entire partition from google_analytics.purchases_and_clicks
    delete from google_analytics.purchases_and_clicks
    where table_date = table_partition;

    -- 2. Insert the new data
    insert into google_analytics.purchases_and_clicks
    select * from google_analytics.purchases_and_clicks_view
    where table_date = table_partition;
end;

begin
    -- test me: Reload current partition for the day
    if ("${runTest}" = "true") then -- noqa
        call google_analytics.load_purchases_and_clicks ("");
    end if;
end;
