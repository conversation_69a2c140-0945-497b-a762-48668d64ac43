-- Flyway will re-run this script if ${changeReason} is updated
drop procedure if exists analytics_transformed.load_partners;
create or replace procedure analytics_transformed.load_partners (
    p_year int64,
    p_month int64,
    p_day int64,
    p_timestamp int64
) options (
    -- Required to avoid `Cannot query over table 'xyz' without a filter` error.
    strict_mode = false
)
/*
    Fully reloads `analytics_transformed.partners` target table
    from the `analytics_raw.partners` source table.
*/
begin
    truncate table analytics_transformed.partners;
    insert into analytics_transformed.partners
    select
        t.id,
        t.name,
        t.slug
    from analytics_raw.partners as t
    where
        -- `year` = 2024 and `month` = 8 and `day` > 0 and `timestamp` > 0;
        `year` = p_year and `month` = p_month and `day` = p_day and `timestamp` = p_timestamp;
end;

begin
    if ("${runTest}" = "true") then -- noqa
        -- analytics-pipelines-staging-raw/platform/v_partners/year=2024/month=10/day=17/timestamp=1729155137
        call analytics_transformed.load_partners (2024, 10, 17, 1729155137);
        select * from analytics_transformed.partners limit 5;
    end if;
end;
