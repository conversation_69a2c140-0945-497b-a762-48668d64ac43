drop procedure if exists analytics_transformed.load_addresses;
create or replace procedure analytics_transformed.load_addresses (
    p_year int64,
    p_month int64,
    p_day int64,
    p_timestamp int64
) options (
    -- Required to avoid `Cannot query over table 'analytics_raw.addresses' without a filter` error.
    strict_mode = false
)
/*
    Loads `analytics_transformed.addresses` target table from the source `analytics_raw.addresses`
    for the given partition columns using `MERGE` statement.
*/
begin
    merge into analytics_transformed.addresses as t
    using (
        with raw as (
            select
                t.*,
                analytics_transformed.is_infinity(t.valid_to) as is_active
            from analytics_raw.addresses as t
            where
                `year` = p_year and `month` = p_month and `day` = p_day and `timestamp` = p_timestamp
        )

        select
            id,
            -- Change tracking attributes
            valid_from,
            valid_to,
            -- Supportive fields that ease merge
            if(is_active, valid_from, greatest(valid_from, valid_to)) as modified_at,
            is_active,
            -- Mandatory address fields
            owner_id,
            first_name,
            family_name,
            country,
            post_code,
            town,
            street_name,
            house_no,
            phone,
            -- Optional address fields
            address_type,
            supplement,
            male,
            company,
            vatin,
            personal_vatin
        from raw
    ) as s
    on t.id = s.id and t.valid_from = s.valid_from
    when not matched by target then
        insert row
    when matched and s.valid_to != t.valid_to then
        update set
            valid_to = s.valid_to,
            modified_at = s.modified_at,
            is_active = s.is_active,
            owner_id = s.owner_id,
            first_name = s.first_name,
            family_name = s.family_name,
            country = s.country,
            post_code = s.post_code,
            town = s.town,
            street_name = s.street_name,
            house_no = s.house_no,
            phone = s.phone,
            address_type = s.address_type,
            supplement = s.supplement,
            male = s.male,
            company = s.company,
            vatin = s.vatin,
            personal_vatin = s.personal_vatin
    ;
end;

begin
    if ("${runTest}" = "true") then -- noqa
        -- analytics-pipelines-staging-raw/platform/v_addresses/year=2024/month=10/day=17/timestamp=1729155137
        call analytics_transformed.load_addresses (2024, 10, 17, 1729155137);
        select * from analytics_transformed.addresses limit 5;
    end if;
end;
