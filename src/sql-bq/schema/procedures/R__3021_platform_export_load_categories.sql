drop procedure if exists analytics_transformed.load_categories;
create or replace procedure analytics_transformed.load_categories (
    p_year int64,
    p_month int64,
    p_day int64,
    p_timestamp int64
) options (
    strict_mode = false,  -- Required to avoid `Cannot query over table without a filter` error.
    description = """Loads `analytics_transformed.categories` target table from the source `analytics_raw.categories`
for the given partition columns using `MERGE` statement.
Uses `distinct` as there could be duplicate files for the same timestamp."""
)
begin
    merge into analytics_transformed.categories as t
    using (
        with raw as (
            select distinct t.*
            from analytics_raw.categories as t
            where
                `year` = p_year and `month` = p_month and `day` = p_day and `timestamp` = p_timestamp
        )

        select
            id,
            -- Change tracking attributes
            created_at,
            updated_at,
            deleted_at,
            -- Supportive fields that ease merge
            greatest(updated_at, coalesce(deleted_at, created_at)) as modified_at,
            if(deleted_at is null, true, false) as is_active,
            -- Business fields
            category_name,
            brand,
            category_type,
            product_category
        from raw
    ) as s
    on t.id = s.id
    when not matched by target then
        insert row
    when matched and s.modified_at > t.modified_at then
        update set
            -- Change tracking attributes
            created_at = s.created_at,
            updated_at = s.updated_at,
            deleted_at = s.deleted_at,
            -- Supportive fields that ease merge
            modified_at = s.modified_at,
            is_active = s.is_active,
            -- Business fields
            category_name = s.category_name,
            brand = s.brand,
            category_type = s.category_type,
            product_category = s.product_category
    ;
end;

drop procedure if exists analytics_transformed.check_categories;
create or replace procedure analytics_transformed.check_categories ()
options (
    description = "Throws error if duplicate rows are found."
)
begin
    select
        id,
        if(count(*) > 1, error("Duplicate rows found!"), 0) as duplicate_rows
    from analytics_transformed.categories
    group by id
    having count(*) > 1;
end;

begin
    if ("${runTest}" = "true") then -- noqa
        -- test: analytics-pipelines-staging-raw/platform/v_categories/year=2025/month=1/day=21/timestamp=1737460800
        truncate table analytics_transformed.categories;
        call analytics_transformed.load_categories (2025, 1, 21, 1737460800);
        call analytics_transformed.check_categories ();
    end if;
end;
