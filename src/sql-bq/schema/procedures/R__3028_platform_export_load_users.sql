drop procedure if exists analytics_transformed.load_users;
create or replace procedure analytics_transformed.load_users (
    p_year int64,
    p_month int64,
    p_day int64,
    p_timestamp int64
) options (
    -- Required to avoid `Cannot query over table 'analytics_raw.users' without a filter` error.
    strict_mode = false
)
/*
    Loads `analytics_transformed.users` target table from the source `analytics_raw.users`
    for the given partition columns using `MERGE` statement.
*/
begin
    merge into analytics_transformed.users as t
    using (
        with raw as (
            select
                t.id,
                t.created_at,
                t.updated_at,
                t.deleted_at,
                t.email,
                t.first_name,
                t.family_name,
                t.`type`,
                t.language,
                t.merchant_id
            from analytics_raw.users as t
            where
                `year` = p_year and `month` = p_month and `day` = p_day and `timestamp` = p_timestamp
        )

        select
            id,
            -- Change tracking attributes
            created_at,
            updated_at,
            deleted_at,
            -- Supportive fields that ease merge
            greatest(updated_at, coalesce(deleted_at, created_at)) as modified_at,
            if(deleted_at is null, true, false) as is_active,
            -- Business fields
            email,
            first_name,
            family_name,
            `type`,
            language,
            merchant_id
        from raw
    ) as s
    on t.id = s.id
    when not matched by target then
        insert row
    when matched and s.modified_at > t.modified_at then
        update set
            created_at = s.created_at,
            updated_at = s.updated_at,
            deleted_at = s.deleted_at,
            modified_at = s.modified_at,
            is_active = s.is_active,
            email = s.email,
            first_name = s.first_name,
            family_name = s.family_name,
            `type` = s.`type`,
            language = s.language,
            merchant_id = s.merchant_id
    ;
end;

begin
    if ("${runTest}" = "true") then -- noqa
        -- test: analytics-pipelines-staging-raw/platform/v_users/year=2024/month=10/day=17/timestamp=1729155137
        call analytics_transformed.load_users (2024, 10, 17, 1729155137);
        select * from analytics_transformed.users limit 5;
    end if;
end;
