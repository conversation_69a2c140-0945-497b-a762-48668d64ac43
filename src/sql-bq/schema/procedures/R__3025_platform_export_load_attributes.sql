-- <PERSON>way will re-run this script if ${changeReason} is updated
drop procedure if exists analytics_transformed.load_attributes;
create or replace procedure analytics_transformed.load_attributes (
    p_year int64,
    p_month int64,
    p_day int64,
    p_timestamp int64
) options (
    strict_mode = false,  -- Required to avoid `Cannot query over table without a filter` error.
    description = """Loads `analytics_transformed.attributes` target table
from the `analytics_raw.attributes` source table using MERGE statement.
1. Uses `distinct` as there could be duplicate files for the same timestamp.
2. Uses `farm_fingerprint` to generate a row_hash for change tracking.
3. Does not delete rows not present in source to keep historical data."""
)
begin
    merge into analytics_transformed.attributes as t
    using (
        with raw as (
            select distinct *
            from analytics_raw.attributes
            where
                `year` = p_year and `month` = p_month and `day` = p_day and `timestamp` = p_timestamp
        )

        select
            -- PK
            attribute_id,
            attribute_value_id,
            -- Business fields
            attribute_name,
            attribute_value,
            `type`,
            filterable,
            `precision`,
            unit,
            value_bool,
            cast(value_numeric as numeric) as value_numeric,
            value_enum_id,
            parse_json(`value`) as `value`,
            -- Change tracking field
            farm_fingerprint(
                concat(
                    coalesce(attribute_name, ""),
                    coalesce(attribute_value, ""),
                    coalesce(`type`, ""),
                    coalesce(cast(filterable as string), ""),
                    coalesce(cast(`precision` as string), ""),
                    coalesce(unit, ""),
                    coalesce(cast(value_bool as string), ""),
                    coalesce(cast(value_numeric as string), ""),
                    coalesce(cast(value_enum_id as string), ""),
                    coalesce(`value`, "")
                )
            ) as row_hash
        from raw
    ) as s
    on t.attribute_id = s.attribute_id and t.attribute_value_id = s.attribute_value_id
    when not matched by target then
        insert row
    when matched and s.row_hash is distinct from t.row_hash then
        update set
            attribute_name = s.attribute_name,
            attribute_value = s.attribute_value,
            `type` = s.`type`,
            filterable = s.filterable,
            `precision` = s.`precision`,
            unit = s.unit,
            value_bool = s.value_bool,
            value_numeric = s.value_numeric,
            value_enum_id = s.value_enum_id,
            `value` = s.value,
            row_hash = s.row_hash
    ;
end;

drop procedure if exists analytics_transformed.check_attributes;
create or replace procedure analytics_transformed.check_attributes ()
options (
    description = "Throws error if duplicate rows are found."
)
begin
    select
        attribute_id,
        attribute_value_id,
        if(count(*) > 1, error("Duplicate rows found!"), 0) as duplicate_rows
    from analytics_transformed.attributes
    group by
        attribute_id,
        attribute_value_id
    having count(*) > 1;
end;

begin
    if ("${runTest}" = "true") then -- noqa
        -- select * from analytics_transformed.attributes limit 10;
        -- analytics-pipelines-staging-raw/platform/v_attributes/year=2025/month=1/day=21/timestamp=1737460800
        call analytics_transformed.load_attributes (2025, 1, 21, 1737460800);
        call analytics_transformed.check_attributes ();
    end if;
end;
