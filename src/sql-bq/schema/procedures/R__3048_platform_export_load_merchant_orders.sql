-- <PERSON>way will re-run this script if ${changeReason} is updated
drop procedure if exists analytics_transformed.load_merchant_orders;
create procedure analytics_transformed.load_merchant_orders (
    p_year int64,
    p_month int64,
    p_day int64,
    p_timestamp int64
) options (
    -- Required to avoid `Cannot query over table 'xyz' without a filter` error.
    strict_mode = false
)
/*
    Fully reload `analytics_transformed.merchant_orders` target table
    from the `analytics_raw.merchant_orders` source table.
*/
begin
    truncate table analytics_transformed.merchant_orders;
    insert into analytics_transformed.merchant_orders
    select
        t.order_id,
        t.merchant_id,
        cast(t.total_charged as numeric) as total_charged,
        cast(t.total_paid as numeric) as total_paid,
        cast(t.total_refunded as numeric) as total_refunded,
        cast(t.total_discount as numeric) as total_discount
    from analytics_raw.merchant_orders as t
    where
        -- `year` = 2024 and `month` = 8 and `day` > 0 and `timestamp` > 0;
        `year` = p_year and `month` = p_month and `day` = p_day and `timestamp` = p_timestamp;
end;

begin
    if ("${runTest}" = "true") then -- noqa
        -- test: analytics-pipelines-staging-raw/platform/
        -- v_merchant_orders/year=2024/month=10/day=17/timestamp=1729155137
        call analytics_transformed.load_merchant_orders (2024, 10, 17, 1729155137);
        select * from analytics_transformed.merchant_orders limit 5;
    end if;
end;
