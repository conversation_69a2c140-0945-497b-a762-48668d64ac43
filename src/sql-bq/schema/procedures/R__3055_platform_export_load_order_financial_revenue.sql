drop procedure if exists analytics_transformed.load_order_financial_revenue;
create or replace procedure analytics_transformed.load_order_financial_revenue (
    p_year int64,
    p_month int64,
    p_day int64,
    p_timestamp int64
)
options (
    strict_mode = false,
    description = """Loads `analytics_transformed.order_financial_revenue` target table
from the `analytics_raw.order_financial_revenue` source table using MERGE statement.
1. Uses `distinct` as there could be duplicate files for the same timestamp.
2. Uses `farm_fingerprint` to generate a row_hash for change tracking.
3. Deletes rows that are not present in the source table."""
)
begin
    merge into analytics_transformed.order_financial_revenue as t
    using (
        with raw as (
            select distinct *
            from analytics_raw.order_financial_revenue
            where
                `year` = p_year and `month` = p_month and `day` = p_day and `timestamp` = p_timestamp
        )

        select
            -- PK
            order_item_id,
            -- Business fields
            order_id,
            cast(revenue as numeric) as revenue,
            cast(addon_revenue as numeric) as addon_revenue,
            cast(gmv as numeric) as gmv,
            cast(discount_eur as numeric) as discount_eur,
            cast(service_fee_revenue_eur as numeric) as service_fee_revenue_eur,
            -- Change tracking field
            farm_fingerprint(
                concat(
                    coalesce(cast(order_id as string), ""),
                    coalesce(cast(revenue as string), ""),
                    coalesce(cast(addon_revenue as string), ""),
                    coalesce(cast(gmv as string), ""),
                    coalesce(cast(discount_eur as string), ""),
                    coalesce(cast(service_fee_revenue_eur as string), "")
                )
            ) as row_hash
        from raw
    ) as s
    on t.order_item_id = s.order_item_id
    when not matched by target then
        insert row
    when matched and s.row_hash is distinct from t.row_hash then
        update set
            t.order_id = s.order_id,
            t.revenue = s.revenue,
            t.addon_revenue = s.addon_revenue,
            t.gmv = s.gmv,
            t.discount_eur = s.discount_eur,
            t.service_fee_revenue_eur = s.service_fee_revenue_eur,
            t.row_hash = s.row_hash
    when not matched by source then
        delete
    ;
end;

drop procedure if exists analytics_transformed.check_order_financial_revenue;
create or replace procedure analytics_transformed.check_order_financial_revenue ()
options (
    description = "Throws error if duplicate rows or missing references are found."
)
begin
    select
        order_item_id,
        if(count(*) > 1, error("Duplicate rows found!"), 0) as duplicate_rows
    from analytics_transformed.order_financial_revenue
    group by order_item_id
    having count(*) > 1;
end;

-- test: select * from analytics_transformed.order_financial_revenue limit 5;
begin
    if ("${runTest}" = "true") then -- noqa
        -- analytics-pipelines-staging-raw/platform/order_financial_revenue/year=2025/month=1/day=21/timestamp=1737460800  -- noqa
        -- select * from analytics_transformed.order_financial_revenue limit 5;
        call analytics_transformed.load_order_financial_revenue (2025, 1, 21, 1737460800);
        call analytics_transformed.check_order_financial_revenue ();
    end if;
end;
