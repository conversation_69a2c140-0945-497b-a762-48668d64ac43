drop procedure if exists analytics_transformed.load_product_categories;
create procedure analytics_transformed.load_product_categories (
    p_year int64,
    p_month int64,
    p_day int64,
    p_timestamp int64
) options (
    strict_mode = false,  -- Required to avoid `Cannot query over table without a filter` error.
    description = """Loads `analytics_transformed.product_categories` target table
from the `analytics_raw.product_categories` source table using `MERGE` statement.
1. Uses `distinct` as there could be duplicate files for the same timestamp.
2. Deletes rows that are not present in the source table."""
)
begin
    merge into analytics_transformed.product_categories as t
    using (
        select distinct
            product_id,
            main_category_id,
            show_category_id
        from analytics_raw.product_categories
        where
            `year` = p_year and `month` = p_month and `day` = p_day and `timestamp` = p_timestamp
    ) as s
    on t.product_id = s.product_id and t.show_category_id = s.show_category_id
    when not matched by target then
        insert row
    when matched and t.main_category_id != s.main_category_id then
        update set main_category_id = s.main_category_id
    when not matched by source then
        delete
    ;
end;

drop procedure if exists analytics_transformed.check_product_categories;
create or replace procedure analytics_transformed.check_product_categories ()
options (
    description = "Throws error if duplicate rows are found."
)
begin
    select
        product_id,
        show_category_id,
        if(count(*) > 1, error("Duplicate rows found!"), 0) as duplicate_rows
    from analytics_transformed.product_categories
    group by
        product_id,
        show_category_id
    having count(*) > 1;
end;

begin
    if ("${runTest}" = "true") then -- noqa
        -- analytics-pipelines-staging-raw/platform/v_product_categories/year=2025/month=1/day=21/timestamp=1737460800
        call analytics_transformed.load_product_categories (2025, 1, 21, 1737460800);
        call analytics_transformed.check_product_categories ();
    end if;
end;
