# https://docs.sqlfluff.com/en/stable/configuration.html#new-project-configuration
[sqlfluff]
sql_file_exts = .sql
dialect = bigquery
max_line_length = 120
# https://docs.sqlfluff.com/en/3.3.0/reference/rules.html
# references.from - causes false positive for BQ external table partition columns and nested fields
exclude_rules = ambiguous.column_count, structure.column_order, references.from

[sqlfluff:indentation]
indented_joins = False
template_blocks_indent = True
indented_using_on = False
allow_implicit_indents = True

[sqlfluff:rules:capitalisation.keywords]
capitalisation_policy = lower

[sqlfluff:rules:capitalisation.identifiers]
capitalisation_policy = lower

[sqlfluff:rules:capitalisation.functions]
extended_capitalisation_policy = lower

[sqlfluff:rules:capitalisation.literals]
capitalisation_policy = lower

[sqlfluff:rules:capitalisation.types]
extended_capitalisation_policy = lower

[sqlfluff:rules:aliasing.table]
aliasing = explicit

[sqlfluff:rules:aliasing.column]
aliasing = explicit
