import re
import uuid
from datetime import datetime
from pathlib import Path
from typing import Any, Optional


def get_run_id(dt: datetime) -> str:
    """
    Gets current date/time with milliseconds
    as a 'YYYMMDD_HHMMSSf' formatted string with milliseconds truncated to the 3 digits
    """
    return dt.strftime("%Y%m%d_%H%M%S%f")[:-3]


def to_space_separated(string: str) -> str:
    """Converts a string to comma separated case."""
    # handling PascalCase
    string = re.sub(r"(?<!^)(?=[A-Z])", " ", string)
    # replacing non-alphanumeric characters with spaces
    string = re.sub(r"[^a-zA-Z0-9]", " ", string)
    # removing extra spaces
    string = " ".join(string.split())

    return string.lower()


def to_snake_case(string: str) -> str:
    """Converts a string to snake case."""
    words = to_space_separated(string).split()

    return "_".join(word.lower() for word in words).strip("_")


def to_kebab_case(string: str) -> str:
    """Converts a string to kebab case."""
    words = to_space_separated(string).split()

    return "-".join(word.lower() for word in words).strip("-")


def to_camel_case(string: str) -> str:
    """Converts a string to camel case."""
    words = to_space_separated(string).split()
    if not words:
        return ""

    head, *tail = words

    return head + "".join(word.title() for word in tail)


def format_file(file_path: Path, **kwargs: Any) -> str:
    """
    Returns a formatted version of a file, using substitutions from kwargs.
    The substitutions are identified by braces ('{' and '}').

    :param file_path: File path
    :param kwargs: keyword arguments, like start_date = "2024-08-01"
    :returns: formatted content of the input file
    """
    with open(file_path, "r") as file:
        text = file.read()

    return text.format(**kwargs)


def string_to_bool(string: str | Any) -> bool:
    """Converts Any value to bool."""
    string = str(string)

    return string.lower() in [
        "true",
        "1",
        "y",
        "yes",
    ]


def get_unique_identifier(length: int = 8) -> str:
    """
    Generate short UUID-based string.
    Good for unique identifiers.
    :param length: length of the string
    :returns: string representation of the UUID
    """

    if length < 4 or length > 32:
        raise ValueError("Length must be between 4 and 32!")

    return uuid.uuid4().hex[:length]


def format_bytes_pretty(bytes_value: int | float) -> str:
    """
    Converts a size in bytes to a human-readable format with the appropriate unit.
    Similar to Postgres's pg_size_pretty function.

    :param bytes_value: size in bytes
    :returns: formatted string with value and unit (B, KB, MB, GB, TB)

    Examples:
    >>> format_bytes_pretty(1023)
    '1023 B'
    >>> format_bytes_pretty(1024)
    '1.00 KB'
    >>> format_bytes_pretty(1048575)
    '1023.99 KB'
    >>> format_bytes_pretty(1048576)
    '1.00 MB'
    >>> format_bytes_pretty(1073741824)
    '1.00 GB'
    >>> format_bytes_pretty(1099511627776)
    '1.00 TB'
    """
    units = ["KB", "MB", "GB", "TB"]
    unit_index = 0

    if bytes_value < 0:
        raise ValueError("Bytes value cannot be negative")

    if bytes_value < 1024:
        return f"{bytes_value} B"

    value = bytes_value / 1024

    while value >= 1024 and unit_index < len(units) - 1:
        value /= 1024
        unit_index += 1

    # Format with appropriate precision
    if value < 10:
        return f"{value:.2f} {units[unit_index]}"
    elif value < 100:
        return f"{value:.1f} {units[unit_index]}"
    else:
        return f"{int(value)} {units[unit_index]}"


def escape_sql(arg: Optional[Any]) -> Optional[str]:
    """
    Escapes single quotes in a string for SQL queries by replacing them with double quotes.

    :param arg: Input string to escape
    :returns: Escaped string
    """
    if arg is None:
        return arg

    string = str(arg)
    return string.replace("'", '"')
