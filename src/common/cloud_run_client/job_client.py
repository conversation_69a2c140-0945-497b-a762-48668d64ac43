import time
from dataclasses import dataclass
from functools import cached_property
from http import HTTPStatus
from typing import TYPE_CHECKING, Optional, TypeAlias

from googleapiclient import discovery
from googleapiclient.errors import HttpError

from common.cloud_run_client.base_client import CloudRunClient
from common.cloud_run_client.model import (
    CloudRunContainerOverride,
    CloudRunEnvironmentVariable,
    CloudRunJobExecution,
    CloudRunJobRequest,
    CloudRunJobRunRequest,
    CloudRunOverrides,
)
from common.logger import Config, get_logger

if TYPE_CHECKING:
    from googleapiclient._apis.run.v2 import CloudRunResource

    JobsResource: TypeAlias = CloudRunResource.ProjectsResource.LocationsResource.JobsResource

logger = get_logger()


@dataclass(frozen=True)
class CloudRunJobClientConfig(Config):
    """
    - sleep_seconds: number of seconds to sleep while waiting for a job to complete
    - timeout_seconds: maximum seconds to wait before timing out
    """

    sleep_in_seconds: int = 10
    timeout_seconds: int = 3600 * 24  # 24 hours


class CloudRunJobClient(CloudRunClient):
    """
    Custom Cloud Run job client to interact with the service
    Ref. https://googleapis.github.io/google-api-python-client/docs/dyn/run_v2.projects
    """

    _config: CloudRunJobClientConfig

    def __init__(self, config: CloudRunJobClientConfig):
        # Construct the service object for the interacting with the Cloud Run API.
        service = discovery.build("run", "v2")
        super().__init__(config, service)
        self._config = config

    @cached_property
    def resource_api_path(self) -> str:
        return f"projects/{self._config.project_id}/locations/{self._config.location}/jobs"

    def get_resources(self) -> "JobsResource":
        return self._service.projects().locations().jobs()  # noqa

    def list_executions(self, job_name: str) -> list[CloudRunJobExecution]:
        """
        List Cloud Run job execution, results are sorted by creation time, descending.
        :param job_name: name of the resource
        Ref. https://googleapis.github.io/google-api-python-client/docs/dyn/run_v2.projects.locations.jobs.html#list
        :returns: list of resource executions
        """

        logger.info(f"Listing '{job_name}' Cloud Run job executions...")
        request = self.get_resources().executions().list(parent=f"{self.resource_api_path}/{job_name}")
        response = request.execute()

        executions = response.get("executions", [])
        if not executions:
            logger.warning(f"No '{job_name}' Cloud Run job executions found!")

        return [CloudRunJobExecution.from_dict(dict(execution)) for execution in executions]

    def get_last_completed_execution(self, job_name: str) -> Optional[CloudRunJobExecution]:
        """
        Get last Cloud Run job completed execution.
        :param job_name: name of the resource
        :returns: an object describing the resource's execution
        """
        logger.info(f"Getting the last '{job_name}' Cloud Run job execution...")

        executions = self.list_executions(job_name)  # sorted by creation time, descending
        completed_executions = (execution for execution in executions if not execution.is_in_progress)

        return next(completed_executions, None)

    def run(self, job_name: str, body: CloudRunJobRunRequest, wait_for_completion: bool = True) -> CloudRunJobExecution:
        """
        Run a Cloud Run job.
        :param job_name: name of the job to run
        :param body: request body parameters
        :param wait_for_completion: whether to wait for job completion
        Ref. https://googleapis.github.io/google-api-python-client/docs/dyn/run_v2.projects.locations.jobs.html#run
        :returns: an object describing the job's execution
        """
        logger.info(f"Running '{job_name}' Cloud Run job...")

        # Prepare the GoogleCloudRunV2RunJobRequest
        request_body = CloudRunJobRequest()

        # Add environment variables if provided
        if body.environment_variables:
            # Create a container override with environment variables
            env_vars = [
                CloudRunEnvironmentVariable(name=name, value=value)
                for name, value in body.environment_variables.items()
            ]
            container_override = CloudRunContainerOverride(env=env_vars)
            overrides = CloudRunOverrides(container_overrides=[container_override])

            request_body.overrides = overrides

        # Run the job
        request = self.get_resources().run(
            name=f"{self.resource_api_path}/{job_name}", body=request_body.to_dict()  # noqa
        )

        response = request.execute()
        execution = CloudRunJobExecution.from_dict(response.get("metadata"))

        if wait_for_completion:
            logger.info(f"Waiting for job execution '{execution.short_name}' to complete...")
            execution = self._wait_for_execution_to_complete(job_name, execution.short_name)

        return execution

    def get_execution(self, job_name: str, execution_name: str) -> Optional[CloudRunJobExecution]:
        """
        Get Cloud Run job execution if exists.
        :param job_name: can be job name only or fully qualified name (including project and region)
        :param execution_name: name of the execution if found, otherwise None
        Ref. https://googleapis.github.io/google-api-python-client/docs/dyn/run_v2.projects.locations.jobs.executions.html#get # noqa
        :returns: an object describing the jon's execution
        """
        full_execution_name = (
            f"{job_name}/executions/{execution_name}"
            if job_name.startswith("projects")
            else f"{self.resource_api_path}/{job_name}/executions/{execution_name}"
        )

        logger.info(f"Getting '{job_name}' Cloud Run job '{execution_name}' execution...")
        request = self.get_resources().executions().get(name=full_execution_name)
        try:
            response = request.execute()
        except HttpError as http_error:
            if http_error.status_code == HTTPStatus.NOT_FOUND:
                logger.warning(f"Cloud Run job execution '{execution_name}' not found.")
                return None
            else:
                raise

        return CloudRunJobExecution.from_dict(response)  # noqa

    def _wait_for_execution_to_complete(self, job_name: str, execution_name: str) -> CloudRunJobExecution:
        """
        Wait for job execution to complete.
        :param job_name: name of the job
        :param execution_name: short name of the execution
        :returns: the completed job execution
        :raises: TimeoutError if the job doesn't complete within the timeout
        """
        total_wait_time = 0

        while total_wait_time < self._config.timeout_seconds:
            execution = self.get_execution(job_name, execution_name)

            if execution and execution.is_completed:
                logger.info(f"Job execution '{execution_name}' completed.")
                return execution

            logger.info(
                f"Job execution '{execution_name}' in progress. "
                f"Waiting {self._config.sleep_in_seconds} second(s)..."
            )

            time.sleep(self._config.sleep_in_seconds)
            total_wait_time += self._config.sleep_in_seconds

        raise TimeoutError(
            f"Job execution '{execution_name}' did not complete within {self._config.timeout_seconds} seconds!"
        )
