import logging
from dataclasses import dataclass
from functools import cached_property
from os import environ
from pathlib import Path
from typing import Optional

from dataclasses_json import DataClassJsonMixin

from common.consts import (
    DEFAULT_ENV,
    DEFAULT_GOOGLE_PROJECT_ID,
    GCP_REGION,
    PROJECT_NAME,
    AlertCriticality,
    AlertSeverity,
)

DEFAULT_LOG_LEVEL = logging.getLevelName(logging.INFO)
DEFAULT_RAW_BUCKET = f"{PROJECT_NAME}-staging-raw"
DEFAULT_TRANSFORMED_BUCKET = f"{PROJECT_NAME}-staging-transformed"


@dataclass(frozen=True)
class Config(DataClassJsonMixin):
    """
    Common configurations read from cloud environment or gitlab runner.
    """

    env: str = environ.get("ENV_NAME", DEFAULT_ENV)
    logger_name: str = environ.get("LOGGER_NAME", "analytics-pipelines")
    log_level: str = environ.get("LOG_LEVEL", DEFAULT_LOG_LEVEL)
    location: str = environ.get("GOOGLE_CLOUD_LOCATION", GCP_REGION)
    project_id: str = environ.get("GOOGLE_PROJECT_ID", DEFAULT_GOOGLE_PROJECT_ID)
    raw_bucket: str = environ.get("RAW_BUCKET", DEFAULT_RAW_BUCKET)
    transformed_bucket: str = environ.get("TRANSFORMED_BUCKET", DEFAULT_TRANSFORMED_BUCKET)

    @property
    def cloud_run_execution(self) -> Optional[str]:
        """Ref. https://cloud.google.com/run/docs/container-contract#jobs-env-vars"""
        return environ.get("CLOUD_RUN_EXECUTION")

    @property
    def is_local(self) -> bool:
        return self.env == DEFAULT_ENV

    @property
    def is_cloud(self) -> bool:
        return not self.is_local

    @property
    def is_staging(self) -> bool:
        return self.env.lower() == "staging"

    @property
    def is_test(self) -> bool:
        return self.is_local or self.is_staging

    @property
    def is_production(self) -> bool:
        return self.env.lower() == "production"

    @cached_property
    def root_dir(self) -> Path:
        if self.is_local:
            root_path = Path(
                next(iter(str(path.parent) for path in list(Path(__file__).resolve().parents) if path.stem == "src"))
            )
        else:
            root_path = Path(".")

        return root_path


DEFAULT_CONFIG = Config()


@dataclass(frozen=True, kw_only=True)
class MattermostAlertingConfig(Config):
    """
    Mattermost alerting channels configuration:
    - webhook_url: Mattermost webhook_url
    - info_channel: Informative messages like summary of daily refresh or analytics-workflow
    - warning_channel: Warnings from daily analytics refresh or Query Terminator
    - error_p1_channel: High priority errors that needs to be handled asap ->P1 CF Alerts
    - error_p2_channel: Low priority errors -> P2 CF Alerts
    """

    MATTERMOST_ALERTING_SECRET_ID = "mattermost-alerting-secret"

    webhook_url: str
    info_channel: Optional[str] = None
    warning_channel: Optional[str] = None
    error_p1_channel: Optional[str] = None
    error_p2_channel: Optional[str] = None

    def get_channel(
        self, severity: Optional[str | AlertSeverity] = None, criticality: Optional[str | AlertCriticality] = None
    ) -> Optional[str]:
        """
        Gets the right alerting channel depending on alert severity and criticality:
        - criticality other than P1 maps to P2 channel, provided severity is ERROR;
        - if neither severity nor criticality is specified, returns None = default channel of the webhook;
        - if no severity can be mapped to known AlertSeverity, returns None = default channel of the webhook;

        Note that `StrEnum` members can be compared with `str` without casting, like so:
        -------------------------------
        >>> AlertCriticality.P1 == 'p1'
        True

        :param severity: alert severity
        :param criticality: alert criticality
        :returns: the right alerting channel
        """
        if not severity:
            return None

        match severity.upper():
            case AlertSeverity.INFO:
                return self.info_channel
            case AlertSeverity.WARNING:
                return self.warning_channel
            case AlertSeverity.ERROR if criticality and criticality.lower() == AlertCriticality.P1:
                return self.error_p1_channel
            case AlertSeverity.ERROR:
                return self.error_p2_channel
            case _:
                return None
