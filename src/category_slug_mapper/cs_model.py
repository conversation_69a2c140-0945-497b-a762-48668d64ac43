from dataclasses import dataclass
from datetime import date

from pandas import BooleanDtype, Int32Dtype, StringDtype

from common.config import Config
from common.pandas_utils import DataFrameSchema


@dataclass(frozen=True)
class CategorySlugsConfig(Config):
    """
    Category slugs configuration:
    - root_folder: root folder in a GCS bucket where data is stored
    - load_date: date to be loaded (YYYY-MM-DD)
    """

    root_folder: str = "ranking"
    load_date: date = date.today()


class CategorySlugsColumns:
    CREATED_AT = "created_at"
    UPDATED_AT = "updated_at"
    CATEGORY_ID = "category_id"
    ID = "id"
    SLUG = "slug"
    SLUG_EN = "slug_en"
    LANGUAGE = "language"
    CATEGORY_SLUG = "category_slug"


CATEGORY_SLUGS_SCHEMA = DataFrameSchema(
    date_columns=[CategorySlugsColumns.CREATED_AT, CategorySlugsColumns.UPDATED_AT],
    data_types={
        CategorySlugsColumns.CATEGORY_ID: Int32Dtype(),
        CategorySlugsColumns.ID: Int32Dtype(),
        CategorySlugsColumns.SLUG: StringDtype(),
        CategorySlugsColumns.SLUG_EN: StringDtype(),
        # all other slugs as objects as number of those can vary
    },
)


CATEGORY_SLUGS_MAPPING_SCHEMA = DataFrameSchema(
    date_columns=[CategorySlugsColumns.CREATED_AT, CategorySlugsColumns.UPDATED_AT],
    data_types={
        CategorySlugsColumns.CATEGORY_ID: Int32Dtype(),
        CategorySlugsColumns.LANGUAGE: StringDtype(),
        CategorySlugsColumns.CATEGORY_SLUG: StringDtype(),
    },
)


class CountriesColumns:
    CODE = "code"
    NAME = "name"
    MAIN_LANGUAGE = "main_language"
    CURRENCY = "currency"
    IS_SITE = "is_site"


COUNTRIES_SCHEMA = DataFrameSchema(
    data_types={
        CountriesColumns.CODE: StringDtype(),
        CountriesColumns.NAME: StringDtype(),
        CountriesColumns.MAIN_LANGUAGE: StringDtype(),
        CountriesColumns.CURRENCY: StringDtype(),
        CountriesColumns.IS_SITE: BooleanDtype(),
    }
)
