from datetime import date
from functools import cached_property
from pathlib import Path

import pandas as pd
from revenue_sync_model import RevenueDataSchema

from common.pandas_utils import DataFrameWithSchema
from common.sql_repository import SqlRepository


class RevenueSyncPgRepository(SqlRepository):
    """
    Repository for accessing Platform Postgres DB data required by the Revenue Data Sync.
    """

    SQL_SCRIPT_PATH = Path("./revenue_query.sql")

    @cached_property
    def database_host(self) -> str:
        """
        Returns the host of the database.
        """
        return self.engine.url.host

    def select_revenue_data(self, report_date: date, time_zone: str) -> DataFrameWithSchema:
        """
        Select revenue data from Platform DB.
        :param report_date: date for which the report is generated
        :param time_zone: time zone to use for the report
        :return: DataFrameWithSchema containing revenue data.
        """
        result = self.select_from_query_file(
            Path(self.SQL_SCRIPT_PATH), report_date=report_date.isoformat(), time_zone=time_zone
        )

        return DataFrameWithSchema(schema=RevenueDataSchema.SCHEMA, dataframe=pd.DataFrame(result))
