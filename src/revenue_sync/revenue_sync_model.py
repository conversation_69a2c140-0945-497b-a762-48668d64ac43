from dataclasses import dataclass

from pandas import Float32Dtype, Int16Dtype, StringDtype

from common.config import Config
from common.pandas_utils import BaseDataFrameSchema, DataFrameSchema


@dataclass(frozen=True)
class RevenueSyncConfig(Config):
    """
    Configuration settings for the Revenue Sync ETL process.
    """

    time_zone: str = "Europe/Vienna"
    source_sql_instance_secret_id: str = "revenue-sync-db-secret"
    spreadsheet_id: str = "1JkOmHrHugtmkgZWur-N7IoODfmFf_ahTFXIk_z1-8n8"
    sheet_name: str = "hourly revenue data.csv"
    starting_cell: str = "A1"


class RevenueDataSchema(BaseDataFrameSchema):
    """
    Schema definition for Revenue Performance Steering Sync.
    """

    HOUR: str = "order_hour"
    COUNTRY: str = "order_country"
    REVENUE: str = "revenue_eur"
    DISCOUNT: str = "discount_eur"
    FINAL_REVENUE: str = "final_revenue_eur"

    SCHEMA = DataFrameSchema(
        data_types={
            HOUR: Int16Dtype(),
            COUNTRY: StringDtype(),
            REVENUE: Float32Dtype(),
            DISCOUNT: Float32Dtype(),
            FINAL_REVENUE: Float32Dtype(),
        },
    )
