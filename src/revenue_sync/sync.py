from datetime import date
from os import environ
from typing import Optional

from pandas import DataFrame
from revenue_sync_model import RevenueSyncConfig
from revenue_sync_pg_repository import RevenueSyncPgRepository
from tenacity import retry

from common.google_sheet_client import GoogleSheetsClient
from common.logger import get_logger
from common.pandas_utils import DataFrameWithSchema
from common.retry_config import RETRY_CONFIG
from common.time_service import TimeService

logger = get_logger()


class RevenueSync:

    def __init__(
        self,
        config: RevenueSyncConfig,
        pg_repo: RevenueSyncPgRepository,
        google_sheets_client: GoogleSheetsClient,
        time_service: Optional[TimeService] = None,
    ):
        self._config = config
        self._pg_repo = pg_repo
        self._google_sheets_client = google_sheets_client
        self._time_service = time_service if time_service else TimeService()

    @property
    def report_date(self) -> date:
        """
        Report date based on the environment variable.
        Useful for manual reloading of the report from GCP Cloud Run console.
        If not provided, uses today.
        """
        report_date = environ.get("REPORT_DATE")

        return date.fromisoformat(report_date) if report_date else self._time_service.today

    def run(self) -> None:
        """
        Run the sync process.
        """
        extracted = self.extract()
        self.load(extracted)

    def extract(self) -> DataFrameWithSchema:
        """
        Extract revenue data from source database.
        :return: DataFrame containing revenue data.
        """
        logger.info(
            f"Extracting hourly revenue data "
            f"for '{self._time_service.today.isoformat()}' "
            f"and time zone '{self._config.time_zone}'..."
        )

        return self._pg_repo.select_revenue_data(self.report_date, self._config.time_zone)

    def load(self, extracted: DataFrameWithSchema) -> None:
        """
        Load the data into a Google Sheet.
        :param extracted: extracted data to be loaded into the Google Sheet.
        """
        extracted_df = extracted.dataframe_with_schema

        if extracted_df.empty:
            raise ValueError("No revenue data to load!")

        self._try_reload_google_sheet(extracted_df)

    @retry(reraise=True, wait=RETRY_CONFIG)
    def _try_reload_google_sheet(self, extracted_df: DataFrame) -> None:
        logger.info(f"Loading revenue data into '{self._google_sheets_client.spreadsheet_url}' Google Sheet...")
        self._google_sheets_client.reload_google_sheet(df=extracted_df)
