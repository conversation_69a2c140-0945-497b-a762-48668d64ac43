select
    revenue.order_hour,
    revenue.order_country,
    coalesce(round(revenue.revenue_eur, 2), 0) as revenue_eur,
    coalesce(round(revenue.discount_eur, 2), 0) as discount_eur,
    case
        when coalesce(round(revenue.revenue_eur, 2), 0) - coalesce(round(revenue.discount_eur, 2), 0) < 0 then 0
        else coalesce(round(revenue.revenue_eur, 2), 0) - coalesce(round(revenue.discount_eur, 2), 0)
    end as final_revenue_eur
from
    (
        select
            extract(hour from (orders.paid_at)::timestamptz at time zone '{time_zone}') as order_hour,
            orders.country as order_country,
            coalesce(sum((case
                when (
                    merchants.is_addon_support
                )
                    then (case
                        when order_items.net
                            then (
                                order_items.price
                                * (1 / exchange_rates.rate)
                                / order_items.presentment_exchange_rate_modifier
                            )
                        else
                            (
                                order_items.price
                                * (1 / exchange_rates.rate)
                                / order_items.presentment_exchange_rate_modifier
                            )
                            / (1 + (order_items.vat / 100))
                    end) + (case
                        when order_items.net
                            then (
                                order_items.shipping_costs * (1 / exchange_rates.rate)
                                / order_items.presentment_exchange_rate_modifier
                            )
                        else (
                            order_items.shipping_costs * (1 / exchange_rates.rate)
                            / order_items.presentment_exchange_rate_modifier
                        )
                        / (1 + (order_items.vat / 100))
                    end)
                when (order_items.type = 'offer')
                    then
                        (
                            order_items.presentment_base_commission * (1 / exchange_rates.rate)
                            / order_items.presentment_exchange_rate_modifier
                        )
                        + (
                            order_items.presentment_target_price_commission * (1 / exchange_rates.rate)
                            / order_items.presentment_exchange_rate_modifier
                        )
                        + (
                            order_items.presentment_payout_commission * (1 / exchange_rates.rate)
                            / order_items.presentment_exchange_rate_modifier
                        )
                        + (
                            order_items.presentment_payment_commission * (1 / exchange_rates.rate)
                            / order_items.presentment_exchange_rate_modifier
                        )
                when (
                    order_items.type = 'addon'
                )
                    then (case
                        when order_items.net
                            then (
                                order_items.price
                                * (1 / exchange_rates.rate)
                                / order_items.presentment_exchange_rate_modifier
                            )
                        else (
                            order_items.price
                            * (1 / exchange_rates.rate)
                            / order_items.presentment_exchange_rate_modifier
                        )
                        / (1 + (order_items.vat / 100))
                    end) + (case
                        when order_items.net
                            then (
                                order_items.shipping_costs * (1 / exchange_rates.rate)
                                / order_items.presentment_exchange_rate_modifier
                            )
                        else (
                            order_items.shipping_costs * (1 / exchange_rates.rate)
                            / order_items.presentment_exchange_rate_modifier
                        )
                        / (1 + (order_items.vat / 100))
                    end) - (case
                        when (
                            order_items.addon_details ->> 'type'
                        ) = 'extended-warranty'
                            then (
                                coalesce(((order_items.addon_details ->> 'cost'))::numeric, 0)
                                * (1 / exchange_rates.rate)
                                / order_items.presentment_exchange_rate_modifier
                            )
                        else 0
                    end)
                when (order_items.type = 'service-fee') then (case
                    when order_items.net
                        then (
                            order_items.price
                            * (1 / exchange_rates.rate)
                            / order_items.presentment_exchange_rate_modifier
                        )
                    else (
                        order_items.price
                        * (1 / exchange_rates.rate)
                        / order_items.presentment_exchange_rate_modifier
                    )
                    / (1 + (order_items.vat / 100))
                end)
            end)), 0) as revenue_eur,
            coalesce(sum((
                order_items.discount * (1 / exchange_rates.rate)
                / order_items.presentment_exchange_rate_modifier
            )),
            0) as discount_eur
        from
            public.order_items as order_items
        inner join public.orders as orders
        on order_items.order_id = orders.id
        left join public.offers as offers
        on order_items.offer_id = offers.id and order_items.offer_valid_from = offers.valid_from
        left join public.merchants as merchants
        on offers.merchant_id = (merchants.id)
        inner join public.exchange_rates as exchange_rates
        on orders.presentment_currency = exchange_rates.target
            and exchange_rates.base = 'EUR'
            and orders.created_at < exchange_rates.valid_to
            and orders.created_at >= exchange_rates.valid_from
        where
            orders.country in ('at', 'de', 'it', 'ie', 'dk', 'se', 'nl', 'be', 'fi', 'cz', 'pt', 'ch')
            and orders.state in ('released', 'released.failed')
            and orders.paid_at
            >= (
                date_trunc(
                    'day', to_timestamp('{report_date}', 'YYYY-MM-DD') at time zone '{time_zone}'
                ) at time zone '{time_zone}'
            )
            and orders.paid_at
            < (
                date_trunc(
                    'day', to_timestamp('{report_date}', 'YYYY-MM-DD') at time zone '{time_zone}' + interval '1 day'
                ) at time zone '{time_zone}'
            )
        group by
            order_hour,
            order_country
        order by
            order_hour,
            order_country
    ) as revenue;
