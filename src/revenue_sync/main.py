from datetime import datetime

from google.cloud.sql.connector import Connector
from revenue_sync_model import RevenueSyncConfig
from revenue_sync_pg_repository import RevenueSyncPgRepository
from sync import RevenueSync

from common.config import DEFAULT_CONFIG
from common.google_sheet_client import GoogleSheetConfig, GoogleSheetsClient
from common.logger import setup_logger
from common.secret_client import get_secret
from common.sql_client import DatabaseConfig
from common.time_service import TimeService
from common.timing import timing

logger = setup_logger(DEFAULT_CONFIG)
config = (
    RevenueSyncConfig()
    if DEFAULT_CONFIG.is_production
    else RevenueSyncConfig(spreadsheet_id="11co8O5c9l8VchlA7_qT1jaYIUVgFQ-tKeFzqIJbmBS8", sheet_name="Sheet1")
)

pg_secret = get_secret(config.source_sql_instance_secret_id, config.project_id)
pg_config = DatabaseConfig() if config.is_local else DatabaseConfig.from_json(pg_secret)
gs_config = GoogleSheetConfig(
    spreadsheet_id=config.spreadsheet_id, sheet_name=config.sheet_name, starting_cell=config.starting_cell
)
google_sheets_client = GoogleSheetsClient(gs_config=gs_config)


@timing(logger, "RevenueSync")
def run(time_service: TimeService) -> None:
    """
    Processing entry point, called by scheduler.
    :param time_service: TimeService instance to be used for processing.
    """
    logger.info(f"Running '{RevenueSync.__name__}' on '{config.env}' with following config: {config}")

    with Connector() as connector:
        pg_repo = RevenueSyncPgRepository(db_config=pg_config, connector=connector)
        sync = RevenueSync(
            config=config, pg_repo=pg_repo, google_sheets_client=google_sheets_client, time_service=time_service
        )

        sync.run()


if __name__ == "__main__":
    time_serv = TimeService() if config.is_production else TimeService(datetime(2025, 1, 1))
    run(time_serv)
