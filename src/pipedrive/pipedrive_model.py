import os
from dataclasses import dataclass, field
from datetime import UTC, date, datetime, time
from os import environ
from typing import Any, Optional

import dataclasses_json
from dataclasses_json import DataClassJsonMixin
from numpy import object_
from pandas import BooleanDtype, Float32Dtype, StringDtype
from pandas.core.arrays.integer import Int32Dtype

from common.config import Config
from common.pandas_utils import BaseDataFrameSchema, DataFrameSchema
from common.pipeline_logging.pipeline_model import (
    SupportsPipelineExecution,
    SupportsPipelineRun,
)
from common.typings import DataLakePartition

API_SECRET_VARIABLE_NAME = "PIPEDRIVE_API_SECRET"
DEFAULT_API_SECRET_NAME = "pipedrive-api-secret"
PIPEDRIVE_DATA_LAKE_PARTITION = DataLakePartition(base_path="pipedrive")

dataclasses_json.cfg.global_config.encoders[date] = date.isoformat
dataclasses_json.cfg.global_config.decoders[date] = date.fromisoformat
dataclasses_json.cfg.global_config.encoders[datetime] = lambda dt: dt.astimezone(UTC).isoformat()
dataclasses_json.cfg.global_config.decoders[datetime] = lambda dt_str: datetime.fromisoformat(dt_str).replace(
    tzinfo=UTC
)
dataclasses_json.cfg.global_config.encoders[time] = time.isoformat
dataclasses_json.cfg.global_config.decoders[time] = lambda time_str: time.fromisoformat(time_str).replace(tzinfo=UTC)


@dataclass(frozen=True, kw_only=True)
class PipedriveConfig(Config, SupportsPipelineRun):
    api_secret: str = environ.get(API_SECRET_VARIABLE_NAME, DEFAULT_API_SECRET_NAME)

    @property
    def remove_local_files(self) -> bool:
        """Do not remove files where in local mode."""
        return self.is_local is False

    @property
    def pipeline_name(self) -> str:
        """SupportsPipelineRun implementation."""
        return os.environ.get(SupportsPipelineExecution.PIPELINE_NAME, "pipedrive-etl")

    @property
    def run_id(self) -> str:
        """SupportsPipelineRun implementation."""
        return os.environ.get(SupportsPipelineExecution.RUN_ID, "")


@dataclass(frozen=True, slots=True)
class PipedriveUser(DataClassJsonMixin):
    id: int
    name: str
    email: str


@dataclass(frozen=True, slots=True)
class PipedriveOrganization(DataClassJsonMixin):
    id: int
    company_id: int
    name: str
    open_deals_count: int
    related_open_deals_count: int
    closed_deals_count: int
    related_closed_deals_count: int
    email_messages_count: int
    people_count: int
    activities_count: int
    done_activities_count: int
    undone_activities_count: int
    files_count: int
    notes_count: int
    followers_count: int
    won_deals_count: int
    related_won_deals_count: int
    lost_deals_count: int
    related_lost_deals_count: int
    active_flag: bool
    update_time: datetime
    add_time: datetime
    owner_name: str
    cc_email: str
    delete_time: Optional[datetime] = None
    country_code: Optional[str] = None
    address: Optional[str] = None
    extra_fields: Optional[list[dict[str, str]]] = None


@dataclass(slots=True)
class PipedrivePerson(DataClassJsonMixin):
    id: int
    company_id: int
    org_ref_id: Optional[int] = field(init=False)
    name: str
    open_deals_count: int
    related_open_deals_count: int
    closed_deals_count: int
    related_closed_deals_count: int
    participant_open_deals_count: int
    participant_closed_deals_count: int
    email_messages_count: int
    activities_count: int
    done_activities_count: int
    undone_activities_count: int
    files_count: int
    notes_count: int
    followers_count: int
    won_deals_count: int
    related_won_deals_count: int
    lost_deals_count: int
    related_lost_deals_count: int
    active_flag: bool
    phone: list[dict[str, Any]]
    email: list[dict[str, Any]]
    update_time: datetime
    add_time: datetime
    cc_email: str
    org_id: Optional[dict[str, Any]] = None
    first_name: Optional[str] = None
    last_name: Optional[str] = None
    delete_time: Optional[datetime] = None
    next_activity_date: Optional[date] = None
    last_activity_date: Optional[date] = None
    postal_address: Optional[str] = None
    postal_address_lat: Optional[str] = None
    postal_address_long: Optional[str] = None
    postal_address_postal_code: Optional[str] = None
    birthday: Optional[str] = None
    job_title: Optional[str] = None
    org_name: Optional[str] = None
    primary_email: Optional[str] = None
    extra_fields: Optional[list[dict[str, str]]] = None

    def __post_init__(self) -> None:
        self.org_ref_id = self.org_id.get("value") if self.org_id else None


@dataclass(slots=True)
class PipedriveDeal(DataClassJsonMixin):
    id: int
    org_ref_id: Optional[int] = field(init=False)
    person_ref_id: Optional[int] = field(init=False)
    stage_id: int
    title: str
    value: float
    currency: str
    add_time: datetime
    update_time: datetime
    active: bool
    deleted: bool
    status: str
    pipeline_id: int
    products_count: int
    activities_count: int
    person_id: Optional[dict[str, Any]] = None
    org_id: Optional[dict[str, Any]] = None
    stage_change_time: Optional[datetime] = None
    won_time: Optional[datetime] = None
    first_won_time: Optional[datetime] = None
    lost_time: Optional[datetime] = None
    next_activity_date: Optional[date] = None
    next_activity_time: Optional[time] = None
    last_activity_date: Optional[date] = None
    expected_close_date: Optional[date] = None
    extra_fields: Optional[list[dict[str, str]]] = None

    def __post_init__(self) -> None:
        self.org_ref_id = self.org_id.get("value") if self.org_id else None
        self.person_ref_id = self.person_id.get("value") if self.person_id else None


@dataclass(frozen=True, slots=True)
class PipedriveField(DataClassJsonMixin):
    key: str
    name: str
    field_type: str
    id: Optional[int] = None
    add_time: Optional[datetime] = None
    update_time: Optional[datetime] = None
    created_by_user_id: Optional[int] = None
    last_updated_by_user_id: Optional[int] = None
    options: Optional[list[dict[str, Any]]] = None
    org_id: Optional[int] = None
    field_value: Optional[str] = None


@dataclass(frozen=True, slots=True)
class PipedriveExtracted(DataClassJsonMixin):
    users: list[PipedriveUser]
    organizations: list[PipedriveOrganization]
    organization_fields: list[PipedriveField]
    persons: list[PipedrivePerson]
    person_fields: list[PipedriveField]
    deals: list[PipedriveDeal]
    deal_fields: list[PipedriveField]


@dataclass(frozen=True, slots=True)
class PipedriveTransformed(DataClassJsonMixin):
    new_sales_manager: Optional[str]
    new_sales_manager_email: Optional[str]
    merchant_id: int
    merchant_name: Optional[str]
    primary_email: Optional[str]
    all_emails: Optional[list[dict[str, Any]]]
    account_manager: Optional[str]
    account_manager_email: Optional[str]
    account_name: Optional[str]
    first_name: Optional[str]
    last_name: Optional[str]
    contact_roles: Optional[list[str]]
    has_performance_role: Optional[bool]
    live_selling: Optional[datetime]
    date_entered: Optional[date]
    date_closed: Optional[date]
    probability_of_default: Optional[float]
    vat_number: Optional[str]
    credit_score_on_monitor: Optional[str]
    credit_score_when_live: Optional[str]


class PipedriveSchema(BaseDataFrameSchema):
    """
    Pipedrive transformed data schema definition
    """

    new_sales_manager: str = "new_sales_manager"
    new_sales_manager_email: str = "new_sales_manager_email"
    merchant_id: str = "merchant_id"
    merchant_name: str = "merchant_name"
    primary_email: str = "primary_email"
    all_emails: str = "all_emails"
    account_manager: str = "account_manager"
    account_manager_email: str = "account_manager_email"
    account_name: str = "account_name"
    first_name: str = "first_name"
    last_name: str = "last_name"
    contact_roles: str = "contact_roles"
    has_performance_role: str = "has_performance_role"
    live_selling: str = "live_selling"
    date_entered: str = "date_entered"
    date_closed: str = "date_closed"
    probability_of_default: str = "probability_of_default"
    vat_number: str = "vat_number"
    credit_score_on_monitor: str = "credit_score_on_monitor"
    credit_score_when_live: str = "credit_score_when_live"

    SCHEMA = DataFrameSchema(
        date_columns=[live_selling, date_entered, date_closed],
        data_types={
            new_sales_manager: StringDtype(),
            merchant_id: Int32Dtype(),
            merchant_name: StringDtype(),
            primary_email: StringDtype(),
            all_emails: object_(),
            account_manager: StringDtype(),
            account_manager_email: StringDtype(),
            account_name: StringDtype(),
            first_name: StringDtype(),
            last_name: StringDtype(),
            contact_roles: object_(),
            has_performance_role: BooleanDtype(),
            probability_of_default: Float32Dtype(),
            vat_number: StringDtype(),
            credit_score_on_monitor: StringDtype(),
            credit_score_when_live: StringDtype(),
        },
    )


@dataclass(frozen=True, slots=True)
class KeyVal(DataClassJsonMixin):
    field_key: str
    field_value: str


@dataclass(frozen=True, slots=True)
class PersonExtraField(KeyVal):
    org_id: int
    person_id: Optional[int]


@dataclass(frozen=True, slots=True)
class OrganizationExtraField(KeyVal):
    org_id: int
    options: Optional[list[dict[str, Any]]] = None


@dataclass(frozen=True, slots=True)
class OrganizationRiskField(DataClassJsonMixin):
    org_id: int
    probability_of_default: Optional[float]
    vat_number: Optional[str]
    credit_score_on_monitor: Optional[str]
    credit_score_when_live: Optional[str]


@dataclass(frozen=True, slots=True)
class DealExtraField(KeyVal):
    deal_id: int


@dataclass(frozen=True, slots=True)
class Manager(DataClassJsonMixin):
    org_id: int
    account_manager: Optional[str]
    new_sales_manager: Optional[str]


@dataclass(frozen=True, slots=True)
class Merchant(DataClassJsonMixin):
    org_id: int
    merchant_id: int
    merchant_name: str


@dataclass(frozen=True, slots=True)
class EnteredDate(DataClassJsonMixin):
    org_id: int
    person_id: Optional[int]
    date_entered: date


@dataclass(frozen=True, slots=True)
class ClosedDate(DataClassJsonMixin):
    org_id: int
    date_closed: date


@dataclass(frozen=True, slots=True)
class LiveSelling(DataClassJsonMixin):
    org_id: int
    person_id: Optional[int]
    live_selling: datetime


@dataclass(frozen=True, slots=True)
class PersonRole(DataClassJsonMixin):
    person_id: int
    contact_roles: list[str]
    has_performance_role: bool
