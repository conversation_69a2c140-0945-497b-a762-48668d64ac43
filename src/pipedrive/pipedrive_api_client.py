"""Pipedrive Api Client"""

import dataclasses
import json
import re
from dataclasses import dataclass
from typing import Any, Optional

import requests
from dataclasses_json import DataClassJsonMixin
from pipedrive_model import (
    PipedriveDeal,
    PipedriveField,
    PipedriveOrganization,
    <PERSON>ped<PERSON><PERSON>erson,
    PipedriveUser,
)

from common.logger import get_logger

logger = get_logger()


@dataclass(frozen=True)
class PipedriveApiConfig:
    api_token: str
    api_limit: int = 1000


@dataclass(frozen=True)
class PipedriveApiClientPagination(DataClassJsonMixin):
    more_items_in_collection: bool = True
    next_start: int = 0


class PipedriveApiClientError(ValueError):
    """Pipedrive Api client error"""


class PipedriveApiClient:
    """
    Pipedrive Api Client
    Ref. https://developers.pipedrive.com/docs/api/v1
    """

    BASE_URL: str = "https://refurbed.pipedrive.com/api/v1"
    URL_SCHEMA: str = "{0}/?api_token={1}&start={2}&limit={3}"
    URL_TEMPLATE: str = f"{BASE_URL}/{URL_SCHEMA}"
    UUID_REGEX = r"^[0-9a-z]{40}$"

    def __init__(self, config: PipedriveApiConfig) -> None:
        self._config = config

    def get_users(self) -> list[PipedriveUser]:
        """
        Calls Pipedrive API `users` endpoint.
        This endpoint is not paginable.

        Users are people with access to your Pipedrive account.
        Ref. https://developers.pipedrive.com/docs/api/v1/Users#getUsers
        :returns: list of users
        """

        return self.call_endpoint(endpoint_name="users", entity=PipedriveUser, paginated_call=False)

    def get_organisations(self) -> list[PipedriveOrganization]:
        """
        Calls Pipedrive API `organizations` endpoint.

        Organizations are companies and other kinds of organizations you are making deals with.
        Persons can be associated with organizations so that each organization can contain one or more persons.
        Ref. https://developers.pipedrive.com/docs/api/v1/Organizations#getOrganizations
        :returns: list of downloaded organizations
        """

        return self.call_endpoint(endpoint_name="organizations", entity=PipedriveOrganization)

    def get_organisation_fields(self) -> list[PipedriveField]:
        """
        Calls Pipedrive API `organizationsFields` endpoint.

        Organization fields represent the near-complete schema for an organization
        in the context of the company of the authorized user.
        Each company can have a different schema for their organizations, with various custom fields.

        Ref. https://developers.pipedrive.com/docs/api/v1/OrganizationFields#getOrganizationFields
        :returns: list of organizations fields
        """

        return self.call_endpoint(endpoint_name="organizationFields", entity=PipedriveField, get_extra_fields=False)

    def get_persons(self) -> list[PipedrivePerson]:
        """
        Calls Pipedrive API `persons` endpoint.

        Persons are your contacts, the customers you are doing deals with.
        Each person can belong to an organization. Persons should not be confused with users.

        Ref. https://developers.pipedrive.com/docs/api/v1/Persons#getPersons
        :returns: list of persons fields
        """
        return self.call_endpoint(endpoint_name="persons", entity=PipedrivePerson)

    def get_person_fields(self) -> list[PipedriveField]:
        """
        Calls Pipedrive API `personsFields` endpoint.

        Person fields represent the near-complete schema for a person
        in the context of the company of the authorized user.
        Each company can have a different schema for their persons, with various custom fields.

        Ref. https://developers.pipedrive.com/docs/api/v1/PersonFields#getPersonFields
        :returns: list of persons fields
        """
        return self.call_endpoint(endpoint_name="personFields", entity=PipedriveField, get_extra_fields=False)

    def get_deals(self) -> list[PipedriveDeal]:
        """
        Calls Pipedrive API `deals` endpoint.

        Deals represent ongoing, lost or won sales to an organization or to a person.
        Each deal has a monetary value and must be placed in a stage.
        Deals can be owned by a user, and followed by one or many users.
        Each deal consists of standard data fields but can also contain a number of custom fields.
        The custom fields can be recognized by long hashes as keys.
        These hashes can be mapped against DealField.key.
        The corresponding label for each such custom field can be obtained from DealField.name

        Ref. https://developers.pipedrive.com/docs/api/v1/Deals#getDeals
        :returns: list of deals fields
        """
        return self.call_endpoint(endpoint_name="deals", entity=PipedriveDeal)

    def get_deal_fields(self) -> list[PipedriveField]:
        """
        Calls Pipedrive API `dealFields` endpoint.

        Deal fields represent the near-complete schema for a deal in the context of the company of the authorized user.
        Each company can have a different schema for their deals, with various custom fields.

        Ref. https://developers.pipedrive.com/docs/api/v1/PersonFields#getPersonFields
        :returns: list of persons fields
        """
        return self.call_endpoint(endpoint_name="dealFields", entity=PipedriveField, get_extra_fields=False)

    def call_endpoint[
        T: DataClassJsonMixin
    ](self, endpoint_name: str, entity: type[T], get_extra_fields: bool = True, paginated_call: bool = True) -> list[T]:
        """
        Calls Pipedrive API endpoint.
        Combines all dynamic UUID-based fields into one collection `extra_fields` if `get_extra_fields` set to True.

        :param endpoint_name: name of PipeDrive Api endpoint
        :param entity: dataclass type that represents the structure of a converted raw response
        :param get_extra_fields: whether to combine extra fields
        :param paginated_call: whether to retrieve more data via pagination
        :returns: list of downloaded entities
        """

        pagination = PipedriveApiClientPagination()

        all_items = []

        logger.info(f"Calling '{self.BASE_URL}/{endpoint_name}' endpoint...")
        while pagination.more_items_in_collection:
            url = self.URL_TEMPLATE.format(
                endpoint_name, self._config.api_token, pagination.next_start, self._config.api_limit
            )

            response: dict[str, Any] = json.loads(requests.get(url=url).text)
            items = []
            items_raw: Optional[list[dict[str, Any]]] = response.get("data")
            if not items_raw:
                raise PipedriveApiClientError(f"No data found for '{endpoint_name}' endpoint!")

            for item_raw in items_raw:
                if get_extra_fields:
                    extra_fields = {
                        "extra_fields": [
                            {"field_key": k, "field_value": v}
                            for k, v in item_raw.items()
                            if v and re.search(self.UUID_REGEX, k)
                        ]
                    }
                    item_raw = {**item_raw | extra_fields}

                items.append(entity.from_dict(item_raw))
            all_items.extend(items)

            metadata: Optional[dict[str, Any]] = response.get("additional_data", {}).get("pagination")

            if not paginated_call:
                pagination = dataclasses.replace(pagination, more_items_in_collection=False)
            elif not metadata:
                raise PipedriveApiClientError(
                    f"No 'additional_data.pagination' data found for '{endpoint_name}' endpoint!"
                )
            else:
                pagination = PipedriveApiClientPagination.from_dict(metadata)

        return all_items
