from pathlib import Path
from tempfile import TemporaryDirectory

import functions_framework
from cloudevents.abstract import CloudEvent
from dhl_repository import DhlRepository
from etl import Dhl<PERSON><PERSON><PERSON>Etl
from google.cloud.sql.connector import Connector

from common.cloud_events import create_http_event, get_event_message
from common.config import Config
from common.consts import ANALYTICS_CLOUD_SQL_SECRET_ID
from common.data_lake_repository import DataLakeRepository
from common.logger import setup_logger
from common.pipeline_logging.pipeline_model import PipelineRun
from common.secret_client import get_secret
from common.sftp_client import SftpClient
from common.sql_client import DatabaseConfig
from common.timing import timing
from common.typings import HttpResponse

config = Config()
logger = setup_logger(config)

db_secret = get_secret(ANALYTICS_CLOUD_SQL_SECRET_ID, config.project_id)
raw_data_repository = DataLakeRepository(config.raw_bucket)
transformed_data_repository = DataLakeRepository(config.transformed_bucket)


@timing(logger, "dhl-shipping-etl")
@functions_framework.http
def run(request: CloudEvent) -> HttpResponse:
    """
    Http function entry point, called from analytics-workflow.
    """
    message = get_event_message(request)
    logger.info(f"Running DHL Shipping ETL with {message=}...")

    pipeline_run = PipelineRun.from_json(message)
    total_inserted_rows = 0
    with Connector() as connector:
        # A single connector and repository should be able to serve all country providers
        repository = DhlRepository(db_config=db_secret, connector=connector)
        configs = repository.select_shipping_config()

        for shipping_config in configs:
            logger.info(f"Running ETL for {shipping_config.provider_name}...")
            logger.debug(shipping_config.to_dict())

            sftp_secret = get_secret(shipping_config.sftp_secret_id, shipping_config.project_id)
            with SftpClient(sftp_secret) as sftp_client, TemporaryDirectory() as local_path:
                etl = DhlShippingEtl(
                    config=shipping_config,
                    pipeline_run=pipeline_run,
                    local_path=Path(local_path),
                    sftp_client=sftp_client,
                    dhl_repository=repository,
                    raw_data_repository=raw_data_repository,
                    transformed_data_repository=transformed_data_repository,
                )
                inserted_rows = etl.run()

            logger.info(f"Finished ETL for {shipping_config.provider_name}: {inserted_rows=}.")
            total_inserted_rows += inserted_rows

        errors = etl.check_shipping_billing_errors()

    body = f"Finished DHL Shipping ETL: {total_inserted_rows=}, {errors=}."
    logger.info(body)
    return HttpResponse(body)


if __name__ == "__main__":
    # Overwrite secret with local docker db to test your SQL before releasing to staging
    db_secret = DatabaseConfig()
    test_pipeline = PipelineRun()
    run(create_http_event(test_pipeline.to_json()))
