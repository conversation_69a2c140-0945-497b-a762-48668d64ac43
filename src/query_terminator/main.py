from cloudevents.abstract import CloudEvent
from functions_framework import cloud_event
from google.cloud.sql.connector import Connector
from query_timeout_model import QueryTerminatorConfig
from query_timeout_repository import QueryTimeoutRepository

from common.cloud_events import create_http_event
from common.config import MattermostAlertingConfig
from common.logger import setup_logger
from common.mattermost_client import MattermostClient
from common.secret_client import get_secret
from common.sql_client import DatabaseConfig
from query_terminator import QueryTerminator

mm_secret = get_secret(
    secret_id=MattermostAlertingConfig.MATTERMOST_ALERTING_SECRET_ID, project_id=QueryTerminatorConfig.project_id
)
alerting_config = MattermostAlertingConfig.from_json(mm_secret)
config = QueryTerminatorConfig(mattermost_channel=alerting_config.warning_channel)
logger = setup_logger(config)

pg_secret = get_secret(config.analytics_db_secret, config.project_id)
pg_config = DatabaseConfig.from_json(pg_secret)
mm_client = MattermostClient(alerting_config.webhook_url)


@cloud_event
def run(_: CloudEvent) -> None:
    """
    Cloud function entry point. Function triggered by cloud scheduler.
    """

    logger.info("Starting Query Terminator...")
    with Connector() as connector:
        repository = QueryTimeoutRepository(db_config=pg_config, connector=connector)
        terminator = QueryTerminator(config=config, client=mm_client, repository=repository)
        terminator.run()
    logger.info("Finished Query Terminator!")


if __name__ == "__main__":
    # Overwrite db_config with local docker db to test your SQL before releasing to staging
    pg_config = DatabaseConfig()
    run(create_http_event("Running locally..."))
