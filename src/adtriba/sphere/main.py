import dataclasses
import tempfile
from pathlib import Path

import functions_framework
from adtriba_shared.adtriba_model import AdtribaAwsCredentials
from boto3 import Session
from botocore.config import Config
from cloudevents.abstract import CloudEvent
from sphere_bq_repo import AdtribaSphereBqRepository
from sphere_etl import AdtribaSphereEtl
from sphere_model import AdtribaSphereConfig

from common.cloud_events import create_http_event
from common.data_lake_repository import DataLakeRepository
from common.logger import setup_logger
from common.s3_client import S3Client
from common.secret_client import get_secret
from common.timing import timing
from common.typings import HttpResponse

config = AdtribaSphereConfig()
logger = setup_logger(config)

aws_secret = get_secret(config.adtriba_secret, config.project_id)
aws_creds = AdtribaAwsCredentials.from_json(aws_secret)
boto_session = Session(aws_access_key_id=aws_creds.access_key_id, aws_secret_access_key=aws_creds.secret_access_key)
boto_config = Config(max_pool_connections=config.boto_max_pool_connections)
s3_client = S3Client(bucket_name=config.source_bucket, session=boto_session, config=boto_config)
raw_data_lake_repo = DataLakeRepository(config.raw_bucket)
bq_repo = AdtribaSphereBqRepository()


@timing(logger)
@functions_framework.http
def run(_: CloudEvent) -> HttpResponse:
    """
    Http function entry point, called from analytics-workflow.
    """
    logger.info("Starting Adtriba Sphere Etl...")
    with tempfile.TemporaryDirectory(delete=config.remove_local_files) as local_path:
        etl = AdtribaSphereEtl(
            config=config,
            s3_client=s3_client,
            raw_data_lake_repository=raw_data_lake_repo,
            bq_repository=bq_repo,
            local_temp_path=Path(local_path),
        )
        etl.run()

    logger.info("Finished Adtriba Sphere Etl.")
    return HttpResponse()


if __name__ == "__main__":
    config = dataclasses.replace(config, remove_local_files=False)
    run(create_http_event("Running locally test docker 9..."))
