from adtriba_shared.adtriba_model import AdtribaAwsCredentials, AdtribaConfig
from boto3 import Session
from botocore.config import Config

from common.s3_client import S3Client
from common.secret_client import get_secret


def get_s3_client(config: AdtribaConfig, bucket_name: str) -> S3Client:
    """
    Create an S3 client using the provided configuration and bucket name.

    :param config: Configuration object containing AWS credentials and settings.
    :param bucket_name: The name of the S3 bucket to connect to.
    :returns: An instance of S3Client configured with the provided credentials and bucket.
    """
    aws_secret = get_secret(config.adtriba_secret, config.project_id)
    aws_creds = AdtribaAwsCredentials.from_json(aws_secret)
    boto_session = Session(aws_access_key_id=aws_creds.access_key_id, aws_secret_access_key=aws_creds.secret_access_key)
    boto_config = Config(max_pool_connections=config.boto_max_pool_connections)

    return S3Client(bucket_name=bucket_name, session=boto_session, config=boto_config)
