from dataclasses import dataclass
from datetime import UTC

from pandas import DatetimeTZDtype, Float32Dtype, StringDtype

from common.pandas_utils import DataFrameSchema


@dataclass(frozen=True)
class ListColumns:
    scrapped_at = "scrapped_at"
    instance_name = "instance_name"
    price = "price"
    refurbed_product_name = "refurbed_product_name"
    best_product_score = "best_product_score"
    merchant = "merchant"
    best_merchant_score = "best_merchant_score"
    refurbed_merchant_name = "refurbed_merchant_name"
    country = "country"
    merchant_url = "merchant_url"
    merchant_name = "merchant_name"
    cluster = "cluster"
    grade = "grade"
    href = "href"


@dataclass(frozen=True)
class BMProductColumns:
    product_name: str = "product_name"
    product_id: str = "product_id"


@dataclass(frozen=True)
class BMMerchantColumns:
    ref_merchant_name: str = "ref_merchant_name"
    merchant_id: str = "merchant_id"


LISTING_OUTPUT_COLUMNS = [
    ListColumns.scrapped_at,
    BMProductColumns.product_name,
    ListColumns.instance_name,
    ListColumns.price,
    ListColumns.refurbed_product_name,
    BMProductColumns.product_id,
    ListColumns.best_product_score,
    ListColumns.best_merchant_score,
    ListColumns.refurbed_merchant_name,
    BMMerchantColumns.merchant_id,
    ListColumns.country,
    ListColumns.merchant_url,
    ListColumns.merchant_name,
]


class AlgoliaProductColumns:
    product_name: str = "product_name"
    instance_features: str = "instance_features"
    price: str = "price"
    grade: str = "grade"
    url: str = "url"
    country: str = "country"
    scrapped_at: str = "scrapped_at"
    extra_features: str = "extra_features"
    title: str = "title"
    id1: str = "id1"
    id2: str = "id2"
    currency: str = "currency"


ALGOLIA_PRODUCTS_SCHEMA = DataFrameSchema(
    data_types={
        AlgoliaProductColumns.product_name: StringDtype(),
        AlgoliaProductColumns.instance_features: StringDtype(),
        AlgoliaProductColumns.price: Float32Dtype(),
        AlgoliaProductColumns.grade: StringDtype(),
        AlgoliaProductColumns.url: StringDtype(),
        AlgoliaProductColumns.country: StringDtype(),
        AlgoliaProductColumns.scrapped_at: DatetimeTZDtype(unit="us", tz=UTC),
        AlgoliaProductColumns.extra_features: StringDtype(),
        AlgoliaProductColumns.title: StringDtype(),
        AlgoliaProductColumns.id1: StringDtype(),
        AlgoliaProductColumns.id2: StringDtype(),
        AlgoliaProductColumns.currency: StringDtype(),
    },
)
