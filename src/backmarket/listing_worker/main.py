from datetime import datetime, timezone
from logging import ERROR, INFO, getLogger

from bm_shared.api import AlgoliaClient
from bm_shared.bm_data_lake_repository import BMDataLakeRepository
from bm_shared.bm_model import ListingConfig, ListingMessage, Source
from bm_shared.bm_utils import emulate_pubsub_message, run_id_local_test
from bm_shared.listing_worker_model import NoProductsError
from bm_shared.oxy_http_client import OxyHTTPClient
from cloudevents.abstract import CloudEvent
from etl import ListingWorkerEtl, ListingWorkerInput
from functions_framework import cloud_event
from google.cloud.pubsub_v1 import PublisherClient

from common.cloud_events import create_http_event, get_event_message
from common.logger import setup_logger
from common.secret_client import get_secret

config = ListingConfig()
root_logger = setup_logger(config)
root_logger.setLevel(ERROR)  # silence info & debug from all lib functions
logger = getLogger(config.logger_name)
logger.setLevel(config.log_level)  # reset log level for this function
logger.info(f"Backmarket worker created with:{config=}")

oxy_client = OxyHTTPClient(get_secret(config.proxy_secret_id, config.project_id))
algolia_client = AlgoliaClient(oxy_client)
pubsub_client = PublisherClient()

raw_data_repository = BMDataLakeRepository(config.raw_bucket)
transformed_data_repository = BMDataLakeRepository(config.transformed_bucket)
worker_global_input = ListingWorkerInput(raw_data_repository)


@cloud_event
def run(event: CloudEvent) -> None:
    """Cloud function entry point. Function triggered by PubSub

    :param event: cloud event https://github.com/cloudevents/sdk-python/blob/master/cloudevents/http/event.py
    """
    message_encoded = str(get_event_message(event))
    try:
        message = ListingMessage.from_json(message_encoded)
        run_id = datetime.fromtimestamp(message.run_timestamp, tz=timezone.utc)
        worker_global_input.cached_data_update(config, run_id)
        etl = ListingWorkerEtl(
            config,
            run_id,
            oxy_client,
            algolia_client,
            pubsub_client,
            raw_data_repository,
            transformed_data_repository,
            worker_global_input,
            message,
        )
        etl.run()
        logger.info(f"Backmarket worker processing completed for: {message.url}, {run_id.isoformat()}")
    except NoProductsError:
        logger.warning(
            f"Backmarket worker no products in response for url: {message_encoded}",
        )
    except Exception:
        logger.exception(f"Backmarket worker processing failed for: {message_encoded}")


if __name__ == "__main__":
    logger.info("Worker running locally...")
    root_logger.setLevel(INFO)
    config = ListingConfig(max_child_urls=2)
    test_message = ListingMessage(
        "https://www.backmarket.at/de-at/s/adognicosto/32562a13-4a05-411b-b201-c319f2f51d42",
        Source.WORKER,
        run_id_local_test().timestamp(),
    )
    serialized = test_message.to_json()
    test_event = create_http_event(emulate_pubsub_message(serialized))
    run(test_event)
