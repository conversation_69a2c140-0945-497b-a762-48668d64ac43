#!/usr/bin/env python3
"""
Standalone test for PasswordMasker without Google Cloud dependencies
"""
import logging
import re
from typing import Any


class PasswordMasker:
    """Simplified PasswordMasker for testing"""

    passwords_keys = ("pwd", "pass", "password")
    password_mask = "*****"

    def filter(self, record: logging.LogRecord) -> bool:
        if isinstance(record.msg, str):
            record.msg = self.mask_password(record.msg)
        elif isinstance(record.msg, dict):
            self.mask_password_in_dict(record.msg)
        return True

    def mask_password(self, message: str) -> str:
        """Mask passwords in string messages"""
        for key in self.passwords_keys:
            # Pattern 1: password='value' or password="value" (with quotes, preserving quote style)
            message = re.sub(rf"(?i)({key}=)(['\"])([^'\"]*)\2", rf"\1\2{self.password_mask}\2", message)

            # Pattern 2: password=value (without quotes, stopping at word boundary, comma, space, or closing paren)
            message = re.sub(rf"(?i)({key}=)([^'\"\s,\)]+)(?=[\s,\)]|$)", rf"\1{self.password_mask}", message)

            # Pattern 3: password: 'value' or password: "value" (colon with quotes)
            message = re.sub(rf"(?i)({key}['][:][\s]*)(['\"])([^'\"]*)\2", rf"\1\2{self.password_mask}\2", message)

            # Pattern 4: JSON-like format {'password': 'value'} or {"password": "value"}
            message = re.sub(
                rf"(?i)(['\"].*{key}['\"][\s]*:[\s]*)(['\"])([^'\"]*)\2", rf"\1\2{self.password_mask}\2", message
            )

            # Pattern 5: JSON-like format with compound keys like 'rootPassword'
            message = re.sub(
                rf"(?i)(['\"][^'\"]*{key}[^'\"]*['\"][\s]*:[\s]*)(['\"])([^'\"]*)\2",
                rf"\1\2{self.password_mask}\2",
                message,
            )

            # Pattern 6: Command-line argument format '-password=value' or '--password=value'
            message = re.sub(rf"(?i)(-+{key}=)(['\"])([^'\"]*)\2", rf"\1\2{self.password_mask}\2", message)

            # Pattern 7: Quoted command-line argument format like '-password=value'
            message = re.sub(rf"(?i)(['\"])(-+{key}=)([^'\"]*)\1", rf"\1\2{self.password_mask}\1", message)

        return message

    def mask_password_in_dict(self, data: dict[str, Any]) -> None:
        """Mask passwords in dictionary objects"""
        for key, value in data.items():
            if isinstance(value, dict):
                self.mask_password_in_dict(value)
            elif isinstance(value, str):
                if any(pwd_key in key.lower() for pwd_key in self.passwords_keys):
                    data[key] = self.password_mask


def setup_simple_logger() -> logging.Logger:
    """Setup a simple console logger with password masking"""
    logger = logging.getLogger("test_logger")
    logger.setLevel(logging.DEBUG)

    # Remove existing handlers
    for handler in logger.handlers[:]:
        logger.removeHandler(handler)

    # Create console handler
    handler = logging.StreamHandler()
    handler.setLevel(logging.DEBUG)

    # Create formatter
    formatter = logging.Formatter("%(levelname)s - %(message)s")
    handler.setFormatter(formatter)

    # Add password masker filter
    password_masker = PasswordMasker()
    handler.addFilter(password_masker)

    logger.addHandler(handler)
    return logger


if __name__ == "__main__":
    print("Testing PasswordMasker with standalone logger...")
    print("=" * 80)

    logger = setup_simple_logger()

    # Test cases
    test_cases = [
        "This is INFO message with password=qwerty123",
        "This is INFO message with password='qwerty123'",
        "This is INFO message with password='@*&&#$%@#/'",
        "This is INFO message with serialized JSON: {'password': 'foo'}",
        "This is INFO message with serialized JSON: {'rootPassword': 'foo', 'bar': 'baz'}",
        "FlywayParams(host='localhost', password='postgres', url='*****************************************')",
        'Database config: {"password": "secret123", "host": "localhost"}',
        "Multiple passwords: password=first pwd='second' pass=\"third\"",
        "Flyway command: 'flyway info -infoOfState=Pending,Outdated repair migrate -configFiles=/Users/<USER>/repo/refurbed/analytics-pipelines/src/sql-pg/flyway.toml -locations=filesystem:/Users/<USER>/repo/refurbed/analytics-pipelines/src/sql-pg -environment=local -user=postgres '-password=postgres'",
    ]

    for i, message in enumerate(test_cases, 1):
        print(f"\nTest {i}:")
        logger.info(message)

    print("\n" + "=" * 80)
    print("Testing completed!")
