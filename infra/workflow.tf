locals {
  daily_at_00_01_utc  = "1 0 * * *"
  hourly_at_minute_3  = "3 * * * *"
  cloud_functions_url = "https://${var.gcp_region}-${var.gcp_project_id}.cloudfunctions.net/"
}

module "analytics_workflow" {
  source = "git::https://gitlab.com/refurbed/analytics-and-ds/infrastructure-modules//src/workflow?ref=main"

  # Workflow source, name and env variables
  # https://cloud.google.com/blog/topics/developers-practitioners/deploying-multi-yaml-workflows-definitions-terraform
  source_file_paths     = ["workflows/analytics.yaml"]
  workflow_name         = "analytics-workflow"
  workflow_description  = "Analytics workflow for running daily ETLs"
  service_account_email = module.analytics_workflow_sa.sa_email

  # Variables with references to used resources to enforce explicit dependency
  # Note there is a limit of 20 environment variables!
  env_vars = {
    analytics_db_refresh_job = module.analytics_db_refresh_job.job_full_name

    # functions
    proc_executor_url          = module.proc_executor_function.function_url
    workflow_finalizer_url     = module.workflow_finalizer_function.function_url
    dhl_shipping_url           = module.dhl_shipping_function.function_url
    nrm_function_url           = module.nrm_function.function_url
    adtriba_sphere_url         = module.adtriba_sphere_function.function_url
    adtriba_tcm_url            = module.adtriba_tcm_function.function_url
    zendesk_auto_responder_url = module.zendesk_auto_responder_function.function_url
    category_slugs_mapper_url  = module.category_slug_mapper_function.function_url

    # jobs
    pipedrive_job_name = module.pipedrive_job.job_full_name

    # other workflows
    unmanaged_workflow_landing_page_url = module.unmanaged_workflow.workflow_landing_page_url
    unmanaged_workflow_http_post_url    = module.unmanaged_workflow.workflow_http_post_url

    platform_export_landing_page_url = module.platform_export_workflow.workflow_landing_page_url
    platform_export_http_post_url    = module.platform_export_workflow.workflow_http_post_url
  }

  # Cloud scheduler properties: set schedule_cron to `null` to disable scheduler
  schedule_cron  = local.is_production ? local.daily_at_00_01_utc : null
  schedule_input = "{ \\\"run_analytics_db_refresh\\\": true, \\\"run_platform_export\\\": true, \\\"run_unmanaged_workflow\\\": true }"

  labels = local.basic_labels
}

module "unmanaged_workflow" {
  source = "git::https://gitlab.com/refurbed/analytics-and-ds/infrastructure-modules//src/workflow?ref=main"

  # Workflow source, name and env variables
  # https://cloud.google.com/blog/topics/developers-practitioners/deploying-multi-yaml-workflows-definitions-terraform
  source_file_paths     = ["workflows/unmanaged.yaml"]
  workflow_name         = "unmanaged-workflow"
  workflow_description  = "Unmanaged workflow for pipelines created outside monorepo"
  service_account_email = module.unmanaged_workflow_sa.sa_email

  # Variables with references to used resources to enforce explicit dependency
  env_vars = {
    # Current Time
    get_current_time_url = module.workflow_current_time_function.function_url

    # URL for Unmanaged pipelines (not yet in the monorepo)
    cloud_functions_url = local.cloud_functions_url

    # Managed pipelines
    workflow_finalizer_name  = module.workflow_finalizer_function.function_name
    workflow_finalizer_url   = module.workflow_finalizer_function.function_url
    srp_circuit_breakers_url = module.srp_circuit_breakers_function.function_url

    # Whether to dry run due to missing dependencies
    dry_run = local.is_staging
  }

  labels = local.basic_labels
}

module "bm_product_crawler_workflow" {
  source = "git::https://gitlab.com/refurbed/analytics-and-ds/infrastructure-modules//src/workflow?ref=main"
  # Workflow source,name and env variables
  # https://cloud.google.com/blog/topics/developers-practitioners/deploying-multi-yaml-workflows-definitions-terraform
  source_file_paths     = ["workflows/bm_product_crawler.yaml"]
  workflow_name         = "bm-product-crawler-workflow"
  workflow_description  = "Workflow for Backmarket product crawler"
  service_account_email = module.bm_product_crawler_workflow_sa.sa_email

  # Variables with references to used resources to enforce explicit dependency
  env_vars = {
    # functions
    bm_product_starter_name = module.bm_product_starter_function.function_name
    bm_product_starter_url  = module.bm_product_starter_function.function_url

    # jobs
    bm_api_worker_job_id   = module.bm_api_worker_job.job_name
    bm_api_worker_job_name = module.bm_api_worker_job.job_full_name

    bm_compaction_job_id   = module.bm_compaction_job.job_name
    bm_compaction_job_name = module.bm_compaction_job.job_full_name

    # other functions
    workflow_finalizer_name = module.workflow_finalizer_function.function_name
    workflow_finalizer_url  = module.workflow_finalizer_function.function_url

  }

  # Cloud scheduler properties: set schedule_cron to `null` to disable scheduler
  schedule_cron = local.is_production ? local.hourly_at_minute_3 : null

  labels = local.basic_labels
}

module "platform_export_workflow" {
  source = "git::https://gitlab.com/refurbed/analytics-and-ds/infrastructure-modules//src/workflow?ref=main"

  source_file_paths     = ["workflows/platform_export.yaml"]
  workflow_name         = "platform-export"
  workflow_description  = "Platform export workflow: Postgres 'export_etl' views to BigQuery tables"
  service_account_email = module.platform_etl_workflow_sa.sa_email

  # Variables with references to used resources to enforce explicit dependency
  env_vars = {
    # Cloud run functions
    workflow_starter_url           = module.workflow_starter_function.function_url
    workflow_finalizer_url         = module.workflow_finalizer_function.function_url
    platform_export_dispatcher_url = module.platform_export_dispatcher_function.function_url

    # Cloud run jobs
    platform_export_small_job_full_name  = module.platform_export_small_job.job_full_name
    platform_export_medium_job_full_name = module.platform_export_medium_job.job_full_name
    platform_export_large_job_full_name  = module.platform_export_large_job.job_full_name

    # Workflows
    ranker_landing_page_url = module.ranker_workflow.workflow_landing_page_url
    ranker_http_post_url    = module.ranker_workflow.workflow_http_post_url
  }

  labels = local.basic_labels
}

module "ranker_workflow" {
  source = "git::https://gitlab.com/refurbed/analytics-and-ds/infrastructure-modules//src/workflow?ref=main"

  source_file_paths     = ["workflows/ranker.yaml"]
  workflow_name         = "ranker-workflow"
  workflow_description  = "Ranker workflow: ELT in BigQuery to support DS ranker"
  service_account_email = module.ranker_workflow_sa.sa_email

  # Variables with references to used resources to enforce explicit dependency
  env_vars = {
    # Cloud run functions
    workflow_finalizer_url = module.workflow_finalizer_function.function_url
    workflow_dates_url     = module.workflow_dates_function.function_url
    ranker_url             = module.ranker_function.function_url
  }

  labels = local.basic_labels
}
