locals {
  db_service_accounts_names = toset([
    module.platform_export_sa.account_id,
    module.pipedrive_sa.account_id,
    module.adtriba_sa.account_id,
    module.nrm_sa.account_id,
    module.zendesk_auto_responder_sa.account_id,
    module.query_terminator_sa.account_id,
    module.proc_executor_sa.account_id,
    module.dhl_shipping_sa.account_id,
    module.category_slug_mapper_sa.account_id,
    module.srp_circuit_breakers_sa.account_id,
    module.workflow_function_sa.account_id
    # add your service account name here that needs DB access
  ])
}

module "analytics_db" {
  source        = "git::https://gitlab.com/refurbed/analytics-and-ds/infrastructure-modules//src/postgres_cloud_sql?ref=main"
  should_create = var.analytics_db_should_create

  instance_name    = "analytics"
  tier             = var.analytics_db_tier
  database_version = var.analytics_db_pg_version
  iam_groups       = ["<EMAIL>", "<EMAIL>"]

  service_accounts = local.db_service_accounts_names

  labels = local.basic_labels
}
