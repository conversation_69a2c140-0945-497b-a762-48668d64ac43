module "daily_every_8_hours_schedule" {
  source  = "git::https://gitlab.com/refurbed/analytics-and-ds/infrastructure-modules//src/cloud_scheduler?ref=main"
  enabled = var.daily_every_8_hours_schedule_enabled

  schedule_name        = "daily-every-8-hours"
  cron                 = "0 */8 * * *"
  function_subscribers = [module.google_analytics_sa.sa_email]

  labels = local.basic_labels
}

# At minute 0 and 30 past every hour from 6 through 23.
module "every_30_minutes_schedule" {
  source  = "git::https://gitlab.com/refurbed/analytics-and-ds/infrastructure-modules//src/cloud_scheduler?ref=main"
  enabled = var.every_30_minutes_schedule_enabled

  schedule_name        = "every-30-minutes"
  cron                 = "0,30 6-23 * * *"
  function_subscribers = [module.query_terminator_sa.sa_email]

  labels = local.basic_labels
}

# At 29 minute past every hour from 8 through 23.
module "at_minute_29_past_every_hour_schedule" {
  source = "git::https://gitlab.com/refurbed/analytics-and-ds/infrastructure-modules//src/cloud_scheduler?ref=main"

  schedule_name = "at-minute-29-past-every-hour"
  cron          = "29 8-23 * * *"
  time_zone     = "Europe/Vienna"
  job_subscriber = {
    job_name = module.revenue_sync_job.job_name,
    sa_email = module.revenue_sync_sa.sa_email
  }
  enabled = local.is_production

  labels = local.basic_labels
}
