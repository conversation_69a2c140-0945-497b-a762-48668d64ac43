module "demo_sftp" {
  source = "git::https://gitlab.com/refurbed/analytics-and-ds/infrastructure-modules//src/secret_manager?ref=main"

  secret_id = "demo-sftp-config"
  labels    = local.basic_labels
}

module "dhl_sftp" {
  source = "git::https://gitlab.com/refurbed/analytics-and-ds/infrastructure-modules//src/secret_manager?ref=main"

  secret_id  = "dhl-sftp-config"
  sa_readers = [module.dhl_shipping_sa.sa_email]
  labels     = local.basic_labels
}

module "analytics_cloud_sql" {
  source = "git::https://gitlab.com/refurbed/analytics-and-ds/infrastructure-modules//src/secret_manager?ref=main"

  secret_id  = "analytics-cloud-sql-config"
  sa_readers = [module.dhl_shipping_sa.sa_email, module.analytics_db_refresh_sa.sa_email]
  labels     = local.basic_labels
}

module "analytics_db_refresh_source_secret" {
  source = "git::https://gitlab.com/refurbed/analytics-and-ds/infrastructure-modules//src/secret_manager?ref=main"

  secret_id  = "analytics-db-refresh-source-secret"
  sa_readers = [module.analytics_db_refresh_sa.sa_email]
  labels     = local.basic_labels
}

module "analytics_db_refresh_target_secret" {
  source = "git::https://gitlab.com/refurbed/analytics-and-ds/infrastructure-modules//src/secret_manager?ref=main"

  secret_id  = "analytics-db-refresh-target-secret"
  sa_readers = [module.analytics_db_refresh_sa.sa_email, module.platform_export_sa.sa_email]
  labels     = local.basic_labels
}

module "analytics_db_refresh_source_pgpassword_secret" {
  source = "git::https://gitlab.com/refurbed/analytics-and-ds/infrastructure-modules//src/secret_manager?ref=main"

  secret_id    = "analytics-db-refresh-source-pgpassword-secret"
  secret_value = "to-be-updated-manually"
  sa_readers   = [module.analytics_db_refresh_sa.sa_email]
  labels       = local.basic_labels
}

module "analytics_db_cf_reader_secret" {
  source = "git::https://gitlab.com/refurbed/analytics-and-ds/infrastructure-modules//src/secret_manager?ref=main"

  secret_id = "analytics-db-cf-reader"
  sa_readers = [
    module.srp_circuit_breakers_sa.sa_email
  ]
  labels = local.basic_labels
}

module "analytics_db_cf_writer_secret" {
  source = "git::https://gitlab.com/refurbed/analytics-and-ds/infrastructure-modules//src/secret_manager?ref=main"

  secret_id = "analytics-db-cf-writer"
  sa_readers = [
    module.adtriba_sa.sa_email,
    module.dhl_shipping_sa.sa_email,
    module.nrm_sa.sa_email,
    module.pipedrive_sa.sa_email,
    module.platform_export_sa.sa_email,
    module.proc_executor_sa.sa_email,
    module.category_slug_mapper_sa.sa_email,
    module.workflow_function_sa.sa_email,
    module.analytics_db_refresh_sa.sa_email,
    module.zendesk_auto_responder_sa.sa_email
  ]
  labels = local.basic_labels
}

module "oxylab-proxy" {
  source = "git::https://gitlab.com/refurbed/analytics-and-ds/infrastructure-modules//src/secret_manager?ref=main"

  secret_id  = "OXYLAB"
  sa_readers = [module.bm_api_worker_sa.sa_email]
  labels     = local.basic_labels
}

module "platform_db_export_secret" {
  source = "git::https://gitlab.com/refurbed/analytics-and-ds/infrastructure-modules//src/secret_manager?ref=main"

  secret_id  = "platform-db-export"
  sa_readers = [module.platform_export_sa.sa_email]
  labels     = local.basic_labels
}

module "adtriba_aws_secret" {
  source = "git::https://gitlab.com/refurbed/analytics-and-ds/infrastructure-modules//src/secret_manager?ref=main"

  secret_id  = "adtriba-aws-secret"
  sa_readers = [module.adtriba_sa.sa_email]
  labels     = local.basic_labels
}

module "pipedrive_api_secret" {
  source = "git::https://gitlab.com/refurbed/analytics-and-ds/infrastructure-modules//src/secret_manager?ref=main"

  secret_id  = "pipedrive-api-secret"
  sa_readers = [module.pipedrive_sa.sa_email]
  labels     = local.basic_labels
}

module "bm_algolia_api_secret" {
  source = "git::https://gitlab.com/refurbed/analytics-and-ds/infrastructure-modules//src/secret_manager?ref=main"

  secret_id  = "backmarket-algolia-api"
  sa_readers = [module.bm_api_worker_sa.sa_email]
  labels     = local.basic_labels
}

module "mattermost_test_channel" {
  source = "git::https://gitlab.com/refurbed/analytics-and-ds/infrastructure-modules//src/secret_manager?ref=main"

  secret_id  = "mattermost-test-channel"
  sa_readers = [module.cicd_gitlab.sa_email]
  labels     = local.basic_labels
}

module "mattermost_alerting_secret" {
  source = "git::https://gitlab.com/refurbed/analytics-and-ds/infrastructure-modules//src/secret_manager?ref=main"

  secret_id = "mattermost-alerting-secret"
  sa_readers = [
    module.alerting_sa.sa_email,
    module.analytics_db_refresh_sa.sa_email,
    module.workflow_function_sa.sa_email,
    module.query_terminator_sa.sa_email
  ]
  labels = local.basic_labels
}

module "analytics_db_query_terminator_secret" {
  source = "git::https://gitlab.com/refurbed/analytics-and-ds/infrastructure-modules//src/secret_manager?ref=main"

  secret_id  = "analytics-db-query-terminator"
  sa_readers = [module.query_terminator_sa.sa_email]
  labels     = local.basic_labels
}

module "victoria_pushgateway_secret" {
  /*
  Secret format:
  {
   "url":  "",
   "username": "",
   "password": ""
  }
  */
  source = "git::https://gitlab.com/refurbed/analytics-and-ds/infrastructure-modules//src/secret_manager?ref=main"

  secret_id = "victoria-pushgateway"
  labels    = local.basic_labels
}

module "zendesk_secret" {
  /*
  {
   "email":  "",
   "subdomain": "",
   "token": ""
  }
  */
  source = "git::https://gitlab.com/refurbed/analytics-and-ds/infrastructure-modules//src/secret_manager?ref=main"

  secret_id  = "zendesk"
  sa_readers = [module.srp_circuit_breakers_sa.sa_email]
  labels     = local.basic_labels
}


module "revenue_sync_secret" {
  /*
  {
    "host": "db-host",
    "database": "database-name",
    "username": "user-name",
    "password": "user-password",
    "port": "5432"
  }
  */
  source = "git::https://gitlab.com/refurbed/analytics-and-ds/infrastructure-modules//src/secret_manager?ref=main"

  secret_id  = "revenue-sync-db-secret"
  sa_readers = [module.revenue_sync_sa.sa_email]
  labels     = local.basic_labels
}
