# $schema: https://json.schemastore.org/workflows.json
main:
  params: [input]
  steps:
    - init:
        # 0. Define procedure names that can be run in parallel
        # https://cloud.google.com/workflows/docs/reference/syntax/variables#assign-step
        assign:
          - result: {}
          - workflow_execution:
              workflow_id: ${sys.get_env("GOOGLE_CLOUD_WORKFLOW_ID")}
              execution_id: ${sys.get_env("GOOGLE_CLOUD_WORKFLOW_EXECUTION_ID")}
              start: ${sys.now()}
              end: ${sys.now()}
              bq_cost_report_date: ${text.substring(time.format(sys.now() - (60 * 60 * 24)), 0, 10)} # yesterday
              result: {}
              error: null
          - analytics_db_refresh_job_name: ${sys.get_env("analytics_db_refresh_job")}
          - should_run_analytics_db_refresh: ${default(map.get(input, "run_analytics_db_refresh"), false)}
          - is_cicd_run: ${default(map.get(input, "CICD"), false)}
          - platform_export_workflow:
              # Platform export does not run by default from GCP console!
              id: "platform-export-workflow"
              should_run: ${default(map.get(input, "run_platform_export"), false)}
              landing_page_url: ${sys.get_env("platform_export_landing_page_url")}
              http_post_url: ${sys.get_env("platform_export_http_post_url")}
          - unmanaged_workflow:
              # Unmanaged workflow does not run by default from GCP console!
              id: "unmanaged-workflow"
              should_run: ${default(map.get(input, "run_unmanaged_workflow"), false)}
              landing_page_url: ${sys.get_env("unmanaged_workflow_landing_page_url")}
              http_post_url: ${sys.get_env("unmanaged_workflow_http_post_url")}
          - parallel_functions_part_1:
              - function:
                  id: "proc-executor -> reload_full_order_item_refunds"
                  url: ${sys.get_env("proc_executor_url")}
                  body: {schema_name: "maintenance", procedure_name: "reload_full_order_item_refunds"}
              - function:
                  id: "proc-executor -> load_order_item_exchange_rate"
                  url: ${sys.get_env("proc_executor_url")}
                  body: {schema_name: "maintenance", procedure_name: "load_order_item_exchange_rate"}
              - function:
                  id: "proc-executor -> load_offer_properties"
                  url: ${sys.get_env("proc_executor_url")}
                  body: {schema_name: "maintenance", procedure_name: "load_offer_properties"}
              - function:
                  id: "dhl-etl-shipping"
                  url: ${sys.get_env("dhl_shipping_url")}
              - function:
                  id: "adtriba-etl-sphere"
                  url: ${sys.get_env("adtriba_sphere_url")}
              - function:
                  id: "adtriba-tcm-etl"
                  url: ${sys.get_env("adtriba_tcm_url")}
              - function:
                  id: "category-slugs-mapper"
                  url: ${sys.get_env("category_slugs_mapper_url")}
          - parallel_jobs:
              - job:
                  id: "pipedrive-etl-job"
                  name: ${sys.get_env("pipedrive_job_name")}
          - parallel_functions_part_2:
              - function:
                  # Dependency on function: "proc-executor -> load_order_item_exchange_rate"
                  id: "nrm-forecast-etl"
                  url: ${sys.get_env("nrm_function_url")}
              - function:
                  # Dependency on job: "pipedrive-etl-job"
                  id: "zendesk-auto-responder"
                  url: ${sys.get_env("zendesk_auto_responder_url")}
    - run_workflow:
        # Grouping step to catch ETL non retryable errors and alert to Mattermost channel
        try:
          steps:
            - run_analytics_db_refresh:
                # 1. Run analytics database refresh as a first step -> prerequisite for all other steps!
                switch:
                  - condition: ${should_run_analytics_db_refresh}
                    steps:
                      - run_analytics_db_refresh_job:
                          call: run_cloud_run_job
                          args:
                            job:
                              name: ${analytics_db_refresh_job_name}
                              env:
                                - name: "CICD"
                                  value: ${string(is_cicd_run)}
                          result: analytics_db_refresh_job_result
                      - map_analytics_db_refresh_job_result:
                          assign:
                            - result["analytics-db-refresh-job"]: ${analytics_db_refresh_job_result}
            - run_parallel_functions_part_1:
                # 2. Run parallel cloud run functions part_1
                call: run_parallel_functions
                args:
                  parallel_functions: ${parallel_functions_part_1}
                result: parallel_functions_result_part_1
            - assign_parallel_functions_result_part_1:
                assign:
                  - result["run-parallel-functions-part-1"]: ${parallel_functions_result_part_1}
            - run_parallel_jobs:
                # 3. Run parallel cloud run jobs
                call: run_parallel_jobs
                args:
                  parallel_jobs: ${parallel_jobs}
                result: parallel_jobs_result
            - assign_parallel_jobs_result:
                assign:
                  - result["run-parallel-jobs"]: ${parallel_jobs_result}
            - run_parallel_functions_part_2:
                # 4. Run parallel cloud run functions part_2
                call: run_parallel_functions
                args:
                  parallel_functions: ${parallel_functions_part_2}
                result: parallel_functions_result_part_2
            - assign_parallel_functions_result_part_2:
                assign:
                  - result["run-parallel-functions-part-2"]: ${parallel_functions_result_part_2}
            - run_platform_export:
                steps:
                  - run_platform_export_async:
                      call: run_workflow_async
                      args:
                        workflow: ${platform_export_workflow}
                      result: run_workflow_async_result
                  - assign_platform_export_result:
                      assign:
                        - result[platform_export_workflow.id]: ${run_workflow_async_result}
            - run_unmanaged_workflow:
                steps:
                  - run_unmanaged_workflow_async:
                      call: run_workflow_async
                      args:
                        workflow: ${unmanaged_workflow}
                      result: run_workflow_async_result
                  - assign_unmanaged_workflow_result:
                      assign:
                        - result[unmanaged_workflow.id]: ${run_workflow_async_result}
        except:
          as: e
          steps:
            - assign_workflow_error:
                assign:
                  - workflow_execution.result: ${result}
                  - workflow_execution.error: ${e}
                  - workflow_execution.end: ${sys.now()}
            - send_error_notification:
                call: run_http_function
                args:
                  function:
                    id: "workflow-finalizer"
                    url: ${sys.get_env("workflow_finalizer_url")}
                    body: ${workflow_execution}
                result: send_error_notification_result
            - raiseError:
                raise: ${workflow_execution}
    - assign_workflow_result:
        assign:
          - workflow_execution.result: ${result}
          - workflow_execution.end: ${sys.now()}
    - send_success_notification:
        call: run_http_function
        args:
          function:
            id: "workflow-finalizer"
            url: ${sys.get_env("workflow_finalizer_url")}
            body: ${workflow_execution}
        result: send_success_notification_result
    - done:
        return: ${result}
