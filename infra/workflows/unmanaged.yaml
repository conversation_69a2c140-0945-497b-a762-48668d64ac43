# $schema: https://json.schemastore.org/workflows.json
main:
  params: [input]
  steps:
    - init:
        assign:
          - result: {}
          - workflow_execution:
              workflow_id: ${sys.get_env("GOOGLE_CLOUD_WORKFLOW_ID")}
              execution_id: ${sys.get_env("GOOGLE_CLOUD_WORKFLOW_EXECUTION_ID")}
              start: ${sys.now()}
              end: ${sys.now()}
              result: {}
              error: null
          - parallel_functions:
              - function:
                  id: "db_schema_monitoring"
                  url: ${sys.get_env("cloud_functions_url") + "db_schema_monitoring"}
                  dry_run: ${sys.get_env("dry_run")}
          - srp_sequential_functions:
              - function:
                  id: "order_items_pdd_bd"
                  url: ${sys.get_env("cloud_functions_url") + "order_items_pdd_bd"}
                  dry_run: ${sys.get_env("dry_run")}
                  retries_num: 0
              - function:
                  id: "create_rti_and_zendesk_tables"
                  url: ${sys.get_env("cloud_functions_url") + "create_rti_and_zendesk_tables"}
                  dry_run: ${sys.get_env("dry_run")}
                  retries_num: 0
              - function:
                  id: "order_items_pdd_update"
                  url: ${sys.get_env("cloud_functions_url") + "order_items_pdd_update"}
                  dry_run: ${sys.get_env("dry_run")}
                  retries_num: 0
              - function:
                  id: "mps_per_country"
                  url: ${sys.get_env("cloud_functions_url") + "mps_per_country"}
                  dry_run: ${sys.get_env("dry_run")}
                  retries_num: 0
              - function:
                  id: "send_alert_to_merchant_interface"
                  url: ${sys.get_env("cloud_functions_url") + "send_alert_to_merchant_interface"}
                  dry_run: ${sys.get_env("dry_run")}
                  retries_num: 0
              - function:
                  id: "supplier_performance_handicap_exports"
                  url: ${sys.get_env("cloud_functions_url") + "supplier_performance_handicap_exports"}
                  dry_run: ${sys.get_env("dry_run")}
                  retries_num: 0
          - srp_circuit_breakers_tuesday_function:
              - function:
                  id: "srp-circuit-breakers"
                  url: ${sys.get_env("srp_circuit_breakers_url")}
                  dry_run: ${sys.get_env("dry_run")}
                  retries_num: 0
    - run_unmanaged_workflow:
    # Grouping step to catch ETL non-retryable errors and alert to a Mattermost channel
        try:
          steps:
            - get_current_time:
                call: run_http_function
                args:
                  function:
                    id: "current-time"
                    url: ${sys.get_env("get_current_time_url")}
                    body: {}
                result: current_time
            - run_srp_sequential_functions:
                # Run SRP flow first
                call: run_sequential_functions
                args:
                  sequential_functions: ${srp_sequential_functions}
                result: srp_sequential_functions_result
            - assign_srp_sequential_functions_result:
                assign:
                  - result["run-srp-sequential-functions"]: ${srp_sequential_functions_result}
            - run_srp_circuit_breakers_tuesday:
                # Run SRP circuit breakers only on Tuesday (dependency on MPS per country function)
                switch:
                  - condition: ${current_time.body.day_of_week == "Tuesday"}
                    steps:
                      - run_srp_circuit_breakers_tuesday_function:
                          call: run_sequential_functions
                          args:
                            sequential_functions: ${srp_circuit_breakers_tuesday_function}
                          result: srp_circuit_breakers_tuesday_function_result
                      - assign_srp_circuit_breakers_tuesday_function_result:
                          assign:
                            - result["run-srp-circuit-breaker-tuesday-function"]: ${srp_circuit_breakers_tuesday_function_result}
            - run_parallel_functions:
                call: run_parallel_functions
                args:
                  parallel_functions: ${parallel_functions}
                result: parallel_functions_result
            - assign_parallel_functions_result:
                assign:
                  - result["run-parallel-functions"]: ${parallel_functions_result}
        except:
          as: e
          steps:
            - assign_workflow_error:
                assign:
                  - workflow_execution.result: ${result}
                  - workflow_execution.error: ${e}
                  - workflow_execution.end: ${sys.now()}
            - send_error_notification:
                call: run_http_function
                args:
                  function:
                    id: ${sys.get_env("workflow_finalizer_name")}
                    url: ${sys.get_env("workflow_finalizer_url")}
                    body: ${workflow_execution}
                result: send_error_notification_result
            - raiseError:
                raise: ${workflow_execution}
    - assign_workflow_result:
        assign:
          - workflow_execution.result: ${result}
          - workflow_execution.end: ${sys.now()}
    - send_success_notification:
        call: run_http_function
        args:
          function:
            id: ${sys.get_env("workflow_finalizer_name")}
            url: ${sys.get_env("workflow_finalizer_url")}
            body: ${workflow_execution}
        result: send_success_notification_result
    - return_result:
        return: ${result}
