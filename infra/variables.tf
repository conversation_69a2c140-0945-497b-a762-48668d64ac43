variable "force_deploy" {
  description = "Allows to deploy Cloud Run Function/Job even if its code has not been changed"
  type        = bool
  default     = false
}

variable "analytics_db_tier" {
  description = "Analytics DB tier"
  type        = string
  default     = "db-f1-micro"
}

variable "analytics_db_pg_version" {
  description = "Analytics DB Postgres version"
  type        = string
  default     = "POSTGRES_15"
}

variable "analytics_db_should_create" {
  description = "Whether to create DB"
  type        = bool
}

variable "daily_every_8_hours_schedule_enabled" {
  description = "Whether to enable daily_every_8_hours_schedule"
  type        = bool
}

variable "every_30_minutes_schedule_enabled" {
  description = "Whether to enable every_30_minutes_schedule"
  type        = bool
  default     = false
}

variable "cicd_repo_should_create" {
  description = "Whether to create Docker CI/CD Artifact Repository"
  type        = bool
}

variable "tests_bucket_should_create" {
  description = "Whether to create a bucket for integration tests"
  type        = bool
}

variable "tests_topic_should_create" {
  description = "Whether to create a topic for integration tests"
  type        = bool
}

variable "dhl_remove_processed_files" {
  description = "Whether to remove processed files in DHL"
  type        = bool
  default     = false
}

# Platform export RAM/CPU for small jobs
variable "platform_export_small_memory" {
  description = "Small cloud run job function memory settings"
  type        = string
  default     = "2Gi"
}

variable "platform_export_small_cpu" {
  description = "Small cloud run job cpu settings"
  type        = string
  default     = "1.0"
}

# Platform export RAM/CPU for medium jobs
variable "platform_export_medium_memory" {
  description = "Medium cloud run job function memory settings"
  type        = string
  default     = "2Gi"
}

variable "platform_export_medium_cpu" {
  description = "Medium cloud run job cpu settings"
  type        = string
  default     = "1.0"
}

# Platform export RAM/CPU for large jobs
variable "platform_export_large_memory" {
  description = "Large cloud run job function memory settings"
  type        = string
  default     = "2Gi"
}

variable "platform_export_large_cpu" {
  description = "Large cloud run job cpu settings"
  type        = string
  default     = "1.0"
}

# Platform export timeout for all jobs
variable "platform_export_timeout" {
  description = "Cloud run job timeout settings for platform export"
  type        = string
  default     = "28800s" # 8 hours
}
