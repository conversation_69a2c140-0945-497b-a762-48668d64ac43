# This is variables definition file.
# Ref. https://developer.hashicorp.com/terraform/language/values/variables#variable-definitions-tfvars-files
# DO NOT SPECIFY ANY SECRET HERE

env_name       = "production"
gcp_project_id = "refb-analytics"

# Cloud SQL Analytics
analytics_db_should_create = false

# Cloud Functions Schedules
daily_every_8_hours_schedule_enabled = true
every_30_minutes_schedule_enabled    = true

cicd_repo_should_create    = false
tests_bucket_should_create = false
tests_topic_should_create  = false
dhl_remove_processed_files = true

# Platform export RAM/CPU configurations: https://cloud.google.com/functions/docs/configuring/memory

# LARGE
platform_export_large_memory = "16Gi"
platform_export_large_cpu    = "4.0"

# MEDIUM
platform_export_medium_memory = "4Gi"
platform_export_medium_cpu    = "1.0"

#SMALL
platform_export_small_memory = "2Gi"
platform_export_small_cpu    = "1.0"
