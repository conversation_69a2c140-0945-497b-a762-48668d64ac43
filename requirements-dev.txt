-r requirements.txt

# Required for local development only!
pre-commit==4.2.0
black==25.1.0
flake8==7.2.0
isort==6.0.1
sqlfluff==3.4.0
pandas-stubs==2.2.3.250527

# unit-testing
pytest==8.4.0
responses==0.25.7
moto[s3]==5.1.5

# https://pypi.org/project/pytest-postgresql/
pytest-postgresql==7.0.2

# https://www.psycopg.org/psycopg3/docs/basic/install.html
psycopg[binary]
types-protobuf==6.30.2.20250516

# https://pypi.org/project/pytest-env/
pytest-env==1.1.5

# https://cloud.google.com/workflows/docs/execute-workflow-client-libraries
google-cloud-workflows==1.18.2
