# AI Assistant Guidelines

Project-level instructions for AI assistants working with this codebase.

## Project Architecture and Patterns

### Repository Pattern
- The project extensively uses the Repository Pattern to abstract database access
- `SqlRepository` is the base class for database access, providing methods for common SQL operations
- Specialized repositories inherit from `SqlRepository` and add domain-specific methods
- Repositories are initialized with a database configuration and optional connector

### ETL Pattern
- Many modules follow the Extract-Transform-Load (ETL) pattern
- ETL classes typically take repositories and other services as dependencies
- ETL processes often involve extracting data from one source, transforming it, and loading it into another destination

### Database Connection Handling
- Database connections are managed using SQLAlchemy
- Cloud SQL connections use the `google.cloud.sql.connector.Connector` class
- Connections are typically created in a context manager (`with` statement) to ensure proper cleanup
- Transactions are managed using the `begin_transaction` method from `SqlRepository`

### Configuration Management
- Configuration is managed through environment variables and secret management
- `Config` classes are used to encapsulate configuration parameters
- Secrets are retrieved using the `get_secret` function from `common.secret_client`
- Database configurations are created using the `DatabaseConfig` class

## Coding guidelines

See [Coding conventions](./src/README.md) in project `README.md`.

### Project structure
- The source code is in python with the root `src` folder.
- The tests are in `tests` folder.
- Associated code is deployed to GCP with infrastructure as a code in terraform from `infra` folder.
- Postgres SQL code is in `src/sql-pg` folder.
- BigQuery SQL code is in `src/sql-bg` folder.
- [flyway migrations](https://documentation.red-gate.com/fd/migrations-271585107.html) are used to deploy SQL code.
- gitlab pipeline is used as CI/CD.

### Testing guidelines
When making changes to the codebase, run the appropriate tests:
- Run `make test-unit` and ensure it passes for Python code changes
- Run `make test-sql` and ensure it passes for Postgres SQL changes
- Run `make test-bq` and ensure it passes for BigQuery SQL changes

We are using `make` commands to properly pass environment variables in tests.

### Build instructions
Building the project before submitting changes is not required.

### Code style
We use a pre-commit hook to lint and format the code.
Run it with `make code-check`.

### Programming paradigms
- Follow the code style in the project, we prefer OOP over functional style.
- We prefer to encapsulate the logic in classes rather than functions.

### Documentation
- We use [reStructuredText](https://devguide.python.org/documentation/markup/) comments in docstring.
- Each public method needs to be commented.

### Class structure
- Public methods should be defined before private members.

### Data types
- Use type hints for every public member or function parameter.
- Prefer `DataClass` over python dictionaries for data objects.
- Data object should inherit from `DataClassJsonMixin`, see `ExportStatus` for example.
- Use `DataFrameWithSchema` in place of untyped `pandas.DataFrame` for repository return types.
- Always define schema for `pandas.DataFrame` using `BaseDataFrameSchema`, see `RevenueDataSchema` for example.
