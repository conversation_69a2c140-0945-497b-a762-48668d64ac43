include:
  - remote: 'https://gitlab.com/gitlab-com/gl-security/security-operations/infrastructure-security-public/oidc-modules/-/raw/3.1.2/templates/gcp_auth.yaml'

# List of stages for jobs, and their order of execution: https://docs.gitlab.com/ee/ci/yaml/index.html#stages
stages:
  - 📦 build
  - 🧪 lint & test
  - 📝 plan
  - 💣 deploy staging
  - 🧐 integration test
  - 🚀 deploy production
  - ✨ tag
  - 📨 notify

variables:
  # $RUN_REPEATABLE_SCRIPTS set on the Gitlab level with default to false
  # https://gitlab.com/refurbed/analytics-and-ds/analytics-pipelines/-/settings/ci_cd#js-cicd-variables-settings

  GIT_DEPTH: 10 # https://docs.gitlab.com/ee/user/project/repository/monorepos/#shallow-cloning
  GCP_REGION: europe-west3
  ENV_NAME: staging # default env
  CICD_DOCKER_REGISTRY: ${GCP_REGION}-docker.pkg.dev/refb-analytics-staging/cicd-artifacts
  CICD_BUILD_IMAGE: ${CICD_DOCKER_REGISTRY}/cicd-build
  CICD_TERRAFORM_IMAGE: ${CICD_DOCKER_REGISTRY}/cicd-infra
  CICD_MIGRATIONS_IMAGE: ${CICD_DOCKER_REGISTRY}/cicd-migrations
  VERSION_TAG: ${CI_COMMIT_REF_SLUG}-${CI_COMMIT_SHORT_SHA}

  DB_SCHEMA_IMAGE_NAME: analytics-db-schema
  ANALYTICS_DB_SCHEMA_IMAGE: ${CICD_DOCKER_REGISTRY}/${DB_SCHEMA_IMAGE_NAME}:${VERSION_TAG}

  APP_IMAGE_NAME: cicd-app
  APP_IMAGE: ${CICD_DOCKER_REGISTRY}/${APP_IMAGE_NAME}:${VERSION_TAG}

image: ${CICD_BUILD_IMAGE}

.gitlab-oidc:
  extends: .google-oidc:auth
  variables:
    WI_POOL_PROVIDER: //iam.googleapis.com/projects/${GOOGLE_PROJECT_NO}/locations/global/workloadIdentityPools/gitlab-pool-oidc-env-${ENV_NAME}/providers/gitlab-jwt-env-${ENV_NAME}
    SERVICE_ACCOUNT: gitlab-cicd@${GOOGLE_PROJECT_ID}.iam.gserviceaccount.com
    GCLOUD_PROJECT: ${GOOGLE_PROJECT_ID}

.docker-login:
  script:
    - export HOME=/tmp # To avoid errors: Read-only file system: '/root/.docker/
    - gcloud auth configure-docker ${GCP_REGION}-docker.pkg.dev --quiet

.terraform:
  image: ${CICD_TERRAFORM_IMAGE}

.terraform-oidc:
  extends:
    - .terraform
    - .gitlab-oidc

.terraform-init: &terraform-init
  - |
    cd infra
    terraform --version
    echo BACKEND_CONFIG_PATH=${BACKEND_CONFIG_PATH}
    terraform init -backend-config=${BACKEND_CONFIG_PATH}

.infra-modules-repo-access: &infra-modules-repo-access
  - git config --global url."https://gitlab-ci-token:${CI_JOB_TOKEN}@gitlab.com".insteadOf "https://gitlab.com"

.terraform-vars:
  variables:
    BACKEND_CONFIG_PATH: ${INFRA_DIR}/backend.hcl
    VAR_FILE_PATH: ${INFRA_DIR}/variables.tfvars

.terraform-validate:
  extends: .terraform
  script:
    - *infra-modules-repo-access
    - cd infra
    - terraform init -backend=false
    - terraform validate

.terraform-plan:
  extends: .terraform-oidc
  stage: 📝 plan
  script:
    - *infra-modules-repo-access
    - *terraform-init
    - echo VAR_FILE_PATH=${VAR_FILE_PATH} TF_PLAN_FILENAME=${TF_PLAN_FILENAME}
    - terraform plan -var-file=${VAR_FILE_PATH} -out=${TF_PLAN_FILENAME}
  artifacts:
    paths:
      - infra/${TF_PLAN_FILENAME}
  resource_group: ${ENV_NAME}

.terraform-apply:
  extends: .terraform-oidc
  script:
    - export HOME=/tmp # To avoid errors: Read-only file system: '/root/.docker/
    - *infra-modules-repo-access
    - *terraform-init
    - echo VAR_FILE_PATH=${VAR_FILE_PATH}
    - terraform apply -auto-approve -var-file=${VAR_FILE_PATH}
  when: manual
  resource_group: ${ENV_NAME}

.unit-test:
  stage: 🧪 lint & test
  image: ${APP_IMAGE}
  artifacts:
    when: always
    reports:
      junit: test-report.xml

.integration-test:
  stage: 🧐 integration test
  image: ${APP_IMAGE}
  extends:
    - .staging-vars
    - .gitlab-oidc
  artifacts:
    when: always
    reports:
      junit: test-report.xml

.staging-vars:
  extends: .terraform-vars
  variables:
    ENV_NAME: "staging"
    INFRA_DIR: "env/${ENV_NAME}"
    GOOGLE_PROJECT_NO: 205674098492
    GOOGLE_PROJECT_ID: "refb-analytics-${ENV_NAME}"
    TF_PLAN_FILENAME: "${GOOGLE_PROJECT_ID}-tfplan"

.production-vars:
  extends: .terraform-vars
  variables:
    ENV_NAME: "production"
    INFRA_DIR: "env/${ENV_NAME}"
    GOOGLE_PROJECT_NO: 472481106066
    GOOGLE_PROJECT_ID: "refb-analytics"
    TF_PLAN_FILENAME: "${GOOGLE_PROJECT_ID}-tfplan"

# Run Postgres migrations
.flyway-pg-vars:
  variables:
    PG_MIGRATIONS_PATH: $CI_PROJECT_DIR/src/sql-pg
    FLYWAY_PG_CONFIG_FILE: $PG_MIGRATIONS_PATH/flyway.toml
    DB_SECRET_NAME: "analytics-cloud-sql-config"
    RUN_REPEATABLE_SCRIPTS: $RUN_REPEATABLE_SCRIPTS
    FLYWAY_COMMAND: "info -infoOfState=Pending,Outdated"

.plan-pg:
  image: ${CICD_MIGRATIONS_IMAGE}
  extends:
    - .flyway-pg-vars
    - .gitlab-oidc
  script:
    - ./.gitlab/migrate_pg.sh

.migrate-pg:
  variables:
    FLYWAY_COMMAND: "info -infoOfState=Pending,Outdated repair migrate"
  extends:
    - .plan-pg
  resource_group: ${ENV_NAME}

# Run BigQuery migrations
.flyway-bq-vars:
  variables:
    BQ_MIGRATIONS_PATH: $CI_PROJECT_DIR/src/sql-bq
    FLYWAY_BQ_CONFIG_FILE: $BQ_MIGRATIONS_PATH/flyway.toml
    FLYWAY_COMMAND: "info -infoOfState=Pending,Outdated"

.plan-bq:
  image: ${CICD_MIGRATIONS_IMAGE}
  extends:
    - .flyway-bq-vars
    - .gitlab-oidc
  script:
    - ./.gitlab/migrate_bq.sh

.migrate-bq:
  variables:
    FLYWAY_COMMAND: "info -infoOfState=Pending,Outdated repair migrate"
  extends:
    - .plan-bq
  resource_group: ${ENV_NAME}
