#!/bin/bash
set -euo pipefail

secret_read() {
  : '
    Reads the GCP secret for given secret id.
   '
  local SECRET_ID="${1}"
  SECRET=$(gcloud secrets versions access "latest" --secret="${SECRET_ID}" | jq)

  echo "${SECRET}"
}

get_run_id() {
  : '
    Gets the latest db refresh run id. Needed for SQL migrations change reason.
   '
  local USER="${1}"
  local PASSWORD="${2}"
  SQL_CMD="select run_id from log.pipeline_execution where pipeline_name = 'analytics db refresh' and succeeded = true order by finished desc limit 1;"
  RUN_ID="$(PGPASSWORD="${PASSWORD}" psql -t -U "${USER}" -d platform -h localhost -p 5432 -c """${SQL_CMD}""")"

  echo "${RUN_ID}" | xargs echo -n
}

proxy_start() {
  : '
    Starts GCP Proxy: https://cloud.google.com/sql/docs/postgres/sql-proxy
    Reads DB credentials from G<PERSON> secret.
    Needed in the next steps to run SQL migrations in Gitlab.
   '
  local SECRET_NAME="${1}"
  local SECRET DB_HOST USER PWD

  SECRET=$(secret_read "${SECRET_NAME}")
  DB_HOST=$(echo "${SECRET}" | jq -r .host)

  echo "Connecting via CLoudSQL Proxy to '$DB_HOST'..."
  cloud-sql-proxy "${DB_HOST}" &
  sleep 5
  echo "Connected"
}

proxy_stop() {
  : '
    Stops GCP Proxy: https://cloud.google.com/sql/docs/postgres/sql-proxy
   '
  # shellcheck disable=SC2009
  PID=$(ps | grep cloud-sql-proxy | grep -v grep | awk '{print $1}')

  if [ -n "${PID}" ]; then
    echo "Stopping CLoudSQL Proxy..."
    kill "${PID}"
  fi
}

migrate_pg() {
  : '
    Runs Flyway migration: https://flywaydb.org/
    Reads DB credentials from GCP secret.
   '
  local SECRET_NAME="${1}"
  local CONFIG_PATH="${2}"
  local MIGRATIONS_PATH="${3}"
  local ENV="${4}"
  local COMMANDS="${5:-'info migrate'}"
  local RUN_REPEATABLE_SCRIPTS="${6:-false}"

  local DB_SECRET USER PWD FLAGS CHANGE_REASON PLACEHOLDERS

  DB_SECRET=$(secret_read "${SECRET_NAME}")
  USER=$(echo "${DB_SECRET}" | jq -r .username)
  PWD=$(echo "${DB_SECRET}" | jq -r .password)

  echo "RUN_REPEATABLE_SCRIPTS: ${RUN_REPEATABLE_SCRIPTS}"
  if [ "$RUN_REPEATABLE_SCRIPTS" = true ] ; then
      CHANGE_REASON="$(date "+%Y%m%d-%H%M%S")"
      PLACEHOLDERS="-placeholders.changeReason=${CHANGE_REASON}"
  else
      echo "Getting the latest changeReason..."
      CHANGE_REASON="$(get_run_id "${USER}" "${PWD}")"

      if [ -n "$CHANGE_REASON" ]; then
        PLACEHOLDERS="-placeholders.changeReason=${CHANGE_REASON}"
      else
        echo "No changeReason found..."
        PLACEHOLDERS=""
      fi
  fi

  echo "CONFIG_PATH: $CONFIG_PATH"
  echo "MIGRATIONS_PATH: $MIGRATIONS_PATH"
  echo "ENV: $ENV"
  echo "PLACEHOLDERS: $PLACEHOLDERS"

  # shellcheck disable=SC2207,SC2116
  FLAGS=($(echo "${COMMANDS}"))

  echo "Running Flyway with following command(s): '${COMMANDS}'..."
  flyway \
   -configFiles="${CONFIG_PATH}"\
   -locations="filesystem:${MIGRATIONS_PATH}"\
   -environment="${ENV}"\
   -user="${USER}"\
   -password="${PWD}"\
   "${PLACEHOLDERS}"\
   "${FLAGS[@]}"
}

main() {
  : '
    Migrates Postgres via Flyway.
   '
  proxy_start "$DB_SECRET_NAME"

  # RUN_REPEATABLE_SCRIPTS is set on the Gitlab CICD variables to allow force run
  migrate_pg "$DB_SECRET_NAME" "$FLYWAY_PG_CONFIG_FILE" "$PG_MIGRATIONS_PATH" "$ENV_NAME" "$FLYWAY_COMMAND" "$RUN_REPEATABLE_SCRIPTS"
  proxy_stop
}

main
