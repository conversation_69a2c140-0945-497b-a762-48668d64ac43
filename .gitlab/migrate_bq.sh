#!/bin/bash
set -euo pipefail

migrate_bq() {
  : '
    Runs Flyway Big Query migration:
    https://documentation.red-gate.com/flyway/reference/database-driver-reference/google-bigquery
   '
  local CONFIG_PATH="${1}"
  local MIGRATIONS_PATH="${2}"
  local ENV="${3}"
  local COMMANDS="${4:-'info migrate'}"

  echo "CONFIG_PATH: $CONFIG_PATH"
  echo "MIGRATIONS_PATH: $MIGRATIONS_PATH"
  echo "ENV: $ENV"
  echo "COMMANDS: $COMMANDS"
  # shellcheck disable=SC2207,SC2116
  FLAGS=($(echo "${COMMANDS}"))

  echo "Running Flyway with following command(s): '${COMMANDS}'..."
  flyway \
   -configFiles="${CONFIG_PATH}"\
   -locations="filesystem:${MIGRATIONS_PATH}"\
   -environment="${ENV}"\
   "${FLAGS[@]}"
}

main() {
  : '
    Migrates Big Query via Flyway.
   '
  migrate_bq "$FLYWAY_BQ_CONFIG_FILE" "$BQ_MIGRATIONS_PATH" "$ENV_NAME" "$FLYWAY_COMMAND"
}

main
