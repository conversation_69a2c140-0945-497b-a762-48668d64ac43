# GitLab docker images

This directory contains `Docker<PERSON>le's` used in CI/CD pipelines.
They are not rebuilt automatically, since should not change frequently.
There is one Docker registry (GCP Artifact Registry) to store the images on the `refb-analytics-staging` GCP account.

## GitLab integration with GCP Artifact Registry

To use our custom image in GitLab, some prerequisites are needed:

* A dedicated service account with `roles/artifactregistry.reader` minimum permissions needed to pull the image from the registry,
* GitLab `DOCKER_AUTH_CONFIG` variable needed as described [here](https://docs.gitlab.com/ee/ci/docker/using_docker_images.html#determine-your-docker_auth_config-data),
* Credentials should be generated as described [here](https://cloud.google.com/artifact-registry/docs/docker/authentication?_gl=1*rfv51d*_ga******************************_ga_WH2QY8WWF5*********************************************&_ga=2.********.-**********.**********#json-key):
  * Generate and download locally service account key for `artifact-registry-gitlab`,
  * Run following command:
    ```bash
    gcloud auth configure-docker europe-west3-docker.pkg.dev --quiet
    cat refb-analytics-staging.json | docker login -u _json_key --password-stdin https://europe-west3-docker.pkg.dev
    ```
  * Copy content of `~/.docker/config.json` into GitLab `DOCKER_AUTH_CONFIG` variable.

⚠️ In case of error `Error saving credentials: error storing credentials - err: exit status 1`, remove your local `~/.docker/config.json` file first.

## Docker images

There are 4 images used by the gitlab CI/CD pipeline and for local development.

### CI/CD images

There are 3 images used by the gitlab CI/CD pipeline:
- [build](build.Dockerfile) is the `cicd-build` image used to build application code
- [infra](infra.Dockerfile) is the `cicd-infra` image used to build and deploy terraform code
- [migrations](migrations.Dockerfile) is the `cicd-migrations` image used to run `flyway` migrations

Use `make push-cicd-images` command to build and push those images into GCP Artifact Registry.

### Database image

The database image contains base postgres image with a copy of platform `preview` database.

The image is used by:
- `docker-compose.yml` in root folder for local `SQL` development and testing;
- `docker-compose.yml` in `.gitlab/db_schema` for building database schema;

#### Usage
- Use `make pg-up` to run the image with the preview database.
- Use `make pg-migrate` to run flyway migrations and use it for local development or testing.

#### Creating a new database dump

In order to prepare new platform `preview` dump do the following:
1. Ask in the [Tech -> Platform](https://ozwiena.refurbed.io/refurbed/pl/7h4u48hicir89ry9bg1zafufba) MM channel
for the new `pg_dump` of platform database.
2. Test the restore of the `dump` locally by using the following script:
```bash
psql --username="$POSTGRES_USER" --host="$POSTGRES_HOST" --port="$POSTGRES_PORT" --dbname="$POSTGRES_DB" -f "$DUMP_PATH"
```
3. It's likely the script needs a few fixes, so apply them accordingly.
4. Place content of the following migration into sql dump:
`src/analytics_db_refresh/sql/source/schema/R__0001_reload_order_item_offers.sql`
5. Place the following script at the end of the `SQL` dump:
```sql
-- Create and load public.order_item_offers table
<-- Paste the body of `maintenance.reload_order_item_offers` procedure -->

-- Healthcheck in docker compose
drop table if exists public.healthcheck;
create unlogged table public.healthcheck
as
select current_timestamp as created_at;

-- Reclaims disk space and updates statistics
vacuum (verbose, analyze);
```
6. Name and upload the file into [GCS bucket](https://console.cloud.google.com/storage/browser/analytics-pipelines-staging-backup/platform-preview)
7. Update `DUMP_NAME` variable with the new name in the `.env` and `.env.dist` files.
8. Run `make push-db-image` to build and push the image to GCP Artifact Registry.
9. Create a gitlab merge request with above changes to inform the team about new image.
10. Restore this file to the `staging` cloud-sql database using cloud-sql [import](https://cloud.google.com/sql/docs/postgres/import-export/import-export-sql#import_data_to).
