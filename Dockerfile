# This is image for Python app CI/CD build and tests.
FROM gcr.io/google.com/cloudsdktool/google-cloud-cli:521.0.0-slim AS gcloud
FROM hashicorp/terraform:1.11 AS tf
FROM flyway/flyway:11.8 AS flyway
FROM python:3.12-slim

COPY --from=gcloud /usr/lib/google-cloud-sdk /usr/lib/google-cloud-sdk
ENV PATH=$PATH:/usr/lib/google-cloud-sdk/bin/

COPY --from=tf /bin/terraform /bin/terraform
COPY --from=flyway /flyway /flyway
COPY --from=flyway /opt/java/openjdk /opt/java/openjdk

ENV PATH="${PATH}:/usr/lib/google-cloud-sdk/bin/:/opt/java/openjdk/bin:/flyway/"

RUN apt-get update
RUN apt-get install -y build-essential manpages-dev musl-dev linux-headers-amd64 bash curl git

RUN pip install --upgrade pip

ENV PYTHONUNBUFFERED=1

# Set our working directory
WORKDIR /app

# Copy all build related files.
# This should improve consecutive builds.
COPY .env.dist .
COPY .flake8 .
COPY .pre-commit-config.yaml .
COPY pyproject.toml .
COPY requirements.txt .
COPY requirements-dev.txt .

# Then install all dependencies
RUN python -m venv venv
RUN . venv/bin/activate
RUN pip install -r requirements-dev.txt

# Initialize pre-commit
RUN git init . && pre-commit install-hooks

# Now copy over the rest of the code
COPY tests tests
COPY src src
