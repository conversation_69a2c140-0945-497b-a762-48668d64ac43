# This is image for Python app CI/CD build and tests.
# Multi-stage build for better caching and smaller final image
FROM gcr.io/google.com/cloudsdktool/google-cloud-cli:521.0.0-slim AS gcloud
FROM hashicorp/terraform:1.11 AS tf
FROM flyway/flyway:11.8 AS flyway

# Base stage with system dependencies
FROM python:3.12-slim AS base

# Set environment variables early for better caching
ENV PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    PIP_NO_CACHE_DIR=1 \
    PIP_DISABLE_PIP_VERSION_CHECK=1

# Copy external tools from multi-stage builds
COPY --from=gcloud /usr/lib/google-cloud-sdk /usr/lib/google-cloud-sdk
COPY --from=tf /bin/terraform /bin/terraform
COPY --from=flyway /flyway /flyway
COPY --from=flyway /opt/java/openjdk /opt/java/openjdk

# Set PATH once with all required paths
ENV PATH="/usr/lib/google-cloud-sdk/bin:/opt/java/openjdk/bin:/flyway:${PATH}"

# Install system dependencies in a single layer with cleanup
RUN apt-get update && \
    apt-get install -y --no-install-recommends \
        build-essential \
        manpages-dev \
        musl-dev \
        linux-headers-amd64 \
        bash \
        curl \
        git && \
    apt-get clean && \
    rm -rf /var/lib/apt/lists/* /tmp/* /var/tmp/*

# Upgrade pip in the same layer
RUN pip install --upgrade pip

# Dependencies stage for better caching
FROM base AS dependencies

# Set working directory
WORKDIR /app

# Copy only dependency files first for better Docker layer caching
COPY requirements.txt requirements-dev.txt ./

# Create virtual environment and install dependencies in one layer
RUN python -m venv venv && \
    . venv/bin/activate && \
    pip install --no-cache-dir -r requirements-dev.txt

# Configuration stage
FROM dependencies AS config

# Copy configuration files
COPY .env.dist .flake8 .pre-commit-config.yaml pyproject.toml ./

# Initialize git and pre-commit hooks
RUN git init . && \
    . venv/bin/activate && \
    pre-commit install-hooks

# Final stage with application code
FROM config AS final

# Copy application code last for optimal caching
COPY tests tests/
COPY src src/

# Activate virtual environment by default
ENV PATH="/app/venv/bin:$PATH"

# Add health check for container monitoring
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD python --version || exit 1
