include: .gitlab/templates.yml

workflow:
  rules:
    - if: $CI_COMMIT_TAG
      when: never
    - if: $CI_PIPELINE_SOURCE == "schedule"
      when: never
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
      when: never
    - when: always

# https://stackoverflow.com/a/67012275
.step_rules:
  rules:
    - &main_branch_only_rule
      if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
      when: on_success
    - &main_branch_manual_rule
      if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
      when: manual
    - &sql_pg_files_changed
      changes:
        - src/sql-pg/**
    - &sql_bq_files_changed
      changes:
        - src/sql-bq/**

###########
# 📦 build
###########
build-app:
  stage: 📦 build
  extends:
    - .staging-vars
    - .gitlab-oidc
  script:
    - !reference [ .docker-login, script ]
    - >
      docker build --force-rm --progress=plain
      --build-arg BUILDKIT_INLINE_CACHE=1
      --tag "${APP_IMAGE}" .
      --cache-from "${CICD_DOCKER_REGISTRY}/${APP_IMAGE_NAME}:latest"
    - docker push "${APP_IMAGE}"

build-db-schema:
  stage: 📦 build
  extends:
    - .staging-vars
    - .gitlab-oidc
  needs:
    - build-app
  variables:
    IMAGE_NAME: ${ANALYTICS_DB_SCHEMA_IMAGE}
  script:
    - !reference [ .docker-login, script ]
    - ./.gitlab/db_schema/build_db_schema_image.sh
  rules:
    - *sql_pg_files_changed

#################
# 🧪 lint & test
#################
lint-code:
  stage: 🧪 lint & test
  image: ${APP_IMAGE}
  script:
    - pre-commit run --all-files

validate-terraform:
  stage: 🧪 lint & test
  extends: .terraform-validate

unit-test:
  extends: .unit-test
  script:
    - pytest tests/unit --junitxml=test-report.xml -rA

sql-test:
  extends: .unit-test
  allow_failure: true
  services:
    - name: ${ANALYTICS_DB_SCHEMA_IMAGE}
      alias: ${DB_SCHEMA_IMAGE_NAME}
      command: [ "postgres", "-c", "max_connections=1000" ]
  variables:
    POSTGRES_HOST: ${DB_SCHEMA_IMAGE_NAME}
    POSTGRES_PORT: 5432
  script:
    - pytest tests/integration/test_local_sql --junitxml=test-report.xml -rA
  rules:
    - *sql_pg_files_changed

###########
# 📝 plan
###########
01-plan-staging-infra:
  extends:
    - .staging-vars
    - .terraform-plan

02-plan-staging-pg:
  stage: 📝 plan
  extends:
    - .staging-vars
    - .plan-pg
  rules:
    - *sql_pg_files_changed

03-plan-staging-bq:
  stage: 📝 plan
  extends:
    - .staging-vars
    - .plan-bq
  rules:
    - *sql_bq_files_changed

04-plan-production-infra:
  extends:
    - .production-vars
    - .terraform-plan

05-plan-production-pg:
  stage: 📝 plan
  extends:
    - .production-vars
    - .plan-pg
  rules:
    - *sql_pg_files_changed

06-plan-production-bq:
  stage: 📝 plan
  extends:
    - .production-vars
    - .plan-bq
  rules:
    - *sql_bq_files_changed

###################
# 💣 deploy staging
###################
01-apply-staging-infra:
  stage: 💣 deploy staging
  extends:
    - .staging-vars
    - .terraform-apply
  when: manual
  needs:
    - 01-plan-staging-infra

02-migrate-staging-pg:
  stage: 💣 deploy staging
  extends:
    - .staging-vars
    - .migrate-pg
  when: manual
  needs:
    - 02-plan-staging-pg
  rules:
    - *sql_pg_files_changed

03-migrate-staging-bq:
  stage: 💣 deploy staging
  extends:
    - .staging-vars
    - .migrate-bq
  when: manual
  needs:
    - 03-plan-staging-bq
  rules:
    - *sql_bq_files_changed

######################
# 🧐 integration test
######################
01-cloud-sql-test:
  extends: .integration-test
  needs:
    - 01-apply-staging-infra
    - 02-migrate-staging-pg
  script:
    - pytest tests/integration/test_cloud_sql --junitxml=test-report.xml -rA
  rules:
    - *sql_pg_files_changed

02-bq-sql-test:
  extends: .integration-test
  needs:
    - 01-apply-staging-infra
    - 03-migrate-staging-bq
  script:
    - pytest tests/integration/test_big_query --junitxml=test-report.xml -rA
  rules:
    - *sql_bq_files_changed

03-workflow-test:
  extends: .integration-test
  needs:
    - 01-apply-staging-infra
  script:
    - pytest tests/integration/test_workflow --junitxml=test-report.xml -rA

04-cloud-storage-test:
  extends: .integration-test
  needs:
    - 01-apply-staging-infra
  script:
    - pytest tests/integration/test_cloud_storage --junitxml=test-report.xml -rA

05-cloud-sql-instance-test:
  extends: .integration-test
  needs:
    - 01-apply-staging-infra
  script:
    - pytest tests/integration/test_cloud_sql_instance_client.py --junitxml=test-report.xml -rA

06-cloud-run-test:
  extends: .integration-test
  needs:
    - 01-apply-staging-infra
  script:
    - pytest tests/integration/test_cloud_run* --junitxml=test-report.xml -rA

07-mattermost-client-test:
  extends: .integration-test
  needs:
    - 01-apply-staging-infra
  script:
    - pytest tests/integration/test_mattermost_client --junitxml=test-report.xml -rA

08-logging-and-alerting-test:
  extends: .integration-test
  needs:
    - 01-apply-staging-infra
  script:
    - pytest tests/integration/test_cloud_logging_client.py --junitxml=test-report.xml -rA
    - pytest tests/integration/test_logging_and_alerting --junitxml=test-report.xml -rA

09-pubsub-client-test:
  extends: .integration-test
  needs:
    - 01-apply-staging-infra
  script:
    - pytest tests/integration/test_pubsub_client --junitxml=test-report.xml -rA

######################
# Manual tests below:
######################
10-analytics-db-refresh-test:
  extends: .integration-test
  # Manual as it takes around 15 min
  when: manual
  allow_failure: true
  script:
    - pytest tests/integration/test_analytics_db_refresh --junitxml=test-report.xml -rA

11-google-sheets-client-test:
  extends: .integration-test
  # Manual, connecting to Google Sheets API fails too often!
  when: manual
  allow_failure: true
  script:
    - pytest tests/integration/test_google_sheets_client --junitxml=test-report.xml -rA

12-sftp-client-test:
  extends: .integration-test
  # Manual as we are connecting to live DHL sFTP server!
  when: manual
  allow_failure: true
  script:
    - pytest tests/integration/test_sftp_client --junitxml=test-report.xml -rA

13-pipedrive-api-client-test:
  extends: .integration-test
  # Manual as we are connecting to live Pipedrive API!
  when: manual
  allow_failure: true
  script:
    - pytest tests/integration/test_pipedrive_api_client --junitxml=test-report.xml -rA

14-algolia-client-test:
  extends: .integration-test
  # Manual as we are connecting to live Algolia API
  when: manual
  allow_failure: true
  script:
    - pytest tests/integration/test_algolia_api --junitxml=test-report.xml -rA

15-platform-export-test:
  extends: .integration-test
  # Manual as implementation is in progress
  when: manual
  allow_failure: true
  script:
    - pytest tests/integration/test_platform_export --junitxml=test-report.xml -rA

16-zendesk-client-test:
  extends: .integration-test
  # Manual as we are creating tickets in a Zendesk sandbox
  when: manual
  allow_failure: true
  script:
    - pytest tests/integration/test_zendesk_client --junitxml=test-report.xml -rA

#######################
# 🚀 deploy production
#######################
04-apply-production-infra:
  stage: 🚀 deploy production
  extends:
    - .production-vars
    - .terraform-apply
  rules:
    - *main_branch_manual_rule

05-migrate-production-pg:
  stage: 🚀 deploy production
  extends:
    - .production-vars
    - .migrate-pg
  rules:
    - *main_branch_manual_rule

06-migrate-production-bq:
  stage: 🚀 deploy production
  extends:
    - .production-vars
    - .migrate-bq
  rules:
    - *main_branch_manual_rule

########
# ✨ tag
########
.tag-image:
  stage: ✨ tag
  extends:
    - .staging-vars
    - .gitlab-oidc
  image: ${CICD_BUILD_IMAGE}
  rules:
    - *main_branch_only_rule
  script:
    - !reference [ .docker-login, script ]
    - docker pull ${IMAGE_NAME_FROM}
    - docker tag ${IMAGE_NAME_FROM} ${IMAGE_NAME_TO}
    - docker push ${IMAGE_NAME_TO}

tag-db-schema-image:
  extends: .tag-image
  variables:
    IMAGE_NAME_FROM: ${ANALYTICS_DB_SCHEMA_IMAGE}
    IMAGE_NAME_TO: ${CICD_DOCKER_REGISTRY}/${DB_SCHEMA_IMAGE_NAME}:latest

tag-app-image:
  extends: .tag-image
  variables:
    IMAGE_NAME_FROM: ${APP_IMAGE}
    IMAGE_NAME_TO: ${CICD_DOCKER_REGISTRY}/${APP_IMAGE_NAME}:latest

notify-failed:
  stage: 📨 notify
  when: on_failure
  script:
    - |
      export MM_MESSAGE=":exclamation: **GitLab pipeline has failed:** <$CI_PIPELINE_URL|$CI_PROJECT_NAME>\
      \n**User:** $GITLAB_USER_NAME\
      \n**Date:** $CI_JOB_STARTED_AT"
    - echo $MM_MESSAGE
    - |
      curl -i -X POST -H 'Content-type: application/json' --data "{
        \"text\": \"$MM_MESSAGE\"
      }" $MM_WEBHOOK;
  only:
    - main
