from datetime import datetime
from unittest.mock import MagicMock, patch

import pytest
from sqlalchemy import text

from common.migrate_db import FlywayParams
from migrate_db.migrate_pg import MigratePg


def test_get_run_id_success(migrate_pg: MigratePg, sql_repository: MagicMock) -> None:
    """
    Test that get_run_id returns the run_id from the database when the query succeeds.
    """
    # Arrange
    expected_run_id = "20230101_123456789"
    mock_result = MagicMock()
    mock_result.fetchone.return_value = [expected_run_id]

    mock_transaction = MagicMock()
    sql_repository.begin_transaction.return_value.__enter__.return_value = mock_transaction
    sql_repository._run_sql.return_value = mock_result

    # Act
    result = migrate_pg.get_run_id()

    # Assert
    assert result == expected_run_id
    sql_repository.begin_transaction.assert_called_once()
    sql_repository._run_sql.assert_called_once()

    # Verify the SQL query
    sql_query = sql_repository._run_sql.call_args[0][0]
    assert isinstance(sql_query, text)
    assert "SELECT run_id FROM log.pipeline_execution" in str(sql_query)
    assert "pipeline_name = 'analytics db refresh'" in str(sql_query)
    assert "succeeded = true" in str(sql_query)
    assert "ORDER BY finished DESC LIMIT 1" in str(sql_query)


def test_get_run_id_empty_result(migrate_pg: MigratePg, sql_repository: MagicMock) -> None:
    """
    Test that get_run_id returns None when the query returns no results.
    """
    # Arrange
    mock_result = MagicMock()
    mock_result.fetchone.return_value = None

    mock_transaction = MagicMock()
    sql_repository.begin_transaction.return_value.__enter__.return_value = mock_transaction
    sql_repository._run_sql.return_value = mock_result

    # Act
    result = migrate_pg.get_run_id()

    # Assert
    assert result is None


def test_get_run_id_exception(migrate_pg: MigratePg, sql_repository: MagicMock) -> None:
    """
    Test that get_run_id handles exceptions gracefully.
    """
    # Arrange
    sql_repository.begin_transaction.side_effect = Exception("Test exception")

    # Act
    result = migrate_pg.get_run_id()

    # Assert
    assert result is None


@patch("migrate_db.main.get_timestamp_run_id")
def test_get_flyway_params_with_run_repeatable_scripts(
    mock_get_timestamp_run_id: MagicMock, migrate_pg: MigratePg, database_config: MagicMock
) -> None:
    """
    Test that get_flyway_params returns the correct FlywayParams when run_repeatable_scripts is True.
    """
    # Arrange
    timestamp = "20230101_123456789"
    mock_get_timestamp_run_id.return_value = timestamp

    # Act
    result = migrate_pg.get_flyway_params(run_repeatable_scripts=True)

    # Assert
    assert isinstance(result, FlywayParams)
    assert result.host == database_config.host
    assert result.port == database_config.port
    assert result.database == database_config.database
    assert result.user == database_config.username
    assert result.password == database_config.password
    assert "runTest=false" in result.placeholders
    assert f"changeReason={timestamp}" in result.placeholders


@patch("migrate_db.main.MigratePg.get_run_id")
def test_get_flyway_params_with_run_id(
    mock_get_run_id: MagicMock, migrate_pg: MigratePg, database_config: MagicMock
) -> None:
    """
    Test that get_flyway_params returns the correct FlywayParams when run_repeatable_scripts is False
    and a run_id is found.
    """
    # Arrange
    run_id = "20230101_123456789"
    mock_get_run_id.return_value = run_id

    # Act
    result = migrate_pg.get_flyway_params(run_repeatable_scripts=False)

    # Assert
    assert isinstance(result, FlywayParams)
    assert "runTest=false" in result.placeholders
    assert f"changeReason={run_id}" in result.placeholders


@patch("migrate_db.main.MigratePg.get_run_id")
def test_get_flyway_params_without_run_id(
    mock_get_run_id: MagicMock, migrate_pg: MigratePg, database_config: MagicMock
) -> None:
    """
    Test that get_flyway_params returns the correct FlywayParams when run_repeatable_scripts is False
    and no run_id is found.
    """
    # Arrange
    mock_get_run_id.return_value = None

    # Act
    result = migrate_pg.get_flyway_params(run_repeatable_scripts=False)

    # Assert
    assert isinstance(result, FlywayParams)
    assert "runTest=false" in result.placeholders
    assert len(result.placeholders) == 1  # Only runTest=false, no changeReason


@patch("migrate_db.main.migrate_db")
def test_migrate(mock_migrate_db: MagicMock, migrate_pg: MigratePg) -> None:
    """
    Test that migrate calls migrate_db with the correct parameters.
    """
    # Arrange
    flyway_command = "info migrate"
    run_repeatable_scripts = True

    mock_flyway_params = MagicMock(spec=FlywayParams)
    with patch.object(migrate_pg, "get_flyway_params", return_value=mock_flyway_params) as mock_get_flyway_params:
        # Act
        migrate_pg.run(flyway_command=flyway_command, run_repeatable_scripts=run_repeatable_scripts)

        # Assert
        mock_get_flyway_params.assert_called_once_with(run_repeatable_scripts)
        mock_migrate_db.assert_called_once_with(params=mock_flyway_params, flyway_command=flyway_command)
