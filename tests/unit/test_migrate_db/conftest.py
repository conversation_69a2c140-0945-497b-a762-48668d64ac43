from unittest.mock import MagicMock, patch

import pytest

from common.config import Config
from common.migrate_db import FlywayParams
from common.sql_client import DatabaseConfig
from common.sql_repository import SqlRepository
from migrate_db.migrate_pg import MigratePg


@pytest.fixture
def config() -> Config:
    """
    Returns a mock Config object.
    """
    config = MagicMock(spec=Config)
    config.env = "test"
    return config


@pytest.fixture
def database_config() -> DatabaseConfig:
    """
    Returns a mock DatabaseConfig object.
    """
    return DatabaseConfig(
        host="test-host",
        port=5432,
        database="test-db",
        username="test-user",
        password="test-password",
    )


@pytest.fixture
def sql_repository() -> SqlRepository:
    """
    Returns a mock SqlRepository object.
    """
    return MagicMock(spec=SqlRepository)


@pytest.fixture
def migrate_pg(config: Config, database_config: DatabaseConfig, sql_repository: SqlRepository) -> MigratePg:
    """
    Returns a MigratePg instance with mocked dependencies.
    """
    with patch("migrate_db.main.SqlRepository", return_value=sql_repository):
        return MigratePg(config, database_config)
