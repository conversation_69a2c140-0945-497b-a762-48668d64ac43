from unittest.mock import Mock, patch

from export_loader import PlatformExportLoader
from export_shared.schema_mapping import SOURCE_TO_TARGET

from common.pipeline_logging.pipeline_logger import PipelineExecutionLogger
from platform_export.export_shared.export_model import ExportConfig


@patch.object(PipelineExecutionLogger, "finish_processing_step")
@patch.object(PipelineExecutionLogger, "start_processing_step")
def test_loader(
    mock_start_processing_step: Mock,
    mock_finish_processing_step: Mock,
    dataset_name: str,
    platform_repository: Mock,
    analytics_bq_repository: Mock,
    export_config: ExportConfig,
) -> None:
    # arrange
    bytes_processed = 1000
    analytics_bq_repository.load_from_raw.return_value = bytes_processed
    loader = PlatformExportLoader(
        config=export_config,
        platform_repository=platform_repository,
        analytics_bq_repository=analytics_bq_repository,
    )
    target_table = SOURCE_TO_TARGET[export_config.source].table_name
    expected = f"BigQuery table '{dataset_name}.{target_table}' loaded with {bytes_processed}"

    # act
    actual = loader.run()

    # assert
    assert expected in actual
    analytics_bq_repository.load_from_raw.assert_called_once()
    analytics_bq_repository.check_table.assert_called_once()
    mock_start_processing_step.assert_called_once()
    mock_finish_processing_step.assert_called_once()
