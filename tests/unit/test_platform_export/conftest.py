from unittest.mock import Mock

import pytest
from export_shared.schema_incremental import PRODUCTS_SCHEMA
from pandas import DataFrame

from common.data_lake_repository import DataLakeRepository
from common.pandas_utils import DataFrameSchema
from common.sql_model import SqlTable
from common.time_service import TimeService
from platform_export.export_shared.analytics_bq_repository import AnalyticsBQRepository
from platform_export.export_shared.export_model import ExportConfig
from platform_export.export_shared.platform_repository import PlatformRepository
from platform_export.export_shared.schema_mapping import (
    PRODUCTS_SOURCE,
    PRODUCTS_TARGET,
)


@pytest.fixture(scope="function")
def source_table() -> SqlTable:
    return PRODUCTS_SOURCE


@pytest.fixture(scope="function")
def source_schema() -> DataFrameSchema:
    return PRODUCTS_SCHEMA


@pytest.fixture(scope="function")
def target_table() -> str:
    return PRODUCTS_TARGET


@pytest.fixture(scope="function")
def extracted_df(source_schema: DataFrameSchema) -> DataFrame:
    columns = source_schema.columns
    return DataFrame(data=[[c for c in columns]], columns=columns)


@pytest.fixture(scope="function")
def export_config(time_service: TimeService, source_table: SqlTable) -> ExportConfig:
    return ExportConfig(
        timestamp=time_service.timestamp_in_sec,
        source=source_table,
    )


@pytest.fixture(scope="function")
def platform_repository() -> Mock:
    return Mock(spec=PlatformRepository)


@pytest.fixture(scope="function")
def raw_data_repository() -> Mock:
    return Mock(spec=DataLakeRepository)


@pytest.fixture(scope="session")
def dataset_name() -> str:
    return "dataset"


@pytest.fixture(scope="function")
def analytics_bq_repository(dataset_name: str) -> Mock:
    mock = Mock(spec=AnalyticsBQRepository)
    mock.DATASET = dataset_name

    return mock
