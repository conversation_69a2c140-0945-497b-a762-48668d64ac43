from datetime import datetime
from pathlib import Path
from unittest.mock import <PERSON><PERSON><PERSON>, Mock, patch

import pytest
from pandas import DataFrame
from pandas._testing import assert_frame_equal

from common.pipeline_logging.pipeline_logger import PipelineExecutionLogger
from platform_export.etl.export_extractor import PlatformExtractorEtl
from platform_export.export_shared.export_model import ExportConfig, ExportStatus


@pytest.fixture(scope="function")
def extractor_etl(
    export_config: ExportConfig,
    platform_repository: Mock,
    raw_data_repository: Mock,
    analytics_bq_repository: Mock,
) -> PlatformExtractorEtl:
    return PlatformExtractorEtl(
        config=export_config,
        platform_repository=platform_repository,
        raw_data_repository=raw_data_repository,
        analytics_bq_repository=analytics_bq_repository,
    )


def test_extract(extractor_etl: PlatformExtractorEtl, analytics_bq_repository: Mock, target_table: str) -> None:
    # arrange
    expected = ExportStatus()
    analytics_bq_repository.get_export_status.return_value = expected

    # act
    actual = extractor_etl.extract()

    # assert
    assert actual == expected
    analytics_bq_repository.get_export_status.assert_called_once_with(table=target_table)


def test_transform_when_empty_input(extractor_etl: PlatformExtractorEtl) -> None:
    empty_df = DataFrame()
    actual = extractor_etl.transform(empty_df)
    assert_frame_equal(actual, empty_df)


def test_transform_when_valid_input(
    extractor_etl: PlatformExtractorEtl, extracted_df: DataFrame, now: datetime
) -> None:
    # arrange
    expected_df = extracted_df.copy()
    expected_df["year"] = now.year
    expected_df["month"] = now.month
    expected_df["day"] = now.day
    expected_df["timestamp"] = int(now.timestamp())

    # act
    actual = extractor_etl.transform(extracted_df)

    # assert
    assert_frame_equal(actual, expected_df)


def test_load_when_empty_input(extractor_etl: PlatformExtractorEtl, raw_data_repository: Mock) -> None:
    actual = extractor_etl.load(DataFrame())

    assert actual is None
    raw_data_repository.write_data_frame.assert_not_called()


def test_load_when_valid_input(
    extractor_etl: PlatformExtractorEtl,
    raw_data_repository: Mock,
    extracted_df: DataFrame,
) -> None:
    # arrange
    expected = "bucket_folder/table_name/file01.parquet"
    raw_data_repository.write_data_frame.return_value = [Path(expected)]

    # act
    actual = extractor_etl.load(extracted_df)

    # assert
    assert actual == expected
    raw_data_repository.write_data_frame.assert_called_once()


@patch.object(PipelineExecutionLogger, "finish_processing_step")
@patch.object(PipelineExecutionLogger, "start_processing_step")
def test_run_when_no_changes(
    mock_start_processing_step: Mock,
    mock_finish_processing_step: Mock,
    extractor_etl: PlatformExtractorEtl,
    analytics_bq_repository: Mock,
    platform_repository: Mock,
    raw_data_repository: Mock,
) -> None:
    # arrange
    ctx_manager = MagicMock()
    ctx_manager.__enter__.return_value = iter(())
    platform_repository.select_changes.return_value = ctx_manager

    # act
    result = extractor_etl.run()

    # assert
    assert result == []
    analytics_bq_repository.get_export_status.assert_called_once()
    platform_repository.select_changes.assert_called_once()
    raw_data_repository.write_data_frame.assert_not_called()
    mock_start_processing_step.assert_called_once()
    mock_finish_processing_step.assert_called_once()


@patch.object(PipelineExecutionLogger, "finish_processing_step")
@patch.object(PipelineExecutionLogger, "start_processing_step")
def test_run_when_changes(
    mock_start_processing_step: Mock,
    mock_finish_processing_step: Mock,
    extractor_etl: PlatformExtractorEtl,
    analytics_bq_repository: Mock,
    platform_repository: Mock,
    raw_data_repository: Mock,
    extracted_df: DataFrame,
) -> None:
    # arrange
    ctx_manager = MagicMock()
    ctx_manager.__enter__.return_value = iter([extracted_df, extracted_df])
    platform_repository.select_changes.return_value = ctx_manager

    expected_paths = ["path1", "path2"]
    raw_data_repository.write_data_frame.side_effect = [[Path(p)] for p in expected_paths]

    # act
    result = extractor_etl.run()

    # assert
    analytics_bq_repository.get_export_status.assert_called_once()
    platform_repository.select_changes.assert_called_once()
    mock_start_processing_step.assert_called_once()
    mock_finish_processing_step.assert_called_once()

    assert result == expected_paths
    assert raw_data_repository.write_data_frame.call_count == len(expected_paths)
