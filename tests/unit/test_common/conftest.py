from logging import LogRecord
from unittest.mock import Mock

import pytest

from common.logger import PasswordMasker


@pytest.fixture(scope="function")
def password_masker() -> PasswordMasker:
    """Create a PasswordMasker instance for testing"""
    return PasswordMasker()


@pytest.fixture(scope="function")
def mock_log_record() -> Mock:
    """Create a mock LogRecord for testing"""
    return Mock(spec=LogRecord)
