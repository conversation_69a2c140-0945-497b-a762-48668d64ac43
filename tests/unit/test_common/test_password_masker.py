"""
Unit tests for PasswordMasker functionality
"""

from logging import LogRecord

import pytest

from common.logger import PasswordMasker


@pytest.mark.parametrize(
    "message, expected",
    [
        # Basic password formats
        ("This is INFO message with password=qwerty123", "This is INFO message with password=*****"),
        ("This is INFO message with password='qwerty123'", "This is INFO message with password='*****'"),
        ('This is INFO message with password="qwerty123"', 'This is INFO message with password="*****"'),
        # Special characters in passwords
        ("This is INFO message with password='@*&&#$%@#/'", "This is INFO message with password='*****'"),
        # JSON-like formats
        (
            "This is INFO message with serialized JSON: {'password': 'foo'}",
            "This is INFO message with serialized JSON: {'password': '*****'}",
        ),
        (
            'This is INFO message with serialized JSON: {"password": "foo"}',
            'This is INFO message with serialized JSON: {"password": "*****"}',
        ),
        (
            "This is INFO message with serialized JSON: {'rootPassword': 'foo', 'bar': 'baz'}",
            "This is INFO message with serialized JSON: {'rootPassword': '*****', 'bar': 'baz'}",
        ),
        # FlywayParams format
        (
            "FlywayParams(host='localhost', password='postgres', url='*****************************************')",
            "FlywayParams(host='localhost', password='*****', url='*****************************************')",
        ),
        # Multiple passwords
        (
            "Multiple passwords: password=first pwd='second' pass=\"third\"",
            "Multiple passwords: password=***** pwd='*****' pass=\"*****\"",
        ),
        # Edge cases
        ("password=''", "password='*****'"),
        ("password='my secret password'", "password='*****'"),
        ("password='secret,with,commas'", "password='*****'"),
        # No password - should remain unchanged
        ("This is a normal log message without sensitive data", "This is a normal log message without sensitive data"),
        # Mixed quotes
        (
            """Config: {"password": 'secret123', "host": "localhost"}""",
            """Config: {"password": '*****', "host": "localhost"}""",
        ),
    ],
)
def test_mask_password(password_masker: PasswordMasker, message: str, expected: str) -> None:
    """Test password masking for various message formats"""
    result = password_masker.mask_password(message)
    assert result == expected


@pytest.mark.parametrize(
    "message, expected",
    [
        ("PASSWORD='secret'", "PASSWORD='*****'"),
        ("Password='secret'", "Password='*****'"),
        ("PWD='secret'", "PWD='*****'"),
        ("Pass='secret'", "Pass='*****'"),
    ],
)
def test_mask_password_case_insensitive(password_masker: PasswordMasker, message: str, expected: str) -> None:
    """Test that password masking is case insensitive"""
    result = password_masker.mask_password(message)
    assert result == expected


@pytest.mark.parametrize("password_key", ["pwd", "pass", "password"])
def test_all_password_keys(password_masker: PasswordMasker, password_key: str) -> None:
    """Test that all password keys are masked"""
    message = f"Config: {password_key}='secret123'"
    result = password_masker.mask_password(message)
    expected = f"Config: {password_key}='*****'"
    assert result == expected


@pytest.mark.parametrize(
    "data, expected",
    [
        # Direct password key
        (
            {"username": "admin", "password": "secret123", "host": "localhost"},
            {"username": "admin", "password": "*****", "host": "localhost"},
        ),
        # Compound password key
        (
            {"username": "admin", "rootPassword": "secret123", "host": "localhost"},
            {"username": "admin", "rootPassword": "*****", "host": "localhost"},
        ),
        # Nested dictionary
        (
            {"database": {"host": "localhost", "password": "secret123"}, "cache": {"redis_password": "redis_secret"}},
            {"database": {"host": "localhost", "password": "*****"}, "cache": {"redis_password": "*****"}},
        ),
    ],
)
def test_mask_password_in_dict(password_masker: PasswordMasker, data: dict, expected: dict) -> None:
    """Test masking passwords in dictionary objects"""
    # Make a deep copy to avoid modifying the original test data
    import copy

    test_data = copy.deepcopy(data)

    password_masker.mask_password_in_dict(test_data)
    assert test_data == expected


def test_filter_method_with_string_message(password_masker: PasswordMasker) -> None:
    """Test filter method with string message"""
    record = LogRecord(name="test", level=20, pathname="", lineno=0, msg="password='secret123'", args=(), exc_info=None)

    result = password_masker.filter(record)
    assert result is True
    assert record.msg == "password='*****'"


def test_filter_method_with_dict_message(password_masker: PasswordMasker) -> None:
    """Test filter method with dictionary message"""
    record = LogRecord(
        name="test",
        level=20,
        pathname="",
        lineno=0,
        msg={"username": "admin", "password": "secret123"},
        args=(),
        exc_info=None,
    )

    result = password_masker.filter(record)
    assert result is True
    assert record.msg == {"username": "admin", "password": "*****"}


def test_complex_real_world_example(password_masker: PasswordMasker) -> None:
    """Test complex real-world log message"""
    message = (
        "FlywayParams(host='localhost', port=5433, database='platform', "
        "config_files='/path/to/flyway.toml', locations='filesystem:/path/to/sql', "
        "environment='local', user='postgres', password='postgres', "
        "url='*****************************************', placeholders=['runTest=false']"
    )
    result = password_masker.mask_password(message)

    # Verify password is masked but other parts remain unchanged
    assert "password='*****'" in result
    assert "password='postgres'" not in result
    assert "host='localhost'" in result
    assert "user='postgres'" in result
