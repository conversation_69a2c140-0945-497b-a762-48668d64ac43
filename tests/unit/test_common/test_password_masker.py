"""
Unit tests for PasswordMasker functionality
"""

from logging import LogRecord

import pytest

from common.logger import PasswordMasker


class TestPasswordMasker:
    """Test cases for PasswordMasker class"""

    def setup_method(self):
        """Set up test fixtures"""
        self.masker = PasswordMasker()

    def test_mask_password_with_equals_and_quotes(self):
        """Test masking password with equals sign and single quotes"""
        message = "This is INFO message with password='qwerty123'"
        result = self.masker.mask_password(message)
        expected = "This is INFO message with password='*****'"
        assert result == expected

    def test_mask_password_with_equals_and_double_quotes(self):
        """Test masking password with equals sign and double quotes"""
        message = 'This is INFO message with password="qwerty123"'
        result = self.masker.mask_password(message)
        expected = 'This is INFO message with password="*****"'
        assert result == expected

    def test_mask_password_with_equals_no_quotes(self):
        """Test masking password with equals sign and no quotes"""
        message = "This is INFO message with password=qwerty123"
        result = self.masker.mask_password(message)
        expected = "This is INFO message with password=*****"
        assert result == expected

    def test_mask_password_with_special_characters(self):
        """Test masking password containing special characters"""
        message = "This is INFO message with password='@*&&#$%@#/'"
        result = self.masker.mask_password(message)
        expected = "This is INFO message with password='*****'"
        assert result == expected

    def test_mask_password_in_json_format(self):
        """Test masking password in JSON-like format"""
        message = "This is INFO message with serialized JSON: {'password': 'foo'}"
        result = self.masker.mask_password(message)
        expected = "This is INFO message with serialized JSON: {'password': '*****'}"
        assert result == expected

    def test_mask_password_in_json_format_double_quotes(self):
        """Test masking password in JSON-like format with double quotes"""
        message = 'This is INFO message with serialized JSON: {"password": "foo"}'
        result = self.masker.mask_password(message)
        expected = 'This is INFO message with serialized JSON: {"password": "*****"}'
        assert result == expected

    def test_mask_compound_password_key(self):
        """Test masking password with compound key like rootPassword"""
        message = "This is INFO message with serialized JSON: {'rootPassword': 'foo', 'bar': 'baz'}"
        result = self.masker.mask_password(message)
        expected = "This is INFO message with serialized JSON: {'rootPassword': '*****', 'bar': 'baz'}"
        assert result == expected

    def test_mask_flyway_params(self):
        """Test masking password in FlywayParams format"""
        message = "FlywayParams(host='localhost', password='postgres', url='*****************************************')"
        result = self.masker.mask_password(message)
        expected = "FlywayParams(host='localhost', password='*****', url='*****************************************')"
        assert result == expected

    def test_mask_multiple_passwords(self):
        """Test masking multiple passwords in one message"""
        message = "Multiple passwords: password=first pwd='second' pass=\"third\""
        result = self.masker.mask_password(message)
        expected = "Multiple passwords: password=***** pwd='*****' pass=\"*****\""
        assert result == expected

    def test_mask_case_insensitive(self):
        """Test that password masking is case insensitive"""
        test_cases = [
            ("PASSWORD='secret'", "PASSWORD='*****'"),
            ("Password='secret'", "Password='*****'"),
            ("PWD='secret'", "PWD='*****'"),
            ("Pass='secret'", "Pass='*****'"),
        ]

        for message, expected in test_cases:
            result = self.masker.mask_password(message)
            assert result == expected

    def test_mask_empty_password(self):
        """Test masking empty password"""
        message = "password=''"
        result = self.masker.mask_password(message)
        expected = "password='*****'"
        assert result == expected

    def test_mask_password_with_spaces(self):
        """Test masking password containing spaces"""
        message = "password='my secret password'"
        result = self.masker.mask_password(message)
        expected = "password='*****'"
        assert result == expected

    def test_mask_password_with_comma(self):
        """Test masking password containing comma"""
        message = "password='secret,with,commas'"
        result = self.masker.mask_password(message)
        expected = "password='*****'"
        assert result == expected

    def test_no_masking_when_no_password(self):
        """Test that messages without passwords are not modified"""
        message = "This is a normal log message without sensitive data"
        result = self.masker.mask_password(message)
        assert result == message

    def test_mask_password_in_dict_direct_key(self):
        """Test masking password in dictionary with direct password key"""
        data = {"username": "admin", "password": "secret123", "host": "localhost"}
        self.masker.mask_password_in_dict(data)
        expected = {"username": "admin", "password": "*****", "host": "localhost"}
        assert data == expected

    def test_mask_password_in_dict_compound_key(self):
        """Test masking password in dictionary with compound key containing 'password'"""
        data = {"username": "admin", "rootPassword": "secret123", "host": "localhost"}
        self.masker.mask_password_in_dict(data)
        expected = {"username": "admin", "rootPassword": "*****", "host": "localhost"}
        assert data == expected

    def test_mask_password_in_nested_dict(self):
        """Test masking password in nested dictionary"""
        data = {"database": {"host": "localhost", "password": "secret123"}, "cache": {"redis_password": "redis_secret"}}
        self.masker.mask_password_in_dict(data)
        expected = {"database": {"host": "localhost", "password": "*****"}, "cache": {"redis_password": "*****"}}
        assert data == expected

    def test_filter_method_with_string_message(self):
        """Test filter method with string message"""
        record = LogRecord(
            name="test", level=20, pathname="", lineno=0, msg="password='secret123'", args=(), exc_info=None
        )

        result = self.masker.filter(record)
        assert result is True
        assert record.msg == "password='*****'"

    def test_filter_method_with_dict_message(self):
        """Test filter method with dictionary message"""
        record = LogRecord(
            name="test",
            level=20,
            pathname="",
            lineno=0,
            msg={"username": "admin", "password": "secret123"},
            args=(),
            exc_info=None,
        )

        result = self.masker.filter(record)
        assert result is True
        assert record.msg == {"username": "admin", "password": "*****"}

    @pytest.mark.parametrize("password_key", ["pwd", "pass", "password"])
    def test_all_password_keys(self, password_key):
        """Test that all password keys are masked"""
        message = f"Config: {password_key}='secret123'"
        result = self.masker.mask_password(message)
        expected = f"Config: {password_key}='*****'"
        assert result == expected

    def test_json_with_mixed_quotes(self):
        """Test JSON with mixed quote styles"""
        message = """Config: {"password": 'secret123', "host": "localhost"}"""
        result = self.masker.mask_password(message)
        expected = """Config: {"password": '*****', "host": "localhost"}"""
        assert result == expected

    def test_complex_real_world_example(self):
        """Test complex real-world log message"""
        message = (
            "FlywayParams(host='localhost', port=5433, database='platform', "
            "config_files='/path/to/flyway.toml', locations='filesystem:/path/to/sql', "
            "environment='local', user='postgres', password='postgres', "
            "url='*****************************************', placeholders=['runTest=false']"
        )
        result = self.masker.mask_password(message)

        # Verify password is masked but other parts remain unchanged
        assert "password='*****'" in result
        assert "password='postgres'" not in result
        assert "host='localhost'" in result
        assert "user='postgres'" in result
