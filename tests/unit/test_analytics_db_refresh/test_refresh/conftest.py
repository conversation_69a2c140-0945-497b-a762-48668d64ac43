from datetime import datetime, timedelta
from unittest.mock import Mock

import pytest
from conftest import NOW
from refresh_shared.dump_db import DumpDb
from refresh_shared.dump_restore import PgParams
from refresh_shared.migrate import FlywayParams
from refresh_shared.model import DbRefreshConfig
from refresh_shared.restore_db import RestoreDb
from refresh_shared.restore_instance import (
    RestoreCloudSQLInstance,
    RestoreCloudSQLInstanceConfig,
)
from refresh_shared.source_pg_repo import SourcePgRepository
from refresh_shared.target_pg_repo import TargetPgRepository

from common.cloud_sql_instance_client.instance_client import CloudSQLInstanceClient
from common.cloud_sql_instance_client.instance_model import (
    BackupConfiguration,
    CloudSQLCreateInstanceParams,
    CloudSQLInstance,
    InstanceCreateSettings,
    InstanceSettings,
    IpConfiguration,
)
from common.config import MattermostAlertingConfig
from common.sql_client import DatabaseConfig
from common.time_service import TimeService

BACKUP_START_TIME = NOW + timedelta(hours=1)


@pytest.fixture(scope="session")
def restore_start_time(now: datetime) -> datetime:
    return BACKUP_START_TIME + timedelta(hours=2)


@pytest.fixture(scope="function")
def cloud_sql_instance_client_mock() -> Mock:
    return Mock(spec=CloudSQLInstanceClient)


@pytest.fixture(scope="session")
def new_cloud_sql_instance() -> CloudSQLInstance:
    return CloudSQLInstance(
        name="test-instance",
        connection_name="test-connection",
        database_version="test",
        settings=InstanceSettings(activation_policy="ALWAYS"),
    )


@pytest.fixture(scope="session")
def new_cloud_sql_instance_params() -> CloudSQLCreateInstanceParams:
    return CloudSQLCreateInstanceParams(
        name="test-instance",
        root_password="Test-123@",
        settings=InstanceCreateSettings(
            backup_configuration=BackupConfiguration(),
            ip_configuration=IpConfiguration(),
        ),
    )


@pytest.fixture(scope="session")
def restore_instance_config(
    new_cloud_sql_instance_params: CloudSQLCreateInstanceParams,
) -> RestoreCloudSQLInstanceConfig:
    return RestoreCloudSQLInstanceConfig(
        new_instance_params=new_cloud_sql_instance_params,
        backup_instance_name="test",
        backup_project_name="test",
        wait_for_backup_in_seconds=1,
        timeout_in_seconds=2,
    )


@pytest.fixture(scope="function")
def restore_instance(
    cloud_sql_instance_client_mock: Mock,
    restore_instance_config: RestoreCloudSQLInstanceConfig,
    restore_start_time: datetime,
) -> RestoreCloudSQLInstance:
    return RestoreCloudSQLInstance(
        cloud_sql_instance_client_mock, restore_instance_config, TimeService(restore_start_time)
    )


@pytest.fixture(scope="function")
def source_pg_repository_mock() -> Mock:
    return Mock(spec=SourcePgRepository)


@pytest.fixture(scope="function")
def target_pg_repository_mock() -> Mock:
    return Mock(spec=TargetPgRepository)


@pytest.fixture(scope="function")
def dumb_db_mock() -> Mock:
    return Mock(spec=DumpDb)


@pytest.fixture(scope="function")
def restore_db_mock() -> Mock:
    return Mock(spec=RestoreDb)


@pytest.fixture(scope="session")
def db_refresh_config() -> DbRefreshConfig:
    return DbRefreshConfig(
        source_db_config=DatabaseConfig(),
        target_db_config=DatabaseConfig(),
        alerting_config=MattermostAlertingConfig(webhook_url="test"),
        pg_params=PgParams(host="test", schema=["a", "b", "c", "public"]),
        flyway_params=FlywayParams(
            host="test",
            port=123,
            database="foo",
            config_files="/test/config",
            locations="sql/locations/",
            environment="test",
            user="bar",
            password="baz",
        ),
        materialized_views_to_refresh=["public.foo", "public.bar", "public.baz"],
        temp_schema_suffix="_test",
        backup_schema_suffix="_backup",
    )
