import pytest
from refresh_shared.dump_restore import PgParams
from refresh_shared.migrate import FlywayParams
from refresh_shared.model import DbRefreshConfig


@pytest.mark.parametrize(
    "pg_params, command_params",
    [
        [
            PgParams(host="test-case-01"),
            [
                "--format",
                "directory",
                "--jobs",
                8,
                "--schema",
                "public",
                "--schema",
                "zendesk",
                "--clean",
                "--if-exists",
                "--no-privileges",
                "--no-owner",
                "--verbose",
            ],
        ],
        [
            PgParams(
                host="test-case-02",
                jobs=1,
                schema=["foo"],
                clean=False,
                if_exists=False,
                no_privileges=False,
                no_owner=False,
                verbose=False,
            ),
            ["--format", "directory", "--jobs", 1, "--schema", "foo"],
        ],
    ],
)
def test_pg_params(pg_params: PgParams, command_params: list[str]) -> None:
    # act & assert
    assert pg_params.command_params == command_params


@pytest.mark.parametrize(
    "flyway_params, command_params",
    [
        [
            FlywayParams(
                host="test-case-01",
                port=123,
                database="foo",
                config_files="/test/config",
                locations="sql/locations/",
                environment="test",
                user="bar",
                password="baz",
            ),
            "-configFiles=/test/config -locations=sql/locations/ -environment=test -user=bar '-password=baz' "
            "-url=**************************************",
        ],
        [
            FlywayParams(
                host="test-case-02",
                port=123,
                database="foo",
                config_files="/test/config",
                locations="sql/locations/",
                environment="test",
                user="bar",
                password="baz",
                placeholders=["foo=bar", "baz=bar", "baz=foo"],
            ),
            "-configFiles=/test/config -locations=sql/locations/ -environment=test -user=bar '-password=baz' "
            "-url=************************************** "
            "-placeholders.foo=bar -placeholders.baz=bar -placeholders.baz=foo",
        ],
    ],
)
def test_flyway_params(flyway_params: FlywayParams, command_params: str) -> None:
    # act & assert
    assert flyway_params.command_params == command_params


def test_config(db_refresh_config: DbRefreshConfig) -> None:
    assert db_refresh_config.temp_schemas == ["a_test", "b_test", "c_test", "public_test"]
    assert db_refresh_config.backed_up_schemas == ["a_backup", "b_backup", "c_backup", "public_backup"]
    assert db_refresh_config.temp_materialized_views_to_refresh == [
        "public_test.foo",
        "public_test.bar",
        "public_test.baz",
    ]
