from unittest.mock import Mock

import pytest
from refresh_shared.model import DbRefreshConfig
from refresh_shared.restore_instance import RestoreCloudSQLInstance

from common.cloud_sql_instance_client.instance_model import (
    Backup,
    CloudSQLInstance,
    IpAddress,
)
from common.config import Config
from common.sql_repository import SqlRepository


@pytest.fixture(scope="session")
def cloud_sql_instance_with_ip() -> CloudSQLInstance:
    return CloudSQLInstance(
        name="test-instance",
        connection_name="test-connection",
        database_version="POSTGRES_15",
        settings=Mock(),
        ip_addresses=[
            IpAddress(ip_address="********", type="PRIVATE"),
            IpAddress(ip_address="************", type="PRIMARY"),
        ],
    )


@pytest.fixture(scope="session")
def cloud_sql_instance_without_ip() -> CloudSQLInstance:
    return CloudSQLInstance(
        name="test-instance-no-ip",
        connection_name="test-connection",
        database_version="POSTGRES_15",
        settings=Mock(),
        ip_addresses=[IpAddress(ip_address="************", type="PRIMARY")],
    )


@pytest.fixture(scope="session")
def mock_backup() -> Backup:
    return Mock(spec=Backup, id="backup-123")


@pytest.fixture(scope="session")
def env_config_local() -> Config:
    config = Mock(spec=Config)
    config.is_local = True
    config.is_staging = False
    config.is_production = False

    return config


@pytest.fixture(scope="session")
def env_config_staging() -> Config:
    config = Mock(spec=Config)
    config.is_local = False
    config.is_staging = True
    config.is_production = False

    return config


@pytest.fixture(scope="session")
def env_config_production() -> Config:
    config = Mock(spec=Config)
    config.is_local = False
    config.is_staging = False
    config.is_production = True

    return config


@pytest.fixture(scope="session")
def mock_local_config(db_refresh_config: DbRefreshConfig) -> Mock:
    config = Mock()
    config.refresh_db_config = db_refresh_config
    config.source_restore_instance = Mock(spec=RestoreCloudSQLInstance)
    config.target_restore_instance = Mock(spec=RestoreCloudSQLInstance)
    config.source_db_config = Mock()
    config.source_db_config.instance_name = "local-source"
    config.target_db_config = Mock()
    config.target_db_config.instance_name = "local-target"
    config.wait_for_today_backup = False

    return config


@pytest.fixture(scope="function")
def mock_cloud_config(db_refresh_config: DbRefreshConfig) -> Mock:
    config = Mock()
    config.refresh_db_config = db_refresh_config
    config.source_restore_instance = Mock(spec=RestoreCloudSQLInstance)
    config.target_restore_instance = Mock(spec=RestoreCloudSQLInstance)
    config.source_db_config = Mock()
    config.source_db_config.instance_name = "cloud-source"
    config.target_db_config = Mock()
    config.target_db_config.instance_name = "cloud-target"
    config.wait_for_today_backup = True

    return config


@pytest.fixture(scope="function")
def mock_cicd_config(db_refresh_config: DbRefreshConfig) -> Mock:
    config = Mock()
    config.refresh_db_config = db_refresh_config
    config.source_restore_instance = Mock(spec=RestoreCloudSQLInstance)
    config.target_restore_instance = Mock(spec=RestoreCloudSQLInstance)
    config.source_db_config = Mock()
    config.source_db_config.instance_name = "cicd-source"
    config.target_db_config = Mock()
    config.target_db_config.instance_name = "cicd-target"
    config.wait_for_today_backup = True

    return config


@pytest.fixture(scope="session")
def mock_sql_repository() -> Mock:
    mock_repo = Mock(spec=SqlRepository)
    mock_repo.select_from_function.return_value = [(1,)]  # Return a tuple with execution_id
    return mock_repo
