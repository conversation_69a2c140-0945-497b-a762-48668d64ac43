from datetime import datetime
from unittest.mock import Mock

import pytest
from provider import In<PERSON><PERSON><PERSON>ider
from refresh_shared.model import DbRefreshConfig
from refresh_shared.restore_instance import RestoreCloudSQLInstance

from common.cloud_sql_instance_client.instance_model import (
    Backup,
    CloudSQLInstance,
    IpAddress,
)
from common.config import Config
from common.sql_repository import SqlRepository
from common.time_service import TimeService


@pytest.fixture
def cloud_sql_instance_with_ip() -> CloudSQLInstance:
    """Provides CloudSQLInstance with private IP address."""
    return CloudSQLInstance(
        name="test-instance",
        connection_name="test-connection",
        database_version="POSTGRES_15",
        settings=Mock(),
        ip_addresses=[
            IpAddress(ip_address="********", type="PRIVATE"),
            IpAddress(ip_address="************", type="PRIMARY"),
        ],
    )


@pytest.fixture
def cloud_sql_instance_without_ip() -> CloudSQLInstance:
    """Provides CloudSQLInstance without private IP address."""
    return CloudSQLInstance(
        name="test-instance-no-ip",
        connection_name="test-connection",
        database_version="POSTGRES_15",
        settings=Mock(),
        ip_addresses=[IpAddress(ip_address="************", type="PRIMARY")],
    )


@pytest.fixture
def mock_backup() -> Backup:
    """Provides mock backup object."""
    return Mock(spec=Backup, id="backup-123")


@pytest.fixture
def env_config_local() -> Config:
    """Provides local environment configuration."""
    config = Mock(spec=Config)
    config.is_local = True
    config.is_staging = False
    config.is_production = False
    return config


@pytest.fixture
def env_config_staging() -> Config:
    """Provides staging environment configuration."""
    config = Mock(spec=Config)
    config.is_local = False
    config.is_staging = True
    config.is_production = False
    return config


@pytest.fixture
def env_config_production() -> Config:
    """Provides production environment configuration."""
    config = Mock(spec=Config)
    config.is_local = False
    config.is_staging = False
    config.is_production = True
    return config


@pytest.fixture
def mock_local_config(db_refresh_config: DbRefreshConfig) -> Mock:
    """Provides mock local refresh configuration."""
    config = Mock()
    config.refresh_db_config = db_refresh_config
    config.source_restore_instance = Mock(spec=RestoreCloudSQLInstance)
    config.target_restore_instance = Mock(spec=RestoreCloudSQLInstance)
    config.source_db_config = Mock()
    config.source_db_config.instance_name = "local-source"
    config.target_db_config = Mock()
    config.target_db_config.instance_name = "local-target"
    config.wait_for_today_backup = True
    return config


@pytest.fixture
def mock_cloud_config(db_refresh_config: DbRefreshConfig) -> Mock:
    """Provides mock cloud refresh configuration."""
    config = Mock()
    config.refresh_db_config = db_refresh_config
    config.source_restore_instance = Mock(spec=RestoreCloudSQLInstance)
    config.target_restore_instance = Mock(spec=RestoreCloudSQLInstance)
    config.source_db_config = Mock()
    config.source_db_config.instance_name = "cloud-source"
    config.target_db_config = Mock()
    config.target_db_config.instance_name = "cloud-target"
    config.wait_for_today_backup = True
    return config


@pytest.fixture
def mock_cicd_config(db_refresh_config: DbRefreshConfig) -> Mock:
    """Provides mock CICD refresh configuration."""
    config = Mock()
    config.refresh_db_config = db_refresh_config
    config.source_restore_instance = Mock(spec=RestoreCloudSQLInstance)
    config.target_restore_instance = Mock(spec=RestoreCloudSQLInstance)
    config.source_db_config = Mock()
    config.source_db_config.instance_name = "cicd-source"
    config.target_db_config = Mock()
    config.target_db_config.instance_name = "cicd-target"
    config.wait_for_today_backup = True
    return config


@pytest.fixture
def mock_time_service() -> Mock:
    """Provides mock time service."""
    mock_service = Mock(spec=TimeService)
    # Mock the now property to return a real datetime object
    mock_service.now = datetime(2024, 1, 15, 10, 30, 45, 123456)
    return mock_service


@pytest.fixture
def mock_sql_repository() -> Mock:
    """Provides mock SQL repository."""
    return Mock(spec=SqlRepository)


def create_instance_provider(
    env_config: Config,
    local_config: Mock,
    cloud_config: Mock,
    cicd_config: Mock,
    time_service: Mock,
    sql_repository: Mock,
) -> InstanceProvider:
    """Helper method to create InstanceProvider instance."""
    return InstanceProvider(
        env_config=env_config,
        local_config=local_config,
        cloud_config=cloud_config,
        cicd_config=cicd_config,
        time_service=time_service,
        sql_repository=sql_repository,
    )
