"""
Unit tests for InstanceProvider class.

Tests cover all public methods of the InstanceProvider class following the project's testing patterns
and AI guidelines. Tests use mocks to isolate the class under test and verify behavior.
"""

from typing import Optional
from unittest.mock import Mock, patch

import pytest
from _pytest.mark import param
from provider import InstanceProvider, ProviderConfig, ProviderError
from refresh_shared.model import DbRefreshConfig
from unit.test_analytics_db_refresh.test_instance_provider.conftest import (
    create_instance_provider,
)

from common.cloud_sql_instance_client.instance_model import (
    Backup,
    CloudSQLInstance,
    IpAddress,
)
from common.config import Config


class TestInstanceProvider:
    """Test class for InstanceProvider following OOP patterns."""

    @pytest.mark.parametrize(
        "env_name,is_staging,cicd_env_var,expected",
        [
            param("local", False, None, False, id="local_environment"),
            param("staging", True, "True", True, id="staging_with_cicd"),
            param("staging", True, "False", False, id="staging_without_cicd"),
            param("staging", True, None, False, id="staging_no_cicd_var"),
            param("production", False, "True", False, id="production_with_cicd"),
        ],
    )
    @patch.dict("os.environ", {}, clear=True)
    def test_is_cicd_run(
        self,
        env_name: str,
        is_staging: bool,
        cicd_env_var: Optional[str],
        expected: bool,
        mock_local_config: Mock,
        mock_cloud_config: Mock,
        mock_cicd_config: Mock,
        mock_time_service: Mock,
        mock_sql_repository: Mock,
    ) -> None:
        """
        Test is_cicd_run property returns correct value based on environment and CICD variable.

        :param env_name: Environment name for test identification
        :param is_staging: Whether the environment is staging
        :param cicd_env_var: Value of CICD environment variable
        :param expected: Expected result of is_cicd_run property
        """
        # Arrange
        env_config = Mock(spec=Config)
        env_config.is_staging = is_staging

        if cicd_env_var is not None:
            with patch.dict("os.environ", {"CICD": cicd_env_var}):
                provider = create_instance_provider(
                    env_config,
                    mock_local_config,
                    mock_cloud_config,
                    mock_cicd_config,
                    mock_time_service,
                    mock_sql_repository,
                )
                # Act & Assert
                assert provider.is_cicd_run == expected
        else:
            provider = create_instance_provider(
                env_config,
                mock_local_config,
                mock_cloud_config,
                mock_cicd_config,
                mock_time_service,
                mock_sql_repository,
            )
            # Act & Assert
            assert provider.is_cicd_run == expected

    @patch("common.pipeline_logging.pipeline_logger.PipelineExecutionLogger")
    def test_create_local_environment(
        self,
        mock_pipeline_logger: Mock,
        env_config_local: Config,
        mock_local_config: Mock,
        mock_cloud_config: Mock,
        mock_cicd_config: Mock,
        mock_time_service: Mock,
        mock_sql_repository: Mock,
        db_refresh_config: DbRefreshConfig,
    ) -> None:
        """Test create method in local environment returns None instances."""
        # Arrange
        # Mock the pipeline logger to avoid database interactions
        mock_logger_instance = Mock()
        mock_pipeline_logger.return_value = mock_logger_instance

        provider = create_instance_provider(
            env_config_local,
            mock_local_config,
            mock_cloud_config,
            mock_cicd_config,
            mock_time_service,
            mock_sql_repository,
        )

        # Act
        result = provider.create()

        # Assert
        assert isinstance(result, ProviderConfig)
        assert result.source_instance is None
        assert result.target_instance is None
        assert result.refresh_config == db_refresh_config
        assert result.restore_source_instance == mock_local_config.source_restore_instance

    @patch("common.pipeline_logging.pipeline_logger.PipelineExecutionLogger")
    def test_create_production_environment(
        self,
        mock_pipeline_logger: Mock,
        env_config_production: Config,
        mock_local_config: Mock,
        mock_cloud_config: Mock,
        mock_cicd_config: Mock,
        mock_time_service: Mock,
        mock_sql_repository: Mock,
        cloud_sql_instance_with_ip: CloudSQLInstance,
        mock_backup: Backup,
    ) -> None:
        """Test create method in production environment creates source and describes target."""
        # Arrange
        mock_logger_instance = Mock()
        mock_pipeline_logger.return_value = mock_logger_instance

        mock_cloud_config.source_restore_instance.recreate_instance.return_value = cloud_sql_instance_with_ip
        mock_cloud_config.source_restore_instance.restore_backup.return_value = mock_backup
        mock_cloud_config.target_restore_instance.describe_instance.return_value = cloud_sql_instance_with_ip

        provider = create_instance_provider(
            env_config_production,
            mock_local_config,
            mock_cloud_config,
            mock_cicd_config,
            mock_time_service,
            mock_sql_repository,
        )

        # Act
        result = provider.create()

        # Assert
        assert isinstance(result, ProviderConfig)
        assert result.source_instance == cloud_sql_instance_with_ip
        assert result.target_instance == cloud_sql_instance_with_ip
        mock_cloud_config.source_restore_instance.recreate_instance.assert_called_once()
        mock_cloud_config.source_restore_instance.restore_backup.assert_called_once()
        mock_cloud_config.target_restore_instance.describe_instance.assert_called_once()

    @patch.dict("os.environ", {"CICD": "True"}, clear=False)
    @patch("common.pipeline_logging.pipeline_logger.PipelineExecutionLogger")
    def test_create_cicd_environment(
        self,
        mock_pipeline_logger: Mock,
        env_config_staging: Config,
        mock_local_config: Mock,
        mock_cloud_config: Mock,
        mock_cicd_config: Mock,
        mock_time_service: Mock,
        mock_sql_repository: Mock,
        cloud_sql_instance_with_ip: CloudSQLInstance,
        mock_backup: Backup,
    ) -> None:
        """Test create method in CICD environment creates source and describes target."""
        # Arrange
        mock_logger_instance = Mock()
        mock_pipeline_logger.return_value = mock_logger_instance

        mock_cicd_config.source_restore_instance.recreate_instance.return_value = cloud_sql_instance_with_ip
        mock_cicd_config.source_restore_instance.restore_backup.return_value = mock_backup
        mock_cicd_config.target_restore_instance.describe_instance.return_value = cloud_sql_instance_with_ip

        provider = create_instance_provider(
            env_config_staging,
            mock_local_config,
            mock_cloud_config,
            mock_cicd_config,
            mock_time_service,
            mock_sql_repository,
        )

        # Act
        result = provider.create()

        # Assert
        assert isinstance(result, ProviderConfig)
        assert result.source_instance == cloud_sql_instance_with_ip
        assert result.target_instance == cloud_sql_instance_with_ip
        mock_cicd_config.source_restore_instance.recreate_instance.assert_called_once()
        mock_cicd_config.source_restore_instance.restore_backup.assert_called_once()
        mock_cicd_config.target_restore_instance.describe_instance.assert_called_once()

    @patch("common.pipeline_logging.pipeline_logger.PipelineExecutionLogger")
    def test_create_staging_environment(
        self,
        mock_pipeline_logger: Mock,
        env_config_staging: Config,
        mock_local_config: Mock,
        mock_cloud_config: Mock,
        mock_cicd_config: Mock,
        mock_time_service: Mock,
        mock_sql_repository: Mock,
        cloud_sql_instance_with_ip: CloudSQLInstance,
        mock_backup: Backup,
    ) -> None:
        """Test create method in staging environment creates both instances and restores target backup."""
        # Arrange
        mock_logger_instance = Mock()
        mock_pipeline_logger.return_value = mock_logger_instance
        mock_cloud_config.source_restore_instance.recreate_instance.return_value = cloud_sql_instance_with_ip
        mock_cloud_config.source_restore_instance.restore_backup.return_value = mock_backup
        mock_cloud_config.target_restore_instance.recreate_instance.return_value = cloud_sql_instance_with_ip
        mock_cloud_config.target_restore_instance.restore_backup.return_value = mock_backup

        provider = create_instance_provider(
            env_config_staging,
            mock_local_config,
            mock_cloud_config,
            mock_cicd_config,
            mock_time_service,
            mock_sql_repository,
        )

        # Act
        result = provider.create()

        # Assert
        assert isinstance(result, ProviderConfig)
        assert result.source_instance == cloud_sql_instance_with_ip
        assert result.target_instance == cloud_sql_instance_with_ip
        mock_cloud_config.source_restore_instance.recreate_instance.assert_called_once()
        mock_cloud_config.source_restore_instance.restore_backup.assert_called_once()
        mock_cloud_config.target_restore_instance.recreate_instance.assert_called_once()
        mock_cloud_config.target_restore_instance.restore_backup.assert_called_once()

    @patch("common.pipeline_logging.pipeline_logger.PipelineExecutionLogger")
    def test_create_source_instance_local_returns_none(
        self,
        mock_pipeline_logger: Mock,
        env_config_local: Config,
        mock_local_config: Mock,
        mock_cloud_config: Mock,
        mock_cicd_config: Mock,
        mock_time_service: Mock,
        mock_sql_repository: Mock,
    ) -> None:
        """Test create_source_instance returns None in local environment."""
        # Arrange
        mock_logger_instance = Mock()
        mock_pipeline_logger.return_value = mock_logger_instance
        provider = create_instance_provider(
            env_config_local,
            mock_local_config,
            mock_cloud_config,
            mock_cicd_config,
            mock_time_service,
            mock_sql_repository,
        )

        # Act
        result = provider.create_source_instance()

        # Assert
        assert result is None

    @patch("common.pipeline_logging.pipeline_logger.PipelineExecutionLogger")
    def test_create_source_instance_cloud_creates_instance(
        self,
        mock_pipeline_logger: Mock,
        env_config_production: Config,
        mock_local_config: Mock,
        mock_cloud_config: Mock,
        mock_cicd_config: Mock,
        mock_time_service: Mock,
        mock_sql_repository: Mock,
        cloud_sql_instance_with_ip: CloudSQLInstance,
    ) -> None:
        """Test create_source_instance creates instance in cloud environment."""
        # Arrange
        mock_logger_instance = Mock()
        mock_pipeline_logger.return_value = mock_logger_instance
        mock_cloud_config.source_restore_instance.recreate_instance.return_value = cloud_sql_instance_with_ip

        provider = create_instance_provider(
            env_config_production,
            mock_local_config,
            mock_cloud_config,
            mock_cicd_config,
            mock_time_service,
            mock_sql_repository,
        )

        # Act
        result = provider.create_source_instance()

        # Assert
        assert result == cloud_sql_instance_with_ip
        mock_cloud_config.source_restore_instance.recreate_instance.assert_called_once()

    @patch("common.pipeline_logging.pipeline_logger.PipelineExecutionLogger")
    def test_restore_source_backup_local_returns_none(
        self,
        mock_pipeline_logger: Mock,
        env_config_local: Config,
        mock_local_config: Mock,
        mock_cloud_config: Mock,
        mock_cicd_config: Mock,
        mock_time_service: Mock,
        mock_sql_repository: Mock,
        cloud_sql_instance_with_ip: CloudSQLInstance,
    ) -> None:
        """Test restore_source_backup returns None in local environment."""
        # Arrange
        mock_logger_instance = Mock()
        mock_pipeline_logger.return_value = mock_logger_instance
        provider = create_instance_provider(
            env_config_local,
            mock_local_config,
            mock_cloud_config,
            mock_cicd_config,
            mock_time_service,
            mock_sql_repository,
        )

        # Act
        result = provider.restore_source_backup(cloud_sql_instance_with_ip)

        # Assert
        assert result is None

    @patch("common.pipeline_logging.pipeline_logger.PipelineExecutionLogger")
    def test_restore_source_backup_cloud_restores_backup(
        self,
        mock_pipeline_logger: Mock,
        env_config_production: Config,
        mock_local_config: Mock,
        mock_cloud_config: Mock,
        mock_cicd_config: Mock,
        mock_time_service: Mock,
        mock_sql_repository: Mock,
        cloud_sql_instance_with_ip: CloudSQLInstance,
        mock_backup: Backup,
    ) -> None:
        """Test restore_source_backup restores backup in cloud environment."""
        # Arrange
        mock_logger_instance = Mock()
        mock_pipeline_logger.return_value = mock_logger_instance
        mock_cloud_config.source_restore_instance.restore_backup.return_value = mock_backup

        provider = create_instance_provider(
            env_config_production,
            mock_local_config,
            mock_cloud_config,
            mock_cicd_config,
            mock_time_service,
            mock_sql_repository,
        )

        # Act
        result = provider.restore_source_backup(cloud_sql_instance_with_ip)

        # Assert
        assert result == mock_backup
        mock_cloud_config.source_restore_instance.restore_backup.assert_called_once_with(
            cloud_sql_instance_with_ip, mock_cloud_config.wait_for_today_backup
        )

    @patch("common.pipeline_logging.pipeline_logger.PipelineExecutionLogger")
    def test_prepare_target_instance_local_returns_none(
        self,
        mock_pipeline_logger: Mock,
        env_config_local: Config,
        mock_local_config: Mock,
        mock_cloud_config: Mock,
        mock_cicd_config: Mock,
        mock_time_service: Mock,
        mock_sql_repository: Mock,
    ) -> None:
        """Test prepare_target_instance returns None in local environment."""
        # Arrange
        mock_logger_instance = Mock()
        mock_pipeline_logger.return_value = mock_logger_instance
        provider = create_instance_provider(
            env_config_local,
            mock_local_config,
            mock_cloud_config,
            mock_cicd_config,
            mock_time_service,
            mock_sql_repository,
        )

        # Act
        result = provider.prepare_target_instance()

        # Assert
        assert result is None

    @patch("common.pipeline_logging.pipeline_logger.PipelineExecutionLogger")
    def test_prepare_target_instance_production_describes_instance(
        self,
        mock_pipeline_logger: Mock,
        env_config_production: Config,
        mock_local_config: Mock,
        mock_cloud_config: Mock,
        mock_cicd_config: Mock,
        mock_time_service: Mock,
        mock_sql_repository: Mock,
        cloud_sql_instance_with_ip: CloudSQLInstance,
    ) -> None:
        """Test prepare_target_instance describes existing instance in production."""
        # Arrange
        mock_logger_instance = Mock()
        mock_pipeline_logger.return_value = mock_logger_instance
        mock_cloud_config.target_restore_instance.describe_instance.return_value = cloud_sql_instance_with_ip

        provider = create_instance_provider(
            env_config_production,
            mock_local_config,
            mock_cloud_config,
            mock_cicd_config,
            mock_time_service,
            mock_sql_repository,
        )

        # Act
        result = provider.prepare_target_instance()

        # Assert
        assert result == cloud_sql_instance_with_ip
        mock_cloud_config.target_restore_instance.describe_instance.assert_called_once_with(
            mock_cloud_config.target_db_config.instance_name
        )

    @patch.dict("os.environ", {"CICD": "True"}, clear=False)
    @patch("common.pipeline_logging.pipeline_logger.PipelineExecutionLogger")
    def test_prepare_target_instance_cicd_describes_instance(
        self,
        mock_pipeline_logger: Mock,
        env_config_staging: Config,
        mock_local_config: Mock,
        mock_cloud_config: Mock,
        mock_cicd_config: Mock,
        mock_time_service: Mock,
        mock_sql_repository: Mock,
        cloud_sql_instance_with_ip: CloudSQLInstance,
    ) -> None:
        """Test prepare_target_instance describes existing instance in CICD environment."""
        # Arrange
        mock_logger_instance = Mock()
        mock_pipeline_logger.return_value = mock_logger_instance
        mock_cicd_config.target_restore_instance.describe_instance.return_value = cloud_sql_instance_with_ip

        provider = create_instance_provider(
            env_config_staging,
            mock_local_config,
            mock_cloud_config,
            mock_cicd_config,
            mock_time_service,
            mock_sql_repository,
        )

        # Act
        result = provider.prepare_target_instance()

        # Assert
        assert result == cloud_sql_instance_with_ip
        mock_cicd_config.target_restore_instance.describe_instance.assert_called_once_with(
            mock_cicd_config.target_db_config.instance_name
        )

    @patch("common.pipeline_logging.pipeline_logger.PipelineExecutionLogger")
    def test_prepare_target_instance_staging_creates_instance(
        self,
        mock_pipeline_logger: Mock,
        env_config_staging: Config,
        mock_local_config: Mock,
        mock_cloud_config: Mock,
        mock_cicd_config: Mock,
        mock_time_service: Mock,
        mock_sql_repository: Mock,
        cloud_sql_instance_with_ip: CloudSQLInstance,
    ) -> None:
        """Test prepare_target_instance creates new instance in staging environment."""
        # Arrange
        mock_logger_instance = Mock()
        mock_pipeline_logger.return_value = mock_logger_instance
        mock_cloud_config.target_restore_instance.recreate_instance.return_value = cloud_sql_instance_with_ip

        provider = create_instance_provider(
            env_config_staging,
            mock_local_config,
            mock_cloud_config,
            mock_cicd_config,
            mock_time_service,
            mock_sql_repository,
        )

        # Act
        result = provider.prepare_target_instance()

        # Assert
        assert result == cloud_sql_instance_with_ip
        mock_cloud_config.target_restore_instance.recreate_instance.assert_called_once()

    @patch("common.pipeline_logging.pipeline_logger.PipelineExecutionLogger")
    def test_restore_target_backup_non_staging_returns_none(
        self,
        mock_pipeline_logger: Mock,
        env_config_production: Config,
        mock_local_config: Mock,
        mock_cloud_config: Mock,
        mock_cicd_config: Mock,
        mock_time_service: Mock,
        mock_sql_repository: Mock,
        cloud_sql_instance_with_ip: CloudSQLInstance,
    ) -> None:
        """Test restore_target_backup returns None in non-staging environment."""
        # Arrange
        mock_logger_instance = Mock()
        mock_pipeline_logger.return_value = mock_logger_instance
        provider = create_instance_provider(
            env_config_production,
            mock_local_config,
            mock_cloud_config,
            mock_cicd_config,
            mock_time_service,
            mock_sql_repository,
        )

        # Act
        result = provider.restore_target_backup(cloud_sql_instance_with_ip)

        # Assert
        assert result is None

    @patch("common.pipeline_logging.pipeline_logger.PipelineExecutionLogger")
    def test_restore_target_backup_staging_restores_backup(
        self,
        mock_pipeline_logger: Mock,
        env_config_staging: Config,
        mock_local_config: Mock,
        mock_cloud_config: Mock,
        mock_cicd_config: Mock,
        mock_time_service: Mock,
        mock_sql_repository: Mock,
        cloud_sql_instance_with_ip: CloudSQLInstance,
        mock_backup: Backup,
    ) -> None:
        """Test restore_target_backup restores backup in staging environment."""
        # Arrange
        mock_logger_instance = Mock()
        mock_pipeline_logger.return_value = mock_logger_instance
        mock_cloud_config.target_restore_instance.restore_backup.return_value = mock_backup

        provider = create_instance_provider(
            env_config_staging,
            mock_local_config,
            mock_cloud_config,
            mock_cicd_config,
            mock_time_service,
            mock_sql_repository,
        )

        # Act
        result = provider.restore_target_backup(cloud_sql_instance_with_ip)

        # Assert
        assert result == mock_backup
        mock_cloud_config.target_restore_instance.restore_backup.assert_called_once_with(
            cloud_sql_instance_with_ip, mock_cloud_config.wait_for_today_backup
        )

    def test_update_db_config_none_instances_returns_original(self, db_refresh_config: DbRefreshConfig) -> None:
        """Test update_db_config returns original config when instances are None."""
        # Act
        result = InstanceProvider.update_db_config(db_refresh_config, None, None)

        # Assert
        assert result == db_refresh_config

    def test_update_db_config_source_no_private_ip_raises_error(
        self,
        db_refresh_config: DbRefreshConfig,
        cloud_sql_instance_without_ip: CloudSQLInstance,
        cloud_sql_instance_with_ip: CloudSQLInstance,
    ) -> None:
        """Test update_db_config raises error when source instance has no private IP."""
        # Act & Assert
        with pytest.raises(
            ProviderError, match="Source Cloud SQL 'test-instance-no-ip' instance does not expose private IP address!"
        ):
            InstanceProvider.update_db_config(
                db_refresh_config, cloud_sql_instance_without_ip, cloud_sql_instance_with_ip
            )

    def test_update_db_config_target_no_private_ip_raises_error(
        self,
        db_refresh_config: DbRefreshConfig,
        cloud_sql_instance_with_ip: CloudSQLInstance,
        cloud_sql_instance_without_ip: CloudSQLInstance,
    ) -> None:
        """Test update_db_config raises error when target instance has no private IP."""
        # Act & Assert
        with pytest.raises(
            ProviderError, match="Target Cloud SQL 'test-instance-no-ip' instance does not expose private IP address!"
        ):
            InstanceProvider.update_db_config(
                db_refresh_config, cloud_sql_instance_with_ip, cloud_sql_instance_without_ip
            )

    def test_update_db_config_success_updates_hosts(
        self, db_refresh_config: DbRefreshConfig, cloud_sql_instance_with_ip: CloudSQLInstance
    ) -> None:
        """Test update_db_config successfully updates host addresses with private IPs."""
        # Arrange
        source_instance = cloud_sql_instance_with_ip
        target_instance = CloudSQLInstance(
            name="target-instance",
            connection_name="target-connection",
            database_version="POSTGRES_15",
            settings=Mock(),
            ip_addresses=[IpAddress(ip_address="********", type="PRIVATE")],
        )

        # Act
        result = InstanceProvider.update_db_config(db_refresh_config, source_instance, target_instance)

        # Assert
        assert result != db_refresh_config  # Should be a new instance
        assert result.source_db_config.host == "********"  # Source private IP
        assert result.target_db_config.host == "********"  # Target private IP
        # Other fields should remain the same
        assert result.pg_params == db_refresh_config.pg_params
        assert result.flyway_params == db_refresh_config.flyway_params
        assert result.alerting_config == db_refresh_config.alerting_config


class TestProviderConfig:
    """Test class for ProviderConfig data class."""

    def test_source_instance_name_with_instance(self, cloud_sql_instance_with_ip: CloudSQLInstance) -> None:
        """Test source_instance_name returns instance name when instance exists."""
        # Arrange
        config = ProviderConfig(
            restore_source_instance=Mock(),
            refresh_config=Mock(),
            source_instance=cloud_sql_instance_with_ip,
            target_instance=None,
        )

        # Act & Assert
        assert config.source_instance_name == "test-instance"

    def test_source_instance_name_without_instance(self) -> None:
        """Test source_instance_name returns None when instance is None."""
        # Arrange
        config = ProviderConfig(
            restore_source_instance=Mock(),
            refresh_config=Mock(),
            source_instance=None,
            target_instance=None,
        )

        # Act & Assert
        assert config.source_instance_name is None
