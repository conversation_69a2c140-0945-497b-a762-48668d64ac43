from unittest.mock import Mock

from provider import InstanceProvider

from common.config import Config
from common.time_service import TimeService


def create_instance_provider(
    env_config: Config,
    local_config: Mock,
    cloud_config: Mock,
    cicd_config: Mock,
    time_service: TimeService,
    sql_repository: Mock,
) -> InstanceProvider:
    """Helper test method to create InstanceProvider instance."""
    return InstanceProvider(
        env_config=env_config,
        local_config=local_config,
        cloud_config=cloud_config,
        cicd_config=cicd_config,
        time_service=time_service,
        sql_repository=sql_repository,
    )
