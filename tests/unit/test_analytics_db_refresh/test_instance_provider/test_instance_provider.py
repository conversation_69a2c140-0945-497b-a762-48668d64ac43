from unittest.mock import <PERSON><PERSON><PERSON>, Mock, patch

import pytest
from provider import Instance<PERSON><PERSON>ider, ProviderError
from refresh_shared.model import DbRefreshConfig
from unit.test_analytics_db_refresh.test_instance_provider.utils import (
    create_instance_provider,
)

from common.cloud_sql_instance_client.instance_model import (
    Backup,
    CloudSQLInstance,
    IpAddress,
)
from common.config import Config
from common.pipeline_logging.pipeline_logger import PipelineExecutionLogger
from common.time_service import TimeService


@patch.object(PipelineExecutionLogger, "start_processing")
def test_create_local_environment(
    mock_pipeline_logger: Mock,
    env_config_local: Config,
    mock_local_config: Mock,
    mock_cloud_config: Mock,
    mock_cicd_config: Mock,
    mock_sql_repository: Mock,
    db_refresh_config: DbRefreshConfig,
    time_service: TimeService,
) -> None:
    # Arrange
    mock_pipeline_logger.select_changes.return_value = MagicMock().__enter__.return_value = iter(())

    provider = create_instance_provider(
        env_config_local,
        mock_local_config,
        mock_cloud_config,
        mock_cicd_config,
        time_service,
        mock_sql_repository,
    )

    # Act
    result = provider.create()

    # Assert
    assert result.source_instance is None
    assert result.target_instance is None
    assert result.refresh_config == db_refresh_config


@patch.object(PipelineExecutionLogger, "start_processing")
@patch.dict("os.environ", {"CICD": "True"})
def test_create_cicd_environment(
    mock_pipeline_logger: Mock,
    env_config_staging: Config,
    mock_local_config: Mock,
    mock_cloud_config: Mock,
    mock_cicd_config: Mock,
    time_service: Mock,
    mock_sql_repository: Mock,
    cloud_sql_instance_with_ip: CloudSQLInstance,
    mock_backup: Backup,
) -> None:
    # Arrange
    mock_pipeline_logger.select_changes.return_value = MagicMock().__enter__.return_value = iter(())
    mock_cicd_config.source_restore_instance.recreate_instance.return_value = cloud_sql_instance_with_ip
    mock_cicd_config.source_restore_instance.restore_backup.return_value = mock_backup
    mock_cicd_config.target_restore_instance.describe_instance.return_value = cloud_sql_instance_with_ip

    provider = create_instance_provider(
        env_config_staging,
        mock_local_config,
        mock_cloud_config,
        mock_cicd_config,
        time_service,
        mock_sql_repository,
    )

    # Act
    result = provider.create()

    # Assert
    assert result.source_instance == cloud_sql_instance_with_ip
    assert result.target_instance == cloud_sql_instance_with_ip
    mock_cicd_config.source_restore_instance.recreate_instance.assert_called_once()
    mock_cicd_config.source_restore_instance.restore_backup.assert_called_once()
    mock_cicd_config.target_restore_instance.describe_instance.assert_called_once()
    mock_cloud_config.target_restore_instance.recreate_instance.assert_not_called()


@patch.object(PipelineExecutionLogger, "start_processing")
def test_create_staging_environment(
    mock_pipeline_logger: Mock,
    env_config_staging: Config,
    mock_local_config: Mock,
    mock_cloud_config: Mock,
    mock_cicd_config: Mock,
    time_service: Mock,
    mock_sql_repository: Mock,
    cloud_sql_instance_with_ip: CloudSQLInstance,
    mock_backup: Backup,
) -> None:
    # Arrange
    mock_pipeline_logger.select_changes.return_value = MagicMock().__enter__.return_value = iter(())

    mock_cloud_config.source_restore_instance.recreate_instance.return_value = cloud_sql_instance_with_ip
    mock_cloud_config.source_restore_instance.restore_backup.return_value = mock_backup
    mock_cloud_config.target_restore_instance.recreate_instance.return_value = cloud_sql_instance_with_ip
    mock_cloud_config.target_restore_instance.restore_backup.return_value = mock_backup

    provider = create_instance_provider(
        env_config_staging,
        mock_local_config,
        mock_cloud_config,
        mock_cicd_config,
        time_service,
        mock_sql_repository,
    )

    # Act
    result = provider.create()

    # Assert
    assert result.source_instance == cloud_sql_instance_with_ip
    assert result.target_instance == cloud_sql_instance_with_ip
    mock_cloud_config.source_restore_instance.recreate_instance.assert_called_once()
    mock_cloud_config.source_restore_instance.restore_backup.assert_called_once()
    mock_cloud_config.target_restore_instance.recreate_instance.assert_called_once()
    mock_cloud_config.target_restore_instance.restore_backup.assert_called_once()


@patch.object(PipelineExecutionLogger, "start_processing")
def test_create_production_environment(
    mock_pipeline_logger: Mock,
    env_config_production: Config,
    mock_local_config: Mock,
    mock_cloud_config: Mock,
    mock_cicd_config: Mock,
    time_service: Mock,
    mock_sql_repository: Mock,
    cloud_sql_instance_with_ip: CloudSQLInstance,
    mock_backup: Backup,
) -> None:
    # Arrange
    mock_pipeline_logger.select_changes.return_value = MagicMock().__enter__.return_value = iter(())

    mock_cloud_config.source_restore_instance.recreate_instance.return_value = cloud_sql_instance_with_ip
    mock_cloud_config.source_restore_instance.restore_backup.return_value = mock_backup
    mock_cloud_config.target_restore_instance.describe_instance.return_value = cloud_sql_instance_with_ip

    provider = create_instance_provider(
        env_config_production,
        mock_local_config,
        mock_cloud_config,
        mock_cicd_config,
        time_service,
        mock_sql_repository,
    )

    # Act
    result = provider.create()

    # Assert
    assert result.source_instance == cloud_sql_instance_with_ip
    assert result.target_instance == cloud_sql_instance_with_ip
    mock_cloud_config.source_restore_instance.recreate_instance.assert_called_once()
    mock_cloud_config.source_restore_instance.restore_backup.assert_called_once()
    mock_cloud_config.target_restore_instance.describe_instance.assert_called_once()
    mock_cloud_config.target_restore_instance.recreate_instance.assert_not_called()


@patch.object(PipelineExecutionLogger, "start_processing")
def test_create_source_instance_local_returns_none(
    mock_pipeline_logger: Mock,
    env_config_local: Config,
    mock_local_config: Mock,
    mock_cloud_config: Mock,
    mock_cicd_config: Mock,
    time_service: Mock,
    mock_sql_repository: Mock,
) -> None:
    # Arrange
    mock_pipeline_logger.select_changes.return_value = MagicMock().__enter__.return_value = iter(())

    provider = create_instance_provider(
        env_config_local,
        mock_local_config,
        mock_cloud_config,
        mock_cicd_config,
        time_service,
        mock_sql_repository,
    )

    # Act
    result = provider.create_source_instance()

    # Assert
    assert result is None
    mock_cloud_config.source_restore_instance.recreate_instance.assert_not_called()


@patch.object(PipelineExecutionLogger, "start_processing")
def test_create_source_instance_cloud_creates_instance(
    mock_pipeline_logger: Mock,
    env_config_production: Config,
    mock_local_config: Mock,
    mock_cloud_config: Mock,
    mock_cicd_config: Mock,
    mock_sql_repository: Mock,
    cloud_sql_instance_with_ip: CloudSQLInstance,
    time_service: TimeService,
) -> None:
    # Arrange
    mock_pipeline_logger.select_changes.return_value = MagicMock().__enter__.return_value = iter(())

    mock_cloud_config.source_restore_instance.recreate_instance.return_value = cloud_sql_instance_with_ip

    provider = create_instance_provider(
        env_config_production,
        mock_local_config,
        mock_cloud_config,
        mock_cicd_config,
        time_service,
        mock_sql_repository,
    )

    # Act
    result = provider.create_source_instance()

    # Assert
    assert result == cloud_sql_instance_with_ip
    mock_cloud_config.source_restore_instance.recreate_instance.assert_called_once()


@patch.object(PipelineExecutionLogger, "start_processing")
def test_restore_source_backup_local_returns_none(
    mock_pipeline_logger: Mock,
    env_config_local: Config,
    mock_local_config: Mock,
    mock_cloud_config: Mock,
    mock_cicd_config: Mock,
    mock_sql_repository: Mock,
    cloud_sql_instance_with_ip: CloudSQLInstance,
    time_service: TimeService,
) -> None:
    """Test restore_source_backup returns None in local environment."""
    # Arrange
    mock_pipeline_logger.select_changes.return_value = MagicMock().__enter__.return_value = iter(())
    provider = create_instance_provider(
        env_config_local,
        mock_local_config,
        mock_cloud_config,
        mock_cicd_config,
        time_service,
        mock_sql_repository,
    )

    # Act
    result = provider.restore_source_backup(cloud_sql_instance_with_ip)

    # Assert
    assert result is None
    mock_cloud_config.source_restore_instance.restore_backup.assert_not_called()


@patch.object(PipelineExecutionLogger, "start_processing")
def test_restore_source_backup_cloud_restores_backup(
    mock_pipeline_logger: Mock,
    env_config_production: Config,
    mock_local_config: Mock,
    mock_cloud_config: Mock,
    mock_cicd_config: Mock,
    mock_sql_repository: Mock,
    cloud_sql_instance_with_ip: CloudSQLInstance,
    mock_backup: Backup,
    time_service: TimeService,
) -> None:
    # Arrange
    mock_pipeline_logger.select_changes.return_value = MagicMock().__enter__.return_value = iter(())
    mock_cloud_config.source_restore_instance.restore_backup.return_value = mock_backup

    provider = create_instance_provider(
        env_config_production,
        mock_local_config,
        mock_cloud_config,
        mock_cicd_config,
        time_service,
        mock_sql_repository,
    )

    # Act
    result = provider.restore_source_backup(cloud_sql_instance_with_ip)

    # Assert
    assert result == mock_backup
    mock_cloud_config.source_restore_instance.restore_backup.assert_called_once_with(
        cloud_sql_instance_with_ip, mock_cloud_config.wait_for_today_backup
    )


@patch.object(PipelineExecutionLogger, "start_processing")
def test_prepare_target_instance_local_returns_none(
    mock_pipeline_logger: Mock,
    env_config_local: Config,
    mock_local_config: Mock,
    mock_cloud_config: Mock,
    mock_cicd_config: Mock,
    mock_sql_repository: Mock,
    time_service: TimeService,
) -> None:
    # Arrange
    mock_pipeline_logger.select_changes.return_value = MagicMock().__enter__.return_value = iter(())

    provider = create_instance_provider(
        env_config_local,
        mock_local_config,
        mock_cloud_config,
        mock_cicd_config,
        time_service,
        mock_sql_repository,
    )

    # Act
    result = provider.prepare_target_instance()

    # Assert
    assert result is None
    mock_cloud_config.target_restore_instance.restore_backup.assert_not_called()


@patch.object(PipelineExecutionLogger, "start_processing")
def test_prepare_target_instance_production_describes_instance(
    mock_pipeline_logger: Mock,
    env_config_production: Config,
    mock_local_config: Mock,
    mock_cloud_config: Mock,
    mock_cicd_config: Mock,
    mock_sql_repository: Mock,
    cloud_sql_instance_with_ip: CloudSQLInstance,
    time_service: TimeService,
) -> None:
    # Arrange
    mock_pipeline_logger.select_changes.return_value = MagicMock().__enter__.return_value = iter(())

    mock_cloud_config.target_restore_instance.describe_instance.return_value = cloud_sql_instance_with_ip

    provider = create_instance_provider(
        env_config_production,
        mock_local_config,
        mock_cloud_config,
        mock_cicd_config,
        time_service,
        mock_sql_repository,
    )

    # Act
    result = provider.prepare_target_instance()

    # Assert
    assert result == cloud_sql_instance_with_ip
    mock_cloud_config.target_restore_instance.describe_instance.assert_called_once_with(
        mock_cloud_config.target_db_config.instance_name
    )


@patch.object(PipelineExecutionLogger, "start_processing")
@patch.dict("os.environ", {"CICD": "True"})
def test_prepare_target_instance_cicd_describes_instance(
    mock_pipeline_logger: Mock,
    env_config_staging: Config,
    mock_local_config: Mock,
    mock_cloud_config: Mock,
    mock_cicd_config: Mock,
    time_service: Mock,
    mock_sql_repository: Mock,
    cloud_sql_instance_with_ip: CloudSQLInstance,
) -> None:
    # Arrange
    mock_pipeline_logger.select_changes.return_value = MagicMock().__enter__.return_value = iter(())
    mock_cicd_config.target_restore_instance.describe_instance.return_value = cloud_sql_instance_with_ip

    provider = create_instance_provider(
        env_config_staging,
        mock_local_config,
        mock_cloud_config,
        mock_cicd_config,
        time_service,
        mock_sql_repository,
    )

    # Act
    result = provider.prepare_target_instance()

    # Assert
    assert result == cloud_sql_instance_with_ip
    mock_cicd_config.target_restore_instance.describe_instance.assert_called_once_with(
        mock_cicd_config.target_db_config.instance_name
    )


@patch.object(PipelineExecutionLogger, "start_processing")
def test_prepare_target_instance_staging_creates_instance(
    mock_pipeline_logger: Mock,
    env_config_staging: Config,
    mock_local_config: Mock,
    mock_cloud_config: Mock,
    mock_cicd_config: Mock,
    mock_sql_repository: Mock,
    cloud_sql_instance_with_ip: CloudSQLInstance,
    time_service: TimeService,
) -> None:
    # Arrange
    mock_pipeline_logger.select_changes.return_value = MagicMock().__enter__.return_value = iter(())
    mock_cloud_config.target_restore_instance.recreate_instance.return_value = cloud_sql_instance_with_ip

    provider = create_instance_provider(
        env_config_staging,
        mock_local_config,
        mock_cloud_config,
        mock_cicd_config,
        time_service,
        mock_sql_repository,
    )

    # Act
    result = provider.prepare_target_instance()

    # Assert
    assert result == cloud_sql_instance_with_ip
    mock_cloud_config.target_restore_instance.recreate_instance.assert_called_once()


@patch.object(PipelineExecutionLogger, "start_processing")
def test_restore_target_backup_non_staging_returns_none(
    mock_pipeline_logger: Mock,
    env_config_production: Config,
    mock_local_config: Mock,
    mock_cloud_config: Mock,
    mock_cicd_config: Mock,
    mock_sql_repository: Mock,
    cloud_sql_instance_with_ip: CloudSQLInstance,
    time_service: TimeService,
) -> None:
    # Arrange
    mock_pipeline_logger.select_changes.return_value = MagicMock().__enter__.return_value = iter(())

    provider = create_instance_provider(
        env_config_production,
        mock_local_config,
        mock_cloud_config,
        mock_cicd_config,
        time_service,
        mock_sql_repository,
    )

    # Act
    result = provider.restore_target_backup(cloud_sql_instance_with_ip)

    # Assert
    assert result is None


@patch.object(PipelineExecutionLogger, "start_processing")
def test_restore_target_backup_staging_restores_backup(
    mock_pipeline_logger: Mock,
    env_config_staging: Config,
    mock_local_config: Mock,
    mock_cloud_config: Mock,
    mock_cicd_config: Mock,
    time_service: Mock,
    mock_sql_repository: Mock,
    cloud_sql_instance_with_ip: CloudSQLInstance,
    mock_backup: Backup,
) -> None:
    # Arrange
    mock_pipeline_logger.select_changes.return_value = MagicMock().__enter__.return_value = iter(())
    mock_cloud_config.target_restore_instance.restore_backup.return_value = mock_backup

    provider = create_instance_provider(
        env_config_staging,
        mock_local_config,
        mock_cloud_config,
        mock_cicd_config,
        time_service,
        mock_sql_repository,
    )

    # Act
    result = provider.restore_target_backup(cloud_sql_instance_with_ip)

    # Assert
    assert result == mock_backup
    mock_cloud_config.target_restore_instance.restore_backup.assert_called_once_with(
        cloud_sql_instance_with_ip, mock_cloud_config.wait_for_today_backup
    )


def test_update_db_config_none_instances_returns_original(db_refresh_config: DbRefreshConfig) -> None:
    # Act
    result = InstanceProvider.update_db_config(db_refresh_config, None, None)

    # Assert
    assert result == db_refresh_config


def test_update_db_config_source_no_private_ip_raises_error(
    db_refresh_config: DbRefreshConfig,
    cloud_sql_instance_without_ip: CloudSQLInstance,
    cloud_sql_instance_with_ip: CloudSQLInstance,
) -> None:
    """Test update_db_config raises error when source instance has no private IP."""
    # Act & Assert
    with pytest.raises(
        ProviderError, match="Source Cloud SQL 'test-instance-no-ip' instance does not expose private IP address!"
    ):
        InstanceProvider.update_db_config(db_refresh_config, cloud_sql_instance_without_ip, cloud_sql_instance_with_ip)


def test_update_db_config_target_no_private_ip_raises_error(
    db_refresh_config: DbRefreshConfig,
    cloud_sql_instance_with_ip: CloudSQLInstance,
    cloud_sql_instance_without_ip: CloudSQLInstance,
) -> None:
    # Act & Assert
    with pytest.raises(
        ProviderError, match="Target Cloud SQL 'test-instance-no-ip' instance does not expose private IP address!"
    ):
        InstanceProvider.update_db_config(db_refresh_config, cloud_sql_instance_with_ip, cloud_sql_instance_without_ip)


def test_update_db_config_success_updates_hosts(
    db_refresh_config: DbRefreshConfig, cloud_sql_instance_with_ip: CloudSQLInstance
) -> None:
    # Arrange
    source_instance = cloud_sql_instance_with_ip
    target_instance = CloudSQLInstance(
        name="target-instance",
        connection_name="target-connection",
        database_version="POSTGRES_15",
        settings=Mock(),
        ip_addresses=[IpAddress(ip_address="********", type="PRIVATE")],
    )

    # Act
    result = InstanceProvider.update_db_config(db_refresh_config, source_instance, target_instance)

    # Assert
    assert result != db_refresh_config  # Should be a new instance
    assert result.source_db_config.host == "********"  # Source private IP
    assert result.target_db_config.host == "********"  # Target private IP
    # Other fields should remain the same
    assert result.pg_params == db_refresh_config.pg_params
    assert result.flyway_params == db_refresh_config.flyway_params
    assert result.alerting_config == db_refresh_config.alerting_config
