from typing import Iterator

import pytest

from common.config import Config
from common.logger import setup_logger
from common.secret_client import get_secret
from common.sftp_client import SftpClient
from dhl.dhl_shared.shipping_config import DHL_SFTP_SECRET_ID

logger = setup_logger(Config())


@pytest.fixture(scope="session")
def demo_sftp_secret_id() -> str:
    return "demo-sftp-config"


@pytest.fixture(scope="session")
def demo_sftp_secret_value() -> str:
    return """{
    "host": "demo.wftpserver.com",
    "port": 2222,
    "user": "demo",
    "password": "demo"
}
"""


@pytest.fixture(scope="session")
def demo_ssh_secret(demo_sftp_secret_id: str, config: Config) -> str:
    """
    Demo server: https://www.wftpserver.com/onlinedemo.htm
    """
    return get_secret(demo_sftp_secret_id, config.project_id)


@pytest.fixture(scope="session")
def dhl_ssh_secret(config: Config) -> str:
    """
    DHL server config read from secret manager
    """
    return get_secret(DHL_SFTP_SECRET_ID, config.project_id)


@pytest.fixture(scope="session")
def demo_sftp_client(demo_ssh_secret: str) -> Iterator[SftpClient]:
    with SftpClient(demo_ssh_secret) as client:
        yield client


@pytest.fixture(scope="session")
def dhl_sftp_client(dhl_ssh_secret: str) -> Iterator[SftpClient]:
    with SftpClient(dhl_ssh_secret) as client:
        yield client
