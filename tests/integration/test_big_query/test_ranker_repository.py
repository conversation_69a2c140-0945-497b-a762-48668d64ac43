from datetime import date

import pytest
from ranker_repository import RankerBQRepository


@pytest.fixture(scope="session")
def event_date() -> date:
    return date(2025, 5, 5)


def test_load_procedures(ranker_repository: RankerBQRepository, event_date: date) -> None:
    total_bytes_processed = ranker_repository.load_category_view_item_clicks(event_date)
    assert total_bytes_processed > 0

    total_bytes_processed = ranker_repository.load_category_page_impression_and_clicks(event_date)
    assert total_bytes_processed > 0

    total_bytes_processed = ranker_repository.load_category_view_item_clicks_purchases(event_date)
    assert total_bytes_processed > 0
