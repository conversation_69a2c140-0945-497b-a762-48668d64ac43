from datetime import datetime

import pytest
from _pytest.logging import LogCaptureFixture
from export_shared.analytics_bq_repository import AnalyticsBQRepository
from export_shared.export_model import ExportStatus, PartitionValues
from export_shared.schema_mapping import PRODUCTS_TARGET
from google.api_core.exceptions import BadRequest

from common.bq_repository import BQTable


@pytest.fixture(scope="session")
def partition_values(platform_export_timestamp: int) -> PartitionValues:
    dt = datetime.fromtimestamp(platform_export_timestamp)
    return PartitionValues(year=dt.year, month=dt.month, day=dt.day, timestamp=platform_export_timestamp)


def test_get_export_status_when_invalid_table(analytics_repository: AnalyticsBQRepository) -> None:
    # act & assert
    with pytest.raises(BadRequest):
        _ = analytics_repository.get_export_status("invalid_table")


def test_get_export_status_when_valid_table(analytics_repository: AnalyticsBQRepository) -> None:
    # arrange
    expected = ExportStatus()

    # act
    actual = analytics_repository.get_export_status(PRODUCTS_TARGET)

    # assert
    assert actual.last_id >= expected.last_id
    assert actual.last_modified >= expected.last_modified


def test_load_when_invalid_table(
    partition_values: PartitionValues, analytics_repository: AnalyticsBQRepository
) -> None:
    # arrange
    table = "invalid"

    # act & assert
    with pytest.raises(BadRequest, match="Procedure is not found"):
        _ = analytics_repository.load_from_raw(table_name=table, pv=partition_values)


def test_load_when_valid_table(partition_values: PartitionValues, analytics_repository: AnalyticsBQRepository) -> None:
    # arrange: call analytics_transformed.load_products (2025, 1, 21, 1737460800);
    table = PRODUCTS_TARGET
    bq_table = BQTable(dataset=analytics_repository.DATASET, table=table)
    analytics_repository.truncate_table(bq_table)

    # act
    bytes_processed = analytics_repository.load_from_raw(table_name=table, pv=partition_values)
    qa_passed = analytics_repository.check_table(table_name=table)

    # assert
    assert bytes_processed > 0
    assert qa_passed


def test_check_table_when_error(analytics_repository: AnalyticsBQRepository, caplog: LogCaptureFixture) -> None:
    # arrange
    caplog.set_level("WARNING")
    table_name = "error"
    expected_warning = f"Quality check failed for {table_name=}"

    # act
    result = analytics_repository.check_table(table_name=table_name)

    # assert only 1 warning was produced
    assert not result
    assert len(caplog.messages) == 1
    assert expected_warning in caplog.messages[0]
