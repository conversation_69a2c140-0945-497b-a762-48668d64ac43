from datetime import datetime, timedelta, timezone
from typing import Iterator

import pytest
from bm_shared.api import AlgoliaParsedResponse
from bm_shared.bm_model import BMApiConfig
from compaction_bq_repo import CompactionBqRepository
from compaction_model import AlgoliaProduct, AlgoliaProductRange, CompactionConfig
from google.cloud.storage import Bucket
from integration.test_big_query.test_backmarket.api_worker_repository import (
    BackmarketBqRepository,
)
from integration.test_utils import delete_test_files

from common.bq_client import BigQueryClient


@pytest.fixture(scope="session")
def bm_api_config() -> BMApiConfig:
    return BMApiConfig()


@pytest.fixture(scope="session")
def bm_compaction_config() -> CompactionConfig:
    return CompactionConfig()


@pytest.fixture(scope="session")
def bm_bq_repository() -> BackmarketBqRepository:
    """This is a repository for test purposes only"""

    return BackmarketBqRepository()


@pytest.fixture(scope="session")
def bm_run_id() -> datetime:
    return datetime(2020, 1, 10, 10, tzinfo=timezone.utc)


@pytest.fixture(scope="session")
def bm_parsed_response() -> AlgoliaParsedResponse:
    return AlgoliaParsedResponse.from_json(
        """{"nbHits": 405,
            "hits": [
                {
                    "backmarketID": "413846",
                    "id": "3c52ddef-7498-4cc2-9abe-0a7bf225a58b",
                    "title_model": "iPhone 12 Pro Max",
                    "title": "iPhone 12 Pro Max 256GB - Pazifikblau - Ohne Vertrag",
                    "sub_title_elements": [
                        "256 GB",
                        "Pazifikblau",
                        "Ohne Vertrag"
                    ],
                    "backbox_grade_label": "Gut",
                    "link_grade_v2": {
                        "href": "https://www.backmarket.at/de-at/p/\
                            iphone-12-pro-max-256-gb-pazifikblau-ohne-vertrag/3c52ddef-7498-4cc2-9abe-0a7bf225a58b#l=12",
                        "hash": {
                            "l": "12",
                            "offerType": null
                        }
                    },
                    "price": 679.0,
                    "currency": "EUR"
                }
            ]}"""
    )


@pytest.fixture(scope="session")
def test_compaction_date() -> datetime:
    return datetime(2099, 12, 31, tzinfo=timezone.utc)


@pytest.fixture(scope="session")
def test_compaction_path(test_compaction_date: datetime) -> str:
    return (
        f"backmarket/products/year={test_compaction_date.year}/"
        f"month={test_compaction_date.month}/day={test_compaction_date.day}"
    )


@pytest.fixture(scope="session")
def compaction_repository(
    bq_client: BigQueryClient, transformed_data_lake_bucket: Bucket, test_compaction_path: str
) -> Iterator[CompactionBqRepository]:
    yield CompactionBqRepository(bq_client)

    delete_test_files(transformed_data_lake_bucket, test_compaction_path)


@pytest.fixture(scope="session")
def algolia_test_products(test_compaction_date: datetime) -> list[AlgoliaProduct]:
    return [
        # Start record - should be included in the final result with scrapped_at as a start_date
        AlgoliaProduct(
            product_name="Some Product",
            instance_features="Test Features",
            price=100.0,
            grade="A",
            url="https://example.com/some-product",
            country="DE",
            scrapped_at=test_compaction_date + timedelta(hours=12),
            extra_features="Extra Features",
            title="Test Title",
            id1="id1",
            id2="id2",
            currency="EUR",
        ),
        # Duplicate record - should be ignored
        AlgoliaProduct(
            product_name="Some Product",
            instance_features="Test Features",
            price=100.0,
            grade="A",
            url="https://example.com/some-product",
            country="DE",
            scrapped_at=test_compaction_date + timedelta(hours=12),
            extra_features="Extra Features",
            title="Test Title",
            id1="id1",
            id2="id2",
            currency="EUR",
        ),
        # New Price record and next hour, change from 100.0 to 200.0 - should be included in the final result
        AlgoliaProduct(
            product_name="Some Product",
            instance_features="Test Features Changed",
            price=200.0,
            grade="A",
            url="https://example.com/some-product",
            country="DE",
            scrapped_at=test_compaction_date + timedelta(hours=13),
            extra_features="Extra Features",
            title="Test Title",
            id1="id1",
            id2="id2",
            currency="EUR",
        ),
        # New Grade record and next hour, change from A to B,
        # but premise the same record with different title - should not be included in the final result
        AlgoliaProduct(
            product_name="Some Product",
            instance_features="Test Features Changed again",
            price=200.0,
            grade="B",
            url="https://example.com/some-product",
            country="DE",
            scrapped_at=test_compaction_date + timedelta(hours=16),
            extra_features="Extra Features",
            title="Test Title changed",
            id1="id1",
            id2="id2",
            currency="EUR",
        ),
        # The same Grade record and next hour - should be included in the final result
        AlgoliaProduct(
            product_name="Some Product",
            instance_features="Test Features Changed again",
            price=200.0,
            grade="B",
            url="https://example.com/some-product",
            country="DE",
            scrapped_at=test_compaction_date + timedelta(hours=17),
            extra_features="Extra Features",
            title="Test title with same grade",
            id1="id1",
            id2="id2",
            currency="EUR",
        ),
        # Another product - should be added as a new record with scrapped_at as start_date
        AlgoliaProduct(
            product_name="Another Product",
            instance_features="Test Features",
            price=999.0,
            grade="X",
            url="https://example.com/another-product",
            country="DE",
            scrapped_at=test_compaction_date + timedelta(hours=17),
            extra_features="Extra Features",
            title="Test Title",
            id1="id1",
            id2="id2",
            currency="EUR",
        ),
        # Another product, data does change after 1 hour
        # should be added to an existing record with scrapped_at as end_date
        # All attributes from this product should be used
        AlgoliaProduct(
            product_name="Another Product",
            instance_features="Test Features",
            price=999.0,
            grade="X",
            url="https://example.com/another-product",
            country="DE",
            scrapped_at=test_compaction_date + timedelta(hours=18),
            extra_features="Extra Features",
            title="Last Title",
            id1="id1",
            id2="id2",
            currency="EUR",
        ),
        # Another product - should be added as a new record with scrapped_at as start_date
        # end_date should be calculated by adding 1 hour to the scrapped_at minus 1 millisecond.
        AlgoliaProduct(
            product_name="Yet another Product",
            instance_features="Test Features",
            price=123.0,
            grade="X",
            url="https://example.com/yet-another-product",
            country="AT",
            scrapped_at=test_compaction_date + timedelta(hours=23),
            extra_features="Extra Features",
            title="Some Other Title",
            id1="id1",
            id2="id2",
            currency="EUR",
        ),
    ]


@pytest.fixture(scope="session")
def algolia_test_product_ranges(test_compaction_date: datetime) -> list[AlgoliaProductRange]:
    return [
        AlgoliaProductRange(
            product_name="Some Product",
            instance_features="Test Features",
            price=100.0,
            grade="A",
            url="https://example.com/some-product",
            country="DE",
            extra_features="Extra Features",
            title="Test Title",
            id1="id1",
            id2="id2",
            currency="EUR",
            start_date=test_compaction_date + timedelta(hours=12),
            end_date=test_compaction_date + timedelta(hours=13) - timedelta(milliseconds=1),
        ),
        AlgoliaProductRange(
            product_name="Some Product",
            instance_features="Test Features Changed",
            price=200.0,
            grade="A",
            url="https://example.com/some-product",
            country="DE",
            extra_features="Extra Features",
            title="Test Title",
            id1="id1",
            id2="id2",
            currency="EUR",
            start_date=test_compaction_date + timedelta(hours=13),
            end_date=test_compaction_date + timedelta(hours=16) - timedelta(milliseconds=1),
        ),
        AlgoliaProductRange(
            product_name="Some Product",
            instance_features="Test Features Changed again",
            price=200.0,
            grade="B",
            url="https://example.com/some-product",
            country="DE",
            extra_features="Extra Features",
            title="Test title with same grade",
            id1="id1",
            id2="id2",
            currency="EUR",
            start_date=test_compaction_date + timedelta(hours=16),
            end_date=test_compaction_date + timedelta(hours=18) - timedelta(milliseconds=1),
        ),
        AlgoliaProductRange(
            product_name="Another Product",
            instance_features="Test Features",
            price=999.0,
            grade="X",
            url="https://example.com/another-product",
            country="DE",
            extra_features="Extra Features",
            title="Last Title",
            id1="id1",
            id2="id2",
            currency="EUR",
            start_date=test_compaction_date + timedelta(hours=17),
            end_date=test_compaction_date + timedelta(hours=19) - timedelta(milliseconds=1),
        ),
        AlgoliaProductRange(
            product_name="Yet another Product",
            instance_features="Test Features",
            price=123.0,
            grade="X",
            url="https://example.com/yet-another-product",
            country="AT",
            extra_features="Extra Features",
            title="Some Other Title",
            id1="id1",
            id2="id2",
            currency="EUR",
            start_date=test_compaction_date + timedelta(hours=23),
            end_date=test_compaction_date + timedelta(hours=24) - timedelta(milliseconds=1),
        ),
    ]
