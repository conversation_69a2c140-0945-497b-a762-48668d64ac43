import time

from unit.test_alerting.conftest import <PERSON><PERSON><PERSON><PERSON><PERSON>AM<PERSON>

from common.cloud_logging_client.client import CloudLoggingClient
from common.cloud_logging_client.model import CloudRunJ<PERSON><PERSON>ogFilter
from common.cloud_run_client.job_client import CloudRunJobClient
from common.cloud_run_client.model import CloudRunJ<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>


def test_job_logs_all_severity_levels(
    cloud_logging_client: CloudLoggingClient, cloud_run_job_client: CloudRunJobClient
) -> None:
    # Arrange
    expected_severities = ["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]
    logs_by_severity = {}

    # Act
    # Trigger the job and wait for completion
    run_request = CloudRunJobRunRequest(environment_variables={"LOG_LEVEL": "DEBUG"})
    job_execution = cloud_run_job_client.run(JOB_NAME, run_request)
    # Allow some time for logs to be available
    time.sleep(10)

    for severity in expected_severities:
        log_filter = CloudRunJobLogFilter(
            severity=severity,
            job_name=JOB_NAME,
            job_execution_name=job_execution.short_name,
            max_results=10,
        )
        logs_by_severity[severity] = cloud_logging_client.list_log_entries(log_filter)

    # Assert
    for severity in expected_severities:
        logs = logs_by_severity[severity]
        assert logs, f"No {severity} logs found for the job '{job_execution} execution!"

        assert any(f"This is {severity} message" in str(entry.payload) for entry in logs)

        if severity == "INFO":
            assert 3 == len(
                [entry for entry in logs if "This is INFO message with password=*****" in str(entry.payload)]
            )

            assert any(
                "This is INFO message with serialized JSON: {'password': *****}" in str(entry.payload) for entry in logs
            )
            assert any(
                "This is INFO message with serialized JSON: {'rootPassword': *****, 'bar': 'baz'}" in str(entry.payload)
                for entry in logs
            )
