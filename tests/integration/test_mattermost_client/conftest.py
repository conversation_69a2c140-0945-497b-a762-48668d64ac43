import pytest

from common.config import Config
from common.mattermost_client import Mattermost<PERSON>lient
from common.secret_client import get_secret

MM_TEST_CHANNEL = "mattermost-test-channel"


@pytest.fixture(scope="session")
def webhook_url(config: Config) -> str:
    return get_secret(secret_id=MM_TEST_CHANNEL, project_id=config.project_id)


@pytest.fixture(scope="session")
def mattermost_client(webhook_url: str) -> MattermostClient:
    return MattermostClient(webhook_url)
