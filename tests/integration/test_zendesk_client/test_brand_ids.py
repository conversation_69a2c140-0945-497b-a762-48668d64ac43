import requests
from circuit_breakers_model import CircuitBreakersZendeskConfig
from requests.auth import HTTPBasic<PERSON>uth
from zendesk_client import ZendeskConfig

from common.secret_client import get_secret

zendesk_secret = get_secret(secret_id=ZendeskConfig.ZENDESK_SECRET, project_id=ZendeskConfig.project_id)
zendesk_config = ZendeskConfig.from_json(zendesk_secret)
circuit_breakers_zendesk_constants = CircuitBreakersZendeskConfig()


def test_get_zendesk_brand_ids() -> None:
    # ACT: get brand IDs
    url = f"https://{zendesk_config.subdomain}.zendesk.com/api/v2/brands"
    headers = {
        "Content-Type": "application/json",
    }
    email_address = zendesk_config.email
    api_token = zendesk_config.token

    # Use basic authentication
    auth = HTTPBasicAuth(f"{email_address}/token", api_token)
    response = requests.request("GET", url, auth=auth, headers=headers)

    # Parse the JSON response
    json_data = response.json()

    # Extract only id and name from each brand
    simplified_brands = []
    for brand in json_data.get("brands", []):
        brand_name = brand.get("name")
        brand_id = brand.get("id")

        simplified_brands.append({"id": brand_id, "name": brand_name})

        # ASSERT: let's assert the Supplier Performance Brand ID is what we expect
        if brand_name == "Supplier performance":
            brand_id = brand_id
            assert brand_id == circuit_breakers_zendesk_constants.brand_id_supplier_performance, (
                f"Supplier Performance should be {circuit_breakers_zendesk_constants.brand_id_supplier_performance} "
                f"for {zendesk_config.subdomain},"
                f" but it's {brand_id}"
            )
