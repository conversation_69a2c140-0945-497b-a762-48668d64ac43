import pytest
from zendesk_client import TicketPayload, ZendeskClient, ZendeskConfig

from common.secret_client import get_secret

# Some sandbox refurbedhelp1714995320 constants for testing
# Refurbed developers auto-reply user ID in Sandbox
REFURBED_DEVS_SANDBOX_ID = 21056766009501
REFURBED_DEVS_SANDBOX_EMAIL = "<EMAIL>"
# Brand ID
SANDBOX_REFURBED = 18669468996253
# Ticket form ID
INTERNAL_COLLABORATION_SANDBOX_ID = 18669530973085
# Field IDs for ticket properties
INTERNAL_TOPIC = "supplier_performance__internal_"
SUPPLIER_1_TEST = "supplier_1"


@pytest.fixture(scope="session")
def zendesk_config() -> ZendeskConfig:
    """Real config from staging secret manager for Zendesk sandbox"""
    zendesk_secret = get_secret(secret_id=ZendeskConfig.ZENDESK_SECRET, project_id=ZendeskConfig.project_id)
    return ZendeskConfig.from_json(zendesk_secret)


@pytest.fixture(scope="session")
def zendesk_client(zendesk_config: ZendeskConfig) -> ZendeskClient:
    """Sandbox Zendesk client"""
    return ZendeskClient(zendesk_config)


@pytest.fixture(params=["valid", "extra_valid"])
def valid_payload(
    request: pytest.FixtureRequest, valid_ticket_payload: TicketPayload, extra_valid_ticket_payload: TicketPayload
) -> TicketPayload:
    if request.param == "valid":
        return valid_ticket_payload
    else:
        return extra_valid_ticket_payload


@pytest.fixture(scope="session")
def extra_valid_ticket_payload() -> TicketPayload:
    """Fixture that provides a valid TicketPayload instance with extra SRP fields."""
    return TicketPayload(
        brand_id=SANDBOX_REFURBED,
        ticket_form_id=INTERNAL_COLLABORATION_SANDBOX_ID,
        requester_id=REFURBED_DEVS_SANDBOX_ID,
        submitter_id=REFURBED_DEVS_SANDBOX_ID,
        subject="Test Ticket",
        body="This is a test ticket.",
        primary_supplier_services_topic=INTERNAL_TOPIC,
        supplier_id=SUPPLIER_1_TEST,
        email_ccs=["<EMAIL>"],
    )


@pytest.fixture(scope="session")
def valid_ticket_payload() -> TicketPayload:
    """Fixture that provides a valid TicketPayload instance"""
    return TicketPayload(
        brand_id=SANDBOX_REFURBED,
        ticket_form_id=INTERNAL_COLLABORATION_SANDBOX_ID,
        requester_id=REFURBED_DEVS_SANDBOX_ID,
        submitter_id=REFURBED_DEVS_SANDBOX_ID,
        subject="Test Ticket",
        body="This is a test ticket.",
    )


@pytest.fixture(scope="session")
def missing_subject_ticket_payload() -> TicketPayload:
    """Fixture that provides a non-valid TicketPayload instance."""
    return TicketPayload(
        brand_id=SANDBOX_REFURBED,
        ticket_form_id=INTERNAL_COLLABORATION_SANDBOX_ID,
        requester_id=REFURBED_DEVS_SANDBOX_ID,
        submitter_id=REFURBED_DEVS_SANDBOX_ID,
        subject="",
        body="This is a test ticket.",
    )


@pytest.fixture(scope="session")
def missing_body_ticket_payload() -> TicketPayload:
    """Fixture that provides a non-valid TicketPayload instance."""
    return TicketPayload(
        brand_id=SANDBOX_REFURBED,
        ticket_form_id=INTERNAL_COLLABORATION_SANDBOX_ID,
        requester_id=REFURBED_DEVS_SANDBOX_ID,
        submitter_id=REFURBED_DEVS_SANDBOX_ID,
        subject="Test Ticket",
        body="",
    )


@pytest.fixture(scope="session")
def missing_brand_id_ticket_payload() -> TicketPayload:
    """Fixture that provides a non-valid TicketPayload instance."""
    return TicketPayload(
        brand_id=None,  # noqa
        ticket_form_id=INTERNAL_COLLABORATION_SANDBOX_ID,
        requester_id=REFURBED_DEVS_SANDBOX_ID,
        submitter_id=REFURBED_DEVS_SANDBOX_ID,
        subject="Test Ticket",
        body="This is a test ticket.",
    )


@pytest.fixture(scope="session")
def missing_ticket_form_id_ticket_payload() -> TicketPayload:
    """Fixture that provides a non-valid TicketPayload instance."""
    return TicketPayload(
        brand_id=SANDBOX_REFURBED,
        ticket_form_id=None,  # noqa
        requester_id=REFURBED_DEVS_SANDBOX_ID,
        submitter_id=REFURBED_DEVS_SANDBOX_ID,
        subject="Test Ticket",
        body="This is a test ticket.",
    )


@pytest.fixture(scope="session")
def missing_requester_id_ticket_payload() -> TicketPayload:
    """Fixture that provides a non-valid TicketPayload instance."""
    return TicketPayload(
        brand_id=SANDBOX_REFURBED,
        ticket_form_id=INTERNAL_COLLABORATION_SANDBOX_ID,
        requester_id=None,  # noqa
        submitter_id=REFURBED_DEVS_SANDBOX_ID,
        subject="Test Ticket",
        body="This is a test ticket.",
    )


@pytest.fixture(scope="session")
def missing_submitter_id_ticket_payload() -> TicketPayload:
    """Fixture that provides a non-valid TicketPayload instance."""
    return TicketPayload(
        brand_id=SANDBOX_REFURBED,
        ticket_form_id=INTERNAL_COLLABORATION_SANDBOX_ID,
        requester_id=REFURBED_DEVS_SANDBOX_ID,
        submitter_id=None,  # noqa
        subject="Test Ticket",
        body="This is a test ticket.",
    )


@pytest.fixture(scope="session")
def test_email() -> str:
    """Fixture that provides a valid email for testing find_user_id_via_email."""
    return REFURBED_DEVS_SANDBOX_EMAIL


@pytest.fixture(scope="session")
def non_existent_email() -> str:
    """Fixture that provides a non-existent email for testing find_user_id_via_email."""
    return "<EMAIL>"  # This email should not exist in the Zendesk sandbox
