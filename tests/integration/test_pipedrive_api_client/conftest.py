import pytest
from pipedrive_api_client import PipedriveApi<PERSON>lient, PipedriveApiConfig

from common.config import Config
from common.secret_client import get_secret
from pipedrive.pipedrive_model import DEFAULT_API_SECRET_NAME


@pytest.fixture(scope="session")
def pipedrive_api_token(config: Config) -> str:
    return get_secret(DEFAULT_API_SECRET_NAME, config.project_id)


@pytest.fixture(scope="session")
def pipedrive_api_config(pipedrive_api_token: str) -> PipedriveApiConfig:
    return PipedriveApiConfig(api_token=pipedrive_api_token)


@pytest.fixture(scope="session")
def pipedrive_api_client(pipedrive_api_config: PipedriveApiConfig) -> PipedriveApiClient:
    return PipedriveApiClient(config=pipedrive_api_config)
