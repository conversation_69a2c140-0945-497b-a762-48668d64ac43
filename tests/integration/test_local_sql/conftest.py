from os import environ
from pathlib import Path
from typing import Iterator

import pytest
from export_shared.schema_snapshot import ORDER_ITEM_OFFERS_SCHEMA
from pandas import DataFrame, read_csv
from psycopg import Connection
from pytest_postgresql import factories
from sqlalchemy import Engine

from common.sql_client import DatabaseConfig, get_database_engine
from common.sql_model import SqlTable
from common.sql_repository import SqlRepository
from tests.integration.sql_schema import create_user_table, drop_user_table

current_dir = Path(__file__).resolve().parent

host = environ.get("POSTGRES_HOST", "localhost")
port = environ.get("POSTGRES_PORT", 5433)
postgres_db = environ.get("POSTGRES_DB", "platform")
postgres_user = environ.get("POSTGRES_USER", "postgres")
password = environ.get("POSTGRES_PASSWORD", "postgres")

postgresql_proc = factories.postgresql_noproc(host=host, port=port, user=postgres_user, password=password)

# A function scoped connection is required (postgresql_proc does not work as test fixture!)
postgresql_mocked = factories.postgresql("postgresql_proc")


@pytest.fixture(scope="session")
def data_dir() -> Path:
    return current_dir / "data"


@pytest.fixture(scope="session")
def sql_dir() -> Path:
    return current_dir / "sql"


@pytest.fixture(scope="function")
def order_item_offers_table() -> SqlTable:
    return SqlTable(schema_name="public", table_name="order_item_offers")


@pytest.fixture(scope="function")
def order_item_offers(data_dir: Path) -> DataFrame:
    return read_csv(
        data_dir / "order_item_offers.csv",
        # Date parsing
        parse_dates=ORDER_ITEM_OFFERS_SCHEMA.date_columns,
        # Data type(s) mapping
        dtype=ORDER_ITEM_OFFERS_SCHEMA.data_types,
        # NaN values handling
        na_filter=False,
        keep_default_na=False,
        # Skip empty lines
        skip_blank_lines=True,
    )


@pytest.fixture(scope="function")
def order_item_exchange_rate(data_dir: Path) -> DataFrame:
    return read_csv(
        data_dir / "order_item_exchange_rate.csv",
        # NaN values handling
        na_filter=False,
        keep_default_na=False,
        # Skip empty lines
        skip_blank_lines=True,
    )


@pytest.fixture(scope="function")
def db_config(postgresql_mocked: Connection) -> Iterator[DatabaseConfig]:
    connection = postgresql_mocked
    assert connection.closed is False

    db_config = DatabaseConfig(
        host=connection.info.host,
        database=postgres_db,
        username=connection.info.user,
        password=connection.info.password,
        port=connection.info.port,
        application_name="test_local_sql",
    )
    yield db_config

    connection.close()


@pytest.fixture(scope="function")
def engine(db_config: DatabaseConfig) -> Engine:
    return get_database_engine(db_config=db_config)


@pytest.fixture(scope="function")
def sql_table(engine: Engine) -> Iterator[SqlTable]:
    table = SqlTable(schema_name="public", table_name="user")
    create_user_table(engine, table)

    yield table

    drop_user_table(engine, table)


@pytest.fixture(scope="function")
def sql_repository(db_config: DatabaseConfig) -> SqlRepository:
    return SqlRepository(db_config)
