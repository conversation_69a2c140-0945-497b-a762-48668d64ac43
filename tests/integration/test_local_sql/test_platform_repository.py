from datetime import timed<PERSON><PERSON>
from pathlib import Path
from typing import Iterator, Optional

import pytest
from export_shared.schema_incremental import PRODUCTS_SCHEMA
from export_shared.schema_snapshot import ORDER_ITEM_OFFERS_SCHEMA
from pandas import DataFrame, concat, read_csv
from pandas._testing import assert_frame_equal

from common.monitoring import profile
from common.sql_client import DatabaseConfig
from common.sql_model import SqlTable
from platform_export.export_shared.export_model import (
    CUT_OFF_DATETIME,
    ExportStatus,
    ExportType,
)
from platform_export.export_shared.platform_repository import PlatformRepository
from platform_export.export_shared.schema_mapping import PRODUCTS_SOURCE

CUT_OFF = CUT_OFF_DATETIME.isoformat()
ORDER_ITEM_OFFERS_VIEW = SqlTable(schema_name="export_etl", table_name="v_order_item_offers")
ORDER_ITEMS_OFFERS_COUNT = 10720  # Adjust to your local table count!


@pytest.fixture(scope="function")
def products_df(data_dir: Path) -> DataFrame:
    return read_csv(
        data_dir / "platform_products.csv",
        # Date parsing
        parse_dates=PRODUCTS_SCHEMA.date_columns,
        # Data type(s) mapping
        dtype=PRODUCTS_SCHEMA.data_types,
        # NaN values handling
        na_filter=False,
        keep_default_na=False,
        # Skip empty lines
        skip_blank_lines=True,
    ).astype(dtype=PRODUCTS_SCHEMA.dtypes)


@pytest.fixture(scope="function")
def repository(db_config: DatabaseConfig, products_df: DataFrame) -> Iterator[PlatformRepository]:
    repo = PlatformRepository(db_config)

    yield repo

    # clean-up after each test
    repo.truncate_table(PRODUCTS_SOURCE)


@pytest.mark.parametrize(
    "export_type, export_status, expected",
    [
        (ExportType.INCREMENTAL, ExportStatus(), ""),
        (ExportType.TIME_TRAVEL, ExportStatus(), ""),
        (ExportType.SNAPSHOT, ExportStatus(), ""),
        (
            ExportType.INCREMENTAL,
            ExportStatus(last_id=1),
            f"id > 1 or updated_at >= '{CUT_OFF}' or deleted_at >= '{CUT_OFF}'",
        ),
        (
            ExportType.TIME_TRAVEL,
            ExportStatus(last_id=2),
            f"id > 2 or valid_from >= '{CUT_OFF}' or valid_to >= '{CUT_OFF}'",
        ),
    ],
)
def test_build_change_condition(
    export_type: ExportType, export_status: ExportStatus, expected: Optional[str], repository: PlatformRepository
) -> None:
    result = repository.build_change_condition(export_type=export_type, export_status=export_status)
    assert result == expected


def select_products_changes(repository: PlatformRepository, export_status: ExportStatus) -> DataFrame:
    with repository.select_changes(table=PRODUCTS_SOURCE, schema=PRODUCTS_SCHEMA, export_status=export_status) as dfs:
        return concat(dfs, ignore_index=True)


def test_select_changes_when_no_source_data(repository: PlatformRepository, products_df: DataFrame) -> None:
    # arrange
    export_status = ExportStatus()

    # act
    actual_df = select_products_changes(repository, export_status)

    # assert
    assert actual_df.empty


def test_select_changes_when_no_changes_in_source(repository: PlatformRepository, products_df: DataFrame) -> None:
    # arrange
    last_id = products_df["id"].max()
    last_modified = (products_df["deleted_at"].max()).to_pydatetime() + timedelta(seconds=1)
    export_status = ExportStatus(last_id=last_id, last_modified=last_modified)

    repository.insert(df=products_df, table=PRODUCTS_SOURCE, truncate=True)

    # act
    actual_df = select_products_changes(repository, export_status)

    # assert
    assert actual_df.empty


def test_select_changes_when_full_load(repository: PlatformRepository, products_df: DataFrame) -> None:
    # arrange
    export_status = ExportStatus()
    repository.insert(df=products_df, table=PRODUCTS_SOURCE, truncate=True)

    # act
    actual_df = select_products_changes(repository, export_status).sort_values(by=["id"])

    # assert
    assert_frame_equal(actual_df, products_df)


@pytest.mark.parametrize(
    "date_column",
    [
        "created_at",
        "updated_at",
        "deleted_at",
    ],
)
def test_select_changes_when_created_updated_deleted(
    date_column: str, repository: PlatformRepository, products_df: DataFrame
) -> None:
    # arrange
    max_id = products_df["id"].max()
    last_id = max_id - 1 if date_column == "created_at" else max_id
    last_modified = (products_df[date_column].max()).to_pydatetime()
    export_status = ExportStatus(last_id=last_id, last_modified=last_modified)

    expected_df = products_df[products_df.id == max_id].reset_index(drop=True)
    repository.insert(df=products_df, table=PRODUCTS_SOURCE, truncate=True)

    # act
    actual_df = select_products_changes(repository, export_status)

    # assert
    assert_frame_equal(actual_df, expected_df)


@profile
def assert_select_changes_in_chunks(repository: PlatformRepository, chunk_size: int) -> None:
    """
    Profiles and asserts `select_changes` returns correct number of rows regardless of chunk size.
    """
    count = 0
    with repository.select_changes(
        table=ORDER_ITEM_OFFERS_VIEW,
        schema=ORDER_ITEM_OFFERS_SCHEMA,
        export_status=ExportStatus(),
        chunk_size=chunk_size,
    ) as dfs:
        for df in dfs:
            assert isinstance(df, DataFrame)
            assert len(df.columns) == len(ORDER_ITEM_OFFERS_SCHEMA.columns)
            count += len(df)

    assert count == ORDER_ITEMS_OFFERS_COUNT


def test_select_changes_when_one_chunk(repository: PlatformRepository) -> None:
    """
    Test with 2,000,000 dataset:
    1. consumed memory 8.0546875 MB(s) in 261.37s (0:04:21)
    2. consumed memory 22.18359375 MB(s) in 289.18s (0:04:49)
    3. consumed memory 19.625 MB(s) in 264.22s (0:04:24)
    """
    assert_select_changes_in_chunks(repository, ORDER_ITEMS_OFFERS_COUNT)


def test_select_changes_when_1000_chunk(repository: PlatformRepository) -> None:
    """
    # Test with 2,000,000 dataset:
    1. consumed memory 5.40625 MB(s) in 284.10s (0:04:44)
    2. consumed memory 5.60546875 MB(s) in 316.72s (0:05:16)

    # Test with 2997520 dataset:
    1. consumed memory 5.69140625 MB(s) in 479.76s (0:07:59)
    2. consumed memory 5.3984375 MB(s) in 451.55s (0:07:31)
    """
    assert_select_changes_in_chunks(repository, 1000)


@pytest.mark.skip(reason="Ad-hoc performance testing")
def test_select_changes_when_10000_chunk(repository: PlatformRepository) -> None:
    """
    # Test with 2,000,000 dataset:
    1. consumed memory 32.3671875 MB(s) in 285.02s (0:04:45)
    2. consumed memory 32.015625 MB(s) in 316.24s (0:05:16)

    # Test with 2997520 dataset:
    1. consumed memory 32.9453125 MB(s) in 392.14s (0:06:32)
    2. consumed memory 32.28515625 MB(s) in 439.19s (0:07:19)
    """
    assert_select_changes_in_chunks(repository, 10000)


@pytest.mark.skip(reason="Ad-hoc performance testing")
def test_select_changes_when_100000_chunk(repository: PlatformRepository) -> None:
    """
    # Test with 2,000,000 dataset:
    1. consumed memory 115.74609375 MB(s) in 254.74s (0:04:14)
    2. consumed memory 114.72265625 MB(s) in 290.50s (0:04:50)

    # Test with 2997520 dataset:
    1. consumed memory 120.92578125 MB(s) in 453.16s (0:07:33)
    2. consumed memory 128.46484375 MB(s) in 458.85s (0:07:38)
    """
    assert_select_changes_in_chunks(repository, 100000)


@pytest.mark.skip(reason="Ad-hoc performance testing")
def test_select_changes_when_1000000_chunk(repository: PlatformRepository) -> None:
    """
    # Test with 2,000,000 dataset:
    1. consumed memory 14.453125 MB(s) in 307.18s (0:05:07)
    2. consumed memory 13.33203125 MB(s) in 264.15s (0:04:24)

    # Test with 2997520 dataset:
    1. consumed memory 17.7734375 MB(s) in 454.70s (0:07:34)
    """
    assert_select_changes_in_chunks(repository, 1000000)
