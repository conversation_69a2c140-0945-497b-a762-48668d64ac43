from typing import Iterator

import pytest
from google.cloud.sql.connector import Connector

from common.config import Config
from common.consts import ANALYTICS_CLOUD_SQL_SECRET_ID
from common.secret_client import get_secret
from common.sql_client import DatabaseConfig
from common.sql_model import SqlTable
from common.sql_repository import SqlRepository
from tests.integration.sql_schema import create_user_table, drop_user_table


@pytest.fixture(scope="function")
def db_secret(config: Config) -> str:
    return get_secret(ANALYTICS_CLOUD_SQL_SECRET_ID, config.project_id)


@pytest.fixture(scope="function")
def db_config(db_secret: str) -> DatabaseConfig:
    return DatabaseConfig.from_json(db_secret)


@pytest.fixture(scope="function")
def sql_repository(db_config: DatabaseConfig) -> Iterator[SqlRepository]:
    with Connector() as connector:
        # https://stackoverflow.com/questions/41881731/is-it-safe-to-combine-with-and-yield-in-python
        yield SqlRepository(db_config, connector)


@pytest.fixture(scope="function")
def sql_table(sql_repository: SqlRepository) -> Iterator[SqlTable]:
    table = SqlTable(schema_name="public", table_name="user")
    create_user_table(sql_repository.engine, table)

    yield table

    drop_user_table(sql_repository.engine, table)
