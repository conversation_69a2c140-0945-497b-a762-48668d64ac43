import pytest
from export_shared.schema_mapping import SOURCE_TO_TARGET
from pytest import LogCaptureFixture, param

from platform_export.dispatcher.export_dispatcher import PlatformExportDispatcher
from platform_export.etl import main
from platform_export.export_shared.export_model import ExportConfig, SqlTableSize


@pytest.mark.parametrize(
    "sql_table",
    # `id` is used to name the test cases by the SQL table name
    [param(cs, id=cs.source.full_name) for cs in PlatformExportDispatcher.CONFIGURED_SOURCES],
)
def test_etl(sql_table: SqlTableSize, platform_export_timestamp: int, caplog: LogCaptureFixture) -> None:
    # arrange
    caplog.set_level("INFO")
    config = ExportConfig(timestamp=platform_export_timestamp, source=sql_table.source, table_size=sql_table.size)

    expected_extracted = f"'{sql_table.source.full_name}' extracted"
    target_table = SOURCE_TO_TARGET[sql_table.source].table_name
    expected_loaded = f"BigQuery table 'analytics_transformed.{target_table}' loaded"
    expected_qa = "qa_passed=True"

    # act
    main.run(config)

    # assert
    assert any(expected_extracted in s for s in caplog.messages)
    assert any(expected_loaded in s for s in caplog.messages)
    assert any(expected_qa in s for s in caplog.messages)
